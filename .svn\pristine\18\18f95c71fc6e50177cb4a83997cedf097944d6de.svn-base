<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/clDriverPic"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/w4dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.yaxon.view.YxImageView
        android:id="@+id/yivDriverPic"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:corner_radius="@dimen/w8dp"
        app:border_width="0dp"
        android:scaleType="fitXY"
        app:border_color="@color/gray"
        app:is_cover_src="true"
        android:background="@drawable/shape_pic_bg"/>

    <TextView
        android:id="@+id/tvNoPicTip"
        style="@style/Yx_Textview_White_18"
        android:textColor="@color/gray"
        app:layout_constraintLeft_toLeftOf="@id/yivDriverPic"
        app:layout_constraintTop_toTopOf="@id/yivDriverPic"
        app:layout_constraintRight_toRightOf="@id/yivDriverPic"
        app:layout_constraintBottom_toBottomOf="@id/yivDriverPic"
        android:text="@string/no_picture"
        android:visibility="gone"/>

    <com.yaxon.view.YxImageView
        android:id="@+id/ivNameBg"
        android:layout_width="0dp"
        android:layout_height="@dimen/h68dp"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="@id/yivDriverPic"
        app:layout_constraintEnd_toEndOf="@id/yivDriverPic"
        app:layout_constraintBottom_toBottomOf="@id/yivDriverPic"
        app:corner_bottom_left_radius="@dimen/w8dp"
        app:corner_bottom_right_radius="@dimen/w8dp"
        app:border_width="0dp"
        app:is_cover_src="true"
        android:visibility="gone"
        android:src="@drawable/log_driver_pic_name_bg"/>

    <TextView
        android:id="@+id/tvDriverName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@id/ivNameBg"
        app:layout_constraintRight_toRightOf="@id/ivNameBg"
        app:layout_constraintTop_toTopOf="@id/ivNameBg"
        app:layout_constraintBottom_toBottomOf="@id/ivNameBg"
        android:textColor="@color/black"
        android:textSize="@dimen/w20sp"
        android:visibility="gone"/>

</androidx.constraintlayout.widget.ConstraintLayout>