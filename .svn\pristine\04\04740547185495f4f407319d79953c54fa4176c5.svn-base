package com.yaxon.view.countdown;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.widget.TextView;

import java.util.Locale;


/**
 * <一句话功能简述> <功能详细描述>
 *
 * <AUTHOR> 倒计时
 * 创建日期：20-1-13 上午11:34
 * 描述：CountDownTextView.java
 *
 */
public class CountDownTextView extends TextView {
    public static final String TAG = "CountDownTextView";

    private TvCountDown mTimer;
    private OnCountDownListener mOnCountDownListener;


    public CountDownTextView(Context context,  AttributeSet attrs) {
        super(context, attrs);
    }


    private long timer;
    public void initTimer(long timer){
        this.timer = timer;
    }

    public void startTimer() {
        if (mTimer != null) {
            mTimer.cancel();
        }
        mTimer = new TvCountDown(timer, 1000);
        mTimer.start();
    }


    public void resetTimer(long timer){
        this.timer = timer;
        startTimer();
    }


    public void stopTimer(){
        if (mTimer != null) {
            mTimer.cancel();
        }
    }


    public void setOnCountDownListener(OnCountDownListener onCountDownListener) {
        this.mOnCountDownListener = onCountDownListener;
    }

    public interface OnCountDownListener {
        void onFinish();

    }

    class TvCountDown extends MyCountDownTimer {

        /**
         * @param millisInFuture    总时间
         * @param countDownInterval 周期
         */
        public TvCountDown(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(long millisUntilFinished) {
       //     Log.d("CountDownTv", (millisUntilFinished / 1000) + "秒");
            setText(generateCountdownText(millisUntilFinished));
            setAlpha(1f);
            setEnabled(false);

        }

        @Override
        public void onFinish() {
            if (mOnCountDownListener != null){
                mOnCountDownListener.onFinish();
            }
        }
    }


    private static final int HOUR = 3600000;
    private static final int MIN = 60000;
    private static final int SEC = 1000;
    static String generateCountdownText(long duration) {
        int hr = (int) (duration / HOUR);
        int min = (int) ((duration - (hr * HOUR)) / MIN);
        int sec = (int) ((duration - (hr * HOUR) - (min * MIN)) / SEC);

        Locale locale = Locale.getDefault();
        String format = "%02d";
        String formattedHr = String.format(locale, format, hr);
        String formattedMin = String.format(locale, format, min);
        String formattedSec = String.format(locale, format, sec);
        return String.format(locale, "%s : %s : %s", formattedHr, formattedMin, formattedSec);
    }
}
