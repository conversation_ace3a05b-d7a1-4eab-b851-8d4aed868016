package com.yaxon.yx_control.system.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.yaxon.yx_control.R;
import com.yaxon.yx_control.productesting.MeigeTestingStateControl;

import java.util.Map;

/**
 * <AUTHOR> wangzw
 * @date : 20-4-21上午9:33
 * @desc :
 */
public class MeigeProductTestAdapter extends RecyclerView.Adapter<MeigeProductTestAdapter.ViewHolder> {
    private int[] titles;
    private int[] subTitles;
    private Map<Integer, Integer> map;
    private Context context;


    public MeigeProductTestAdapter(Context context, int[] titles, int[] subTitles, Map<Integer, Integer> map) {
        this.titles = titles;
        this.subTitles = subTitles;
        this.map = map;
        this.context = context;

    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = View.inflate(context, R.layout.item_meige_product_test, null);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {
        final String title = context.getResources().getString(titles[position]);
        String subTitle = context.getResources().getString(subTitles[position]);
        int state = MeigeTestingStateControl.STATE_NULL;
        int keyType = position + 1;

        if (map.containsKey(keyType)){
            state = map.get(keyType);
        } else {
            Log.e("MeigeProductTestAdapter", "keyType :" + keyType + ", position :" + position);
        }

        holder.tvTitle.setText(title);
        holder.tvSubTitle.setText(subTitle);

        holder.tvState.setTextColor(state == MeigeTestingStateControl.STATE_NULL ?
                context.getResources().getColor(R.color.white) :
                state == MeigeTestingStateControl.STATE_NORMAL ?
                        context.getResources().getColor(R.color.green) :
                        context.getResources().getColor(R.color.red));

        String stateContent = "";

        switch (state) {
            case MeigeTestingStateControl.STATE_NULL:
                stateContent = context.getResources().getString(R.string.testing_common_nocheck);
                break;
            case MeigeTestingStateControl.STATE_ERROR:
                if (titles[position] == R.string.product_test_title_button) {
                    stateContent = context.getResources().getString(R.string.testing_common_error)+
                            MeigeTestingStateControl.BUTTON_TEST_STATE;
                }else {
                    stateContent = context.getResources().getString(R.string.testing_common_error);
                }
                break;
            case MeigeTestingStateControl.STATE_NORMAL:
                stateContent = context.getResources().getString(R.string.testing_common_normal);
                break;
        }


        holder.tvState.setText(stateContent);

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                if (itemClickListener != null) {
                    itemClickListener.onItemClick(position);
                }

            }
        });
    }

    @Override
    public int getItemCount() {
        return titles == null ? 0 : titles.length;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {

        private TextView tvTitle;
        private TextView tvSubTitle;
        private TextView tvState;

        public ViewHolder(View itemView) {
            super(itemView);
            tvTitle = (TextView) itemView.findViewById(R.id.tvTitle);
            tvSubTitle = (TextView) itemView.findViewById(R.id.tvSubTitle);
            tvState = (TextView) itemView.findViewById(R.id.tvState);
        }
    }

    public interface OnItemClickListener {
        void onItemClick(int position);
    }

    public OnItemClickListener itemClickListener;

    public void setItemClickListener(OnItemClickListener itemClickListener) {
        this.itemClickListener = itemClickListener;
    }
}
