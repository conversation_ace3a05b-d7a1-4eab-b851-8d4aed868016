package com.yaxon.telematics.service.protocol.takepic;

public class TakePicStatusCodes {

    public static final int ERROR_STATE = -1;

    public static final int CAMERA_OPENING_STATE = 0;
    public static final int CAMERA_OPEN_COMPLETED_STATE = 1;
    public static final int PREVIEW_DISPLAY__STATE = 2;
    public static final int TAKING_PIC_STATE = 3;
    public static final int ONE_PIC_TAKE_SUCCESS_STATE = 4;
    public static final int ONE_PIC_TAKE_FAILURE_STATE = 5;
    public static final int ALL_PIC_TAKE_COMPLETED_STATE = 6;
    public static final int TAKE_COMPLETED_BY_CENTER_STATE = 7;

}
