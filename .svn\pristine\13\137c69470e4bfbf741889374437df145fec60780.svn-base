package com.yaxon.business.menu.activity;

import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.widget.SeekBar;

import androidx.annotation.Nullable;

import com.yaxon.adapter.MenuAdapter;
import com.yaxon.base.R;
import com.yaxon.base.TitleBarActivity;
import com.yaxon.base.databinding.ActivityMenuBinding;
import com.yaxon.base.setup.HardwareActivity;
import com.yaxon.business.home.activity.HomeActivity;
import com.yaxon.business.presenter.DriverPresenter;
import com.yaxon.business.login.LogType;
import com.yaxon.business.menu.viewmodel.MenuViewModel;
import com.yaxon.business.message.activity.MessageActivity;
import com.yaxon.telematics.service.taxi.aidl.YxDriverInfo;
import com.yaxon.utils.AdjustBrightness;
import com.yaxon.utils.DateUtil;
import com.yaxon.utils.ViewUtils;

public class MenuActivity extends TitleBarActivity<ActivityMenuBinding,
        MenuViewModel> {

    private static final String TAG = MenuActivity.class.getSimpleName();
    private MenuAdapter menuAdapter = null;
    /**
     * 当前亮度
     */
    private int curBrightnessRank = 0;

    /**
     * 计算等级
     *
     * @param value 亮度值
     * @return
     */
    private static int computeRank(int value) {
        int rank = 0;
        if (value <= 30) {
            rank = 0;
        } else if (30 < value && value <= 58) {
            rank = 1;
        } else if (58 < value && value <= 86) {
            rank = 2;
        } else if (86 < value && value <= 114) {
            rank = 3;
        } else if (114 < value && value <= 142) {
            rank = 4;
        } else if (142 < value && value <= 170) {
            rank = 5;
        } else if (170 < value && value <= 198) {
            rank = 6;
        } else if (198 < value && value <= 226) {
            rank = 7;
        } else if (226 < value) {
            rank = 8;
        }

        return rank;
    }

    @Override
    protected void setObjectVariable() {

    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_menu;
    }

    @Override
    protected boolean enableShowWithMenuLayout() {
        return false;
    }

    @Override
    public void initViews(@Nullable Bundle savedInstanceState) {
        super.initViews(savedInstanceState);
        showTitleName(getString(R.string.title_function_menu));
        setHomeImageResource(R.drawable.selector_back);
        int mPermission =
                DriverPresenter.getInstance().getCurYxDriverInfo() != null
                        && DriverPresenter.getInstance().getCurYxDriverInfo().mLogType == LogType.ENGINEER.getType()
                        ? MenuAdapter.PERMISSION_ADMINISTRTOR :
                        MenuAdapter.PERMISSION_GUSET;
        menuAdapter = new MenuAdapter(this, mPermission);
        getDataBinding().gvMenu.setAdapter(menuAdapter);
        menuAdapter.setOnItemClickListene(new MenuAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(int textId) {
                switch (textId) {
                    case R.string.base_setup_brightness:
                        showLightControllerZone();
                        break;
                    case R.string.main_menu_toast:
                        yxSingleActivity(MessageActivity.class);
                        //activeHome();
                        break;
                }
            }
        });

        getDataBinding().sbLight.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress,
                                          boolean fromUser) {
                int tmpRank = computeRank(progress);
                if (tmpRank != curBrightnessRank) {
                    curBrightnessRank = tmpRank;
                    getDataBinding().setLightLevel(curBrightnessRank);
                }

                if (progress < 10) {
                    progress = 10;
                }

                if (DateUtil.isDayTime()) {
                    AdjustBrightness.setScreenBrightnessAndSaveNew(
                            HardwareActivity.strKeyBrightnessDay,
                            progress);
                } else {
                    AdjustBrightness.saveConfigScreenBrightness(
                            HardwareActivity.strKeyBrightnessDay,
                            progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });

        getDataBinding().llLightBg.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (!ViewUtils.isInRangeOfView(getDataBinding().llLight, event)){
                    getDataBinding().setShowLight(false);
                }
                return false;
            }
        });
        //消化掉点击事件，避免点击事件被其他控件触发
        getDataBinding().llLightBg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
    }

    @Override
    protected void updateDriverInfo(YxDriverInfo info) {
        super.updateDriverInfo(info);
        if (info != null) {
            menuAdapter.setPermissionLevel(info.mLogType == LogType.ENGINEER.getType()
                    ? MenuAdapter.PERMISSION_ADMINISTRTOR : MenuAdapter.PERMISSION_GUSET);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (getDataBinding().gLight.isShown()){
                getDataBinding().setShowLight(false);
            } else {
                yxSingleActivity(HomeActivity.class);
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }


    /**
     * 显示亮度控制区
     */
    private void showLightControllerZone() {
        getDataBinding().setShowLight(true);
        getDataBinding().setBrightness(getBrightnessValue());
        curBrightnessRank = computeRank(getBrightnessValue());
        getDataBinding().setLightLevel(curBrightnessRank);
    }

    /**
     * 获取当前亮度值
     *
     * @return
     */
    private int getBrightnessValue() {
        int value = -1;
        if (DateUtil.isDayTime()) {//白天
            value = AdjustBrightness
                    .getScreenBrightnessNew(HardwareActivity.strKeyBrightnessDay);
            if (value == -1) {//未设置过的直接用默认值
                value = HardwareActivity.brightnessDayDefaultValue;
            }
        } else {//夜晚
            value = AdjustBrightness
                    .getScreenBrightnessNew(HardwareActivity.strKeyBrightnessNight);
            if (value == -1) {//未设置过的直接用默认值
                value = HardwareActivity.brightnessNightDefaultValue;
            }
        }

        return value;
    }

    @Override
    protected Class<?> getBackToClass() {
        return HomeActivity.class;
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        //Log.e("TimeCheck", TAG + " onCreate");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        //Log.e("TimeCheck", TAG + " onDestroy");
    }
}
