package com.yaxon.yx_facedetect.proxy;

import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.util.Log;

import com.yaxon.yx_facedetect.FaceParams;
import com.yaxon.yx_facedetect.IFaceDetect;
import com.yaxon.yx_facedetect.IKyFaceInterface2;
import com.yaxon.yx_facedetect.YxFaceParameterInfo;

/**
 * <AUTHOR> sprint
 * @date : 21-3-18下午4:05
 * @desc :开易人脸识别功能，包含dsm和adas版本
 */
public class YxKyFaceServiceManager2 implements IFaceDetect {
    private static final String TAG = "YxKyFaceServiceManager2";
    private static YxKyFaceServiceManager2 mInstance;
    private final static String ACTION_FACE_SERVICE = "com.yaxon.yx_facedetect.IKYFaceInterface";
    private final static String FACE_SERVICE_PACKAGE = "com.yaxon.aiservice";
    private IKyFaceInterface2 mFaceService;
    private Context mContext;
    private boolean isUnbinding;

    private YxKyFaceServiceManager2() {
    }

    public static synchronized YxKyFaceServiceManager2 getInstance() {
        if (mInstance == null) {
            mInstance = new YxKyFaceServiceManager2();
        }
        return mInstance;
    }

    private ServiceConnection mConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "onServiceConnected: ");
            mFaceService = IKyFaceInterface2.Stub.asInterface(service);

        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            mFaceService = null;
            Log.d(TAG, "onServiceDisconnected: ");
            if (!isUnbinding){
                bindService(mContext);
            }
        }
    };


    @Override
    public void bindService(Context context) {
        Log.d(TAG, "bindService: ");
        if (context == null) {
            return;
        }
        mContext = context.getApplicationContext();
        if (mContext != null) {
            Intent intent = new Intent(ACTION_FACE_SERVICE);
            intent.setPackage(FACE_SERVICE_PACKAGE);
            mContext.bindService(intent, mConnection, Service.BIND_AUTO_CREATE);
        }
    }

    @Override
    public void unBindService() {
        Log.d(TAG, "unBindService: ");
        try {
            if (mContext != null) {
                mContext.unbindService(mConnection);
                mContext = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void faceConfig(boolean detectTest, int idKeyCount, String appId,
                           String apikey, String secretkey, float similar_threshold) {
        Log.d(TAG, "faceConfig: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "faceConfig: mFaceService is null");
                return;
            }
            mFaceService.faceConfig(detectTest, null, null, null, similar_threshold);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void activityFaceSdk() {
        Log.d(TAG, "activityFaceSdk: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "activityFaceSdk: mFaceService is null");
                return;
            }
            mFaceService.activityFaceSdk();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startFaceRegister(boolean isTest, String driverName) {
        Log.d(TAG, "startFaceRegister: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "startFaceRegister: mFaceService is null");
                return;
            }
            mFaceService.startFaceRegister(isTest, driverName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void registerFaceByImg(String filePath, String fileName) {
        Log.d(TAG, "registerFaceByImg: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "registerFaceByImg: mFaceService is null");
                return;
            }
            mFaceService.registerFaceByImg(filePath, fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startTimerFace(YxFaceParameterInfo info) {
        Log.d(TAG, "startTimerFace: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "startTimerFace: mFaceService is null");
                return;
            }
            mFaceService.startTimerFace(info);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startFaceService(String driverName, boolean isTest, FaceParams faceParams) {
        Log.d(TAG, "startFaceService: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "startFaceService: mFaceService is null");
                return;
            }
            mFaceService.startFaceService(driverName, isTest, faceParams);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startFaceCountService(boolean isTest, boolean isCheck) {
        Log.d(TAG, "startFaceCountService: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "startFaceCountService: mFaceService is null");
                return;
            }
            mFaceService.startFaceCountService(isTest, isCheck);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean checkRegisterDriver(String driverName) {
        Log.d(TAG, "checkRegisterDriver: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "checkRegisterDriver: mFaceService is null");
                return false;
            }
            return mFaceService.checkRegisterDriver(driverName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void stopFaceDetect() {
        Log.d(TAG, "stopFaceDetect: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "stopFaceDetect: mFaceService is null");
                return;
            }
            mFaceService.stopFaceDetect();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startFaceCountActivity() {
        Log.d(TAG, "startFaceCountActivity: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "startFaceCountActivity: mFaceService is null");
                return;
            }
            mFaceService.startFaceCountActivity();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startFaceManagerActivity() {
        Log.d(TAG, "startFaceManagerActivity: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "startFaceManagerActivity: mFaceService is null");
                return;
            }
            mFaceService.startFaceManagerActivity();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setUnbinding(boolean isUndinding) {
        this.isUnbinding = isUndinding;
    }

    @Override
    public void removeRegisterFace(String fileName) {
        try {
            if (mFaceService == null) {
                Log.e(TAG, "removeRegisterFace: interface is null");
                return;
            }
            mFaceService.removeRegisterFace(fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getSpecifyAlgorithmCameraChannel(int type) {
        if (mFaceService == null) {
            Log.e(TAG, "getSpecifyAlgorithmCameraChannel: interface is null");
            return -1;
        }
        try {
            return mFaceService.getSpecifyAlgorithmCameraChannel(type);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

    @Override
    public boolean isActivate() {
        if (mFaceService == null) {
            Log.e(TAG, "isActivate: interface is null");
            return false;
        }
        try {
            return mFaceService.isActivate();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public String faceDetectByImg(String filePath, String fileName, boolean isFuzzyQuery) {

        Log.e(TAG, "faceDetectByImg" );
        return null;
    }

    @Override
    public String registerFaceByPath(String path) {

        if (mFaceService == null) {
            Log.e(TAG, "faceDetectByImg: interface is null");
            return "error";
        }

        try {
            return mFaceService.registerFaceByPath(path);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}
