package com.yaxon.business.presenter;



import com.yaxon.base.common.BaseModel;
import com.yaxon.business.state.StatusManager;



public abstract class PresenterBase extends BaseModel implements StatusManager.DeviceStateChangeListener {
    public PresenterBase(){
        StatusManager.getInstance().addListener(this);
    }

    /**
     * 初始化
     */
    public static void init(){
        CommonPresenter.getInstance();
        DriverPresenter.getInstance();
        OrderPresenter.getInstance();
        //DriverPresenter.getInstance().setUpdate(true);
    }

    @Override
    public void onCenterState(int center) {

    }

    @Override
    public void onLinkState(int link) {

    }

}
