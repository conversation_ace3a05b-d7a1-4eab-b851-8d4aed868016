package com.yaxon.telematics.service.http;

import android.os.Handler;
import android.os.Handler.Callback;
import android.os.HandlerThread;
import android.os.Message;
import android.os.Process;
import android.os.SystemClock;
import android.util.Log;

import com.yaxon.telematics.service.protocol.monitor.MonitorSocketManage;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.HashMap;


/**
 * HTTP请求服务
 *
 * <AUTHOR>
 * @version V1.0，2013-11-25
 * @see
 * @since V1.1.2
 */
public class HttpRequest implements Callback {
    private static final String TAG = "CommHttpRequest";

    private static HttpRequest mHttpRequest = null;
    private MonitorSocketManage mSock = MonitorSocketManage.getInstance();

    /**
     * 消息处理器，可以处理定时器等自定义消息
     */
    protected Handler mHandler = null;

    /**
     * 处理Handler消息循环的HandlerThread类对象
     */
    private HandlerThread mHandlerThread = null;


    private static final byte CR = '\r';
    private static final byte LF = '\n';
    private static final byte[] CRLF = {CR, LF};

    //如果返回得到的header的数据过多，则需要扩展buffer；  
    //content-length的长度每次都new出来，在接收数据时，需要额外的从socket的缓存队列里获取数据，而不仅仅从mHttpHeadBuf获取
    private int HTTP_MAX_BUF = 4096;
    private byte[] mHttpHeadBuf = new byte[2048];
    private int mHttpHeadBufLen = 0;//mHttpHeadBuf里的有效字节数
    private HashMap<String, String> mHttpHeaderMap = new HashMap<String, String>();


    private HttpRequest() {
        super();

        mHandlerThread = new HandlerThread("HttpRequest", Process.THREAD_PRIORITY_URGENT_AUDIO);
        mHandlerThread.start();
        mHandler = new Handler(mHandlerThread.getLooper(), this);

    }

    //
    public synchronized static HttpRequest getInstance() {
        if (null == mHttpRequest) {
            mHttpRequest = new HttpRequest();
        }

        return mHttpRequest;
    }

    public int getHttpChannel() {
        return mSock.getFreeChannel();
    }


    /**
     * 创建HTTP通道
     * <p>
     * <br>
     * 备注：
     * <pre>
     * </pre>
     *
     * @param ip   服务器IP
     * @param port 端口
     * @return 返回创建的SOCKET通道
     * @throws
     * @see
     * @since V1.0
     */
    public synchronized int connectHttpChannel(int channel, String ip, int port) {
        Log.v(TAG, ">>>>>>>>>start connectHttpChanne");
        if (mSock.isConnected(channel)) {
            return channel;
        }

        channel = getHttpChannel();

        if (channel == -1) {
            Log.v(TAG, ">>>>>>>>>connectHttpChannel getHttpChannel == -1");
            return -1;
        }

        Log.v(TAG, ">>>>>>>>>connectHttpChannel channel == " + channel);
        Log.v(TAG, ">>>>>>>>>connectHttpChannel ip == " + ip);
        Log.v(TAG, ">>>>>>>>>connectHttpChannel port == " + port);

        boolean connectResult = mSock.connect(channel, ip, port);
        if (!connectResult) {
            Log.v(TAG, ">>>>>>>>>connectHttpChannel connect fail");
            return -1;
        }

        Log.v(TAG, ">>>>>>>>>connectHttpChannel connect success, channel = " + channel);
        return channel;
    }

    /**
     * 销毁HTTP通道
     * <p>
     * <br>
     * 备注：
     * <pre>
     * </pre>
     *
     * @param channel 通道
     * @return 返回创建的SOCKET通道
     * @throws
     * @see
     * @since V1.0
     */
    public synchronized void disconnectHttpChannel(int channel) {
        if (mSock.isConnected(channel)) {
            mSock.close(channel);
        }

        return;
    }

    /**
     * 请求HTTP发送
     * <p>
     * <br>
     * 备注：阻塞，直到获取应答或超时
     *
     * @param channel    通道
     * @param entityData 协议数据
     * @param ip         服务器IP
     * @param port       端口
     * @return 返回创建的SOCKET通道
     * @throws
     * @see
     * @since V1.0
     */
    public synchronized byte[] doHttpRequest(int channel, byte[] entityData, String ip, int port) {
        Log.v(TAG, ">>>>>>>>>doHttpRequest ...........................");

        if (ip == null) {
            Log.v(TAG, ">>>>>>>>>doHttpRequest ip == null ....");
            return null;
        }

        if (!mSock.isConnected(channel)) {
            Log.v(TAG, ">>>>>>>>>doHttpRequest if (!mSock.isConnected(channel))");
            return null;
        }

        //写HTTP协议体
        byte[] httpData = assembleHttp(entityData, ip, port);
        if (httpData == null) {
            Log.v(TAG, ">>>>>>>>>doHttpRequest assembleHttp == null");
            return null;
        }

        //先清除缓存的http数据
        mSock.clearReceivedData(channel);
        mHttpHeadBufLen = 0;

        //发送http请求
        int sendedLen = mSock.send(channel, httpData, httpData.length);
        if (sendedLen != httpData.length) {
            Log.v(TAG, ">>>>>>>>>doHttpRequest sendedLen != httpData.length");
            return null;
        }

        //等待应答
        //1、http 200
        if (!readHttpSusLine(channel, 30000)) {
            Log.e(TAG, "readHttpSusLine: false");
            return null;
        }

        //2、http header
        if (!readHttpHeader(channel, 30000)) {
            Log.i(TAG, "readHttpHeader false");
            return null;
        }

        //3、http content
        String strLength = mHttpHeaderMap.get("Content-Length");
        if (null == strLength) {
            Log.i(TAG, "get Content-Length false");
            return null;
        }

        int length = 0;
        try {
            length = Integer.parseInt(strLength);

        } catch (Exception e) {
            e.printStackTrace();
            length = 0;
        }

        Log.i(TAG, "Content-Length=" + length);

        byte[] data = null;
        if (length > 0) {
            data = readHttpContent(channel, length, 30000);
        }

        //至此，一次http交互结束
        mHttpHeadBufLen = 0;
        if (data != null) {
            Log.i(TAG, "doHttpRequest sus. data-length=" + data.length);
        } else {
            Log.i(TAG, "doHttpRequest error. data NULL");
        }

        return data;

    }

    //1、等待htt请求成功头
    private boolean readHttpSusLine(int channel, int timeOutMili) {
        String ans = null;
        ans = readHttpLine(channel, timeOutMili);

        if (ans == null) {
            Log.e(TAG, "readHttpSusLine: timeout");
            return false;
        }

        if ("".equals(ans)) {
            Log.e(TAG, "readHttpSusLine: got empty line");
            return false;
        }

        if (ans.contains("HTTP")) {
            if (ans.contains("200")) {
                return true;
            } else {
                Log.e(TAG, "readHttpSusLine got error answer:" + ans);
                return false;
            }
        }

        Log.e(TAG, "UnKnown Line:" + ans);
        return false;
    }

    //2、读取http header
    private boolean readHttpHeader(int channel, int timeOutMili) {
        mHttpHeaderMap.clear();

        String strHeader = null;
        long tmEnd = SystemClock.elapsedRealtime() + timeOutMili;

        boolean bResult = false;

        while (SystemClock.elapsedRealtime() < tmEnd) {
            strHeader = readHttpLine(channel, timeOutMili);
            if (strHeader == null) {
                YxSleep(50);
                continue;
            }

            if ("".equals(strHeader)) {
                Log.e(TAG, "readHttpHeader: got empty line");
                YxSleep(50);
                continue;
            }

            String[] nv = strHeader.split(": ");  // 头部字段的名值都是以(冒号+空格)分隔的
            if (null != nv && nv.length > 1) {
                for (int i = 0; i < nv.length; ) {
                    if ((i + 1) < nv.length) {
                        mHttpHeaderMap.put(nv[i], nv[i + 1]);
                        bResult = true;
                    }

                    i += 2;
                }
            } else {
                Log.i(TAG, "readHttpHeader: split header err");
            }


            break;
        }


        return bResult;
    }

    //3、读取http content
    private byte[] readHttpContent(int channel, int bytesToRead, int timeOutMili) {
        //此时正确的数据应该是回车+换行开头，后接真正的content-length长度的应答数据
        Log.i(TAG, "readHttpContent: 1");

        //先读取CR LF
        String strEmptyLine = null;
        strEmptyLine = getLine(null);
        if (strEmptyLine == null) {
            Log.i(TAG, "readHttpContent: get empty line NULL");
            return null;
        }

        if (!"".equals(strEmptyLine)) {
            Log.i(TAG, "readHttpContent: get empty line error:" + strEmptyLine);
            return null;
        }


        if (timeOutMili < 10) {
            timeOutMili = 10;
        }

        if (timeOutMili > 60000) {
            Log.i(TAG, "readHttpContent: timeout value too large:" + timeOutMili);
            timeOutMili = 60000;
            Log.i(TAG, "readHttpContent: timeout value,reset to::" + timeOutMili);
        }

        long tmEnd = SystemClock.elapsedRealtime() + timeOutMili;

        byte[] data = new byte[bytesToRead];
        int index = 0;
        int waitBytes = bytesToRead;//需要等待的字节数

        if (mHttpHeadBufLen > 0) {
            if (mHttpHeadBufLen <= waitBytes) {//全部拷贝
                System.arraycopy(mHttpHeadBuf, 0, data, index, mHttpHeadBufLen);
                index += mHttpHeadBufLen;
                waitBytes -= mHttpHeadBufLen;
            } else {//部分拷贝
                System.arraycopy(mHttpHeadBuf, 0, data, index, waitBytes);
                index += waitBytes;
                waitBytes = 0;
            }
        }

        byte[] recvData = null;
        Log.i(TAG, "readHttpContent: 2");
        //如果数据不够，则等待sock接收数据
        while ((waitBytes > 0) && (SystemClock.elapsedRealtime() < tmEnd)) {
            recvData = mSock.receive(channel);
            if (recvData == null) {
                YxSleep(50);
                continue;
            }

            int copyBytes = 0;
            if (recvData.length <= waitBytes) {
                copyBytes = recvData.length;
            } else {
                copyBytes = waitBytes;
            }

            System.arraycopy(recvData, 0, data, index, copyBytes);
            waitBytes -= copyBytes;
            index += copyBytes;

            if (waitBytes < 0) {
                waitBytes = 0;
            }

        }

        Log.i(TAG, "readHttpContent: 3");

        if (waitBytes > 0) {
            Log.e(TAG, "readHttpContent length error. BufLen=" + mHttpHeadBufLen + ", bytesToRead=" + bytesToRead);
            return null;
        }

        Log.i(TAG, "readHttpContent: end sus");

        return data;
    }

    /**
     * 获取http数据的一行
     * 备注：
     *
     * @param channel     通道号
     * @param timeOutmili 超时时间
     * @return 数据不包含\r\n或超时则返回null；空行返回""；正确返回的是指定编码的字符串
     * @throws
     * @since V1.0.1
     */
    private String readHttpLine(int channel, int timeOutmili) {

        if (timeOutmili < 10) {
            timeOutmili = 10;
        }

        long tmEnd = SystemClock.elapsedRealtime() + timeOutmili;


        byte[] recvData = null;
        String strLine = null;

        strLine = getLine("GB2312");
        if (null != strLine) {
            return strLine;
        }

        //Log.i(TAG, "readHttpLine: start to wait for http line,channel=" + channel);

        int bytesAvailable = 0;
        while (SystemClock.elapsedRealtime() < tmEnd) {
            recvData = mSock.receive(channel);
            if (recvData == null) {
                YxSleep(50);
                continue;
            }

            //Log.i(TAG, "readHttpLine: get sock data");
            //LogUtil.printLogHex(TAG, "", recvData, recvData.length);

            //HTTP_MAX_BUF
            bytesAvailable = recvData.length + mHttpHeadBufLen;
            //如果当前数据超出缓存大小
            if (bytesAvailable > mHttpHeadBuf.length) {
                Log.e(TAG, "################ Http buffer is too short");
                if (bytesAvailable > HTTP_MAX_BUF || mHttpHeadBuf.length >= HTTP_MAX_BUF) {//如果数据超出最大缓存，则丢弃
                    Log.e(TAG, "################ Http buffer length exceeds max value... clear buffer");
                    mHttpHeadBufLen = 0;
                    return null;
                } else {
                    Log.e(TAG, "################ reassign Http Buffer");
                    byte[] newBuf = new byte[HTTP_MAX_BUF];
                    for (int i = 0; i < mHttpHeadBufLen; i++) {
                        newBuf[i] = mHttpHeadBuf[i];
                    }

                    for (int i = 0; i < recvData.length; i++) {
                        newBuf[mHttpHeadBufLen + i] = recvData[i];
                    }

                    mHttpHeadBuf = newBuf;
                    mHttpHeadBufLen = bytesAvailable;

                }
            } else {
                System.arraycopy(recvData, 0, mHttpHeadBuf, mHttpHeadBufLen, recvData.length);
                mHttpHeadBufLen = bytesAvailable;
            }


            strLine = getLine("GB2312");
            if (null != strLine) {
                return strLine;
            }

        }


        return null;
    }

    /**
     * 获取http数据的一行
     * 备注：
     *
     * @param charSet byte[]转字符串的字符集
     * @return 数据不包含\r\n则返回null；空行返回""；
     * @throws
     * @since V1.0.1
     */
    private String getLine(String charSet) {
        if (mHttpHeadBufLen < 2)
            return null;

        int posCR = -1;

        for (int i = 0; i < mHttpHeadBufLen; i++) {
            if (mHttpHeadBuf[i] == CR) {
                if ((i + 1) < mHttpHeadBufLen) {//防止CR为最后一个字符
                    if (mHttpHeadBuf[i + 1] == LF) {
                        posCR = i;
                        break;
                    }
                }
            }
        }


        if (posCR < 0) {//未找到
            Log.i(TAG, "no cr or lf found");
            return null;
        }


        if (charSet == null) {
            charSet = "GB2312";
        }

        String line = null;
        int dataLeft = 0;
        //找到一行字符，有可能是空行，即只包含\r\n的行
        if (posCR > 0) {
            try {
                line = new String(mHttpHeadBuf, 0, posCR, charSet);
            } catch (UnsupportedEncodingException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        } else {//空行
            line = "";
        }

        dataLeft = mHttpHeadBufLen - posCR - 2;//扣除CR LF
        if (dataLeft > 0) {
            for (int i = 0; i < dataLeft; i++) {
                mHttpHeadBuf[i] = mHttpHeadBuf[posCR + 2 + i];
            }
        } else {
            dataLeft = 0;
        }

        mHttpHeadBufLen = dataLeft;

        return line;

    }

    /**
     * 组帧HTTP头
     * 备注：
     *
     * @param dataSrc 协议数据
     * @param ip      服务器
     * @param port    端口
     * @return HTTP数据
     * @throws
     * @since V1.0.1
     */
    private byte[] assembleHttpHead(byte[] dataSrc, String ip, int port) {
        //ByteArrayOutputStream baos = new ByteArrayOutputStream();

        StringBuilder sb = new StringBuilder();

        sb.append("POST http://");
        sb.append(ip);
        sb.append(':');
        sb.append(port);
        sb.append(' ');
        sb.append("HTTP/1.1");
        sb.append("\r\n");

        sb.append("Content-Length: ");
        if (dataSrc != null) {
            sb.append(dataSrc.length);
        } else {
            sb.append(0);
        }
        sb.append("\r\n");
        sb.append("\r\n");

        Log.v(TAG, ">>>>>>>>>>>>assembleHttpHead is:" + sb.toString());

        return sb.toString().getBytes(Charset.forName("UTF-8"));
    }

    /**
     * 组帧HTTP数据
     * 备注：
     *
     * @param dataSrc 协议数据
     * @param ip      服务器
     * @param port    端口
     * @return HTTP数据
     * @throws
     * @since V1.0.1
     */
    private byte[] assembleHttp(byte[] dataSrc, String ip, int port) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        byte[] head = assembleHttpHead(dataSrc, ip, port);

        if (head == null) {
            return null;
        }

        try {
            baos.write(head);
            baos.write(dataSrc);
            StringBuilder sb = new StringBuilder();
            sb.append("\r\n");
            baos.write(sb.toString().getBytes(Charset.forName("UTF-8")));
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        return baos.toByteArray();
    }

    @Override
    public boolean handleMessage(Message msg) {
        Log.v(TAG, ">>>>>>>>>HttpRequedst handleMessage msg.what = " + msg.what);
        switch (msg.what) {

        }
        return true;
    }


    private void YxSleep(int mili) {
        try {
            Thread.sleep(mili);
        } catch (InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }


}
