/*
 * 文 件 名:  YXAsyncTask.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2013-8-3
 * 文件描述:  自定义AsyncTask，将提示框、控件的禁用、启用操作封装在一起。
 *****************************修改记录********************************
 * 
 * 修 改 者:  
 * 修改单号： [可选]
 * 修改描述:
 *
 *********************************************************************  
 */
package com.yaxon.until;

import android.content.Context;
import android.os.AsyncTask;
import android.util.Log;
import android.view.View;

import com.yaxon.controls.YXProgressDialog2;
import com.yaxon.yx_control.R;

/**
 * 封装AsyncTask，将提示框、控件控制进行统一处理。
 *
 * <AUTHOR>
 * @version V1.0，2013-8-3
 * @see
 * @since V2.0.3
 */
public class YXAsyncTask<Params, Progress, Result> extends AsyncTask<Params, Progress, Result> {
    private static final String TAG = "YXAsyncTask";

    private Context mContext = null;
    private View mView = null;
    private boolean mIsShowPrompt = false;
    private String mContent = "";
    private YXProgressDialog2 mProgressDialog = null;

    public YXAsyncTask(Context context, View view, boolean isShowPrompt, String content) {
        mContext = context;
        mView = view;
        mIsShowPrompt = isShowPrompt;
        mContent = content;

        if (mIsShowPrompt && mContext != null) {
            if (mContent != null) {
                mProgressDialog = new YXProgressDialog2(mContext,
                        mContent);
            } else {
                mProgressDialog = new YXProgressDialog2(mContext,
                        mContext.getString(R.string.deal_backgroud_prompt));
            }

        }
    }

    @Override
    protected Result doInBackground(Params... params) {
        // TODO Auto-generated method stub        
        return null;
    }


    @Override
    protected void onCancelled() {
        // TODO Auto-generated method stub
        super.onCancelled();
//        if (mIsShowPrompt && mProgressDialog.isShowing())
//        {
//            mProgressDialog.dismiss();
//        }  
        if (mProgressDialog != null) {
            mProgressDialog.dismiss();
            mProgressDialog = null;
        }

    }

    @Override
    protected void onPostExecute(Result result) {
        // TODO Auto-generated method stub
        super.onPostExecute(result);
        Log.i(TAG, ">>>Invoke onPostExecute");

        if (mProgressDialog != null) {
            mProgressDialog.dismiss();
            mProgressDialog = null;
        }
//        if (mIsShowPrompt && mProgressDialog.isShowing())
//        {
//            mProgressDialog.dismiss();
//        }

        if (null != mView) {
            mView.setEnabled(true);
        }
    }

    @Override
    protected void onPreExecute() {
        // TODO Auto-generated method stub
        super.onPreExecute();
        Log.i(TAG, ">>>Invoke onPreExecute");

        if (null != mView) {
            mView.setEnabled(false);
        }

        if (mProgressDialog != null) {
            mProgressDialog.show();
        }
    }

    //如果要提前终止，需要调用这个接口
    public void yxCancel(boolean mayInterruptIfRunning) {
        if (mProgressDialog != null) {
            mProgressDialog.dismiss();
            mProgressDialog = null;
        }

        cancel(mayInterruptIfRunning);

    }


//    public void setDialogCancelListener(DialogInterface.OnCancelListener l)
//    {
//    	mProgressDialog.setOnDialogCancelListener(l);
//    	
//    }    
//    
//    public void setDialogDismissListener(DialogInterface.OnDismissListener l)
//    {
//    	mProgressDialog.setOnDialogDismissListener(l);
//    }     

}
