package com.yaxon.telematics.service.protocol.recorder;

import android.content.Context;
import android.content.Intent;
import android.media.MediaRecorder;
import android.os.Environment;
import android.os.PowerManager;
import android.os.PowerManager.WakeLock;
import android.util.Log;

import com.yaxon.telematics.service.ComuServiceApp;
import com.yaxon.telematics.service.R;
import com.yaxon.telematics.service.YxPlatformServiceDelegate;
import com.yaxon.telematics.service.aidl.main.AudioFileInfo;
import com.yaxon.telematics.service.protocol.monitor.MediaInfoManage;
import com.yaxon.telematics.service.protocol.monitor.MonitorProtocolDealBase;
import com.yaxon.telematics.service.util.LogUtil;
import com.yaxon.voiceswitcher.aidl.ResourceDescription;

import java.util.Timer;
import java.util.TimerTask;

public class MediaRecordUtil implements Recorder.OnStateChangedListener {
    public static final String TAG = "MediaRecordUtil";

    public static final String AUDIO_CTRL_APP_NAME_RECORD = "com.yaxon.110r.record";

    public static final String PACKAGE_NAME = ComuServiceApp.getInstance().getPackageName();

    public static final String APP_FLAG_NAME = ComuServiceApp.getInstance().getPackageName() + ".record";

    private static final int PRIORITY = 7;

    private Context mContext;
    private static MediaRecordUtil mMediaRecordUtil = null;
    private Timer timer = null;
    private MonitorProtocolDealBase mMonitorProtocolDealBase = null;
    private Recorder mRecorder = null;

    private RemainingTimeCalculator mRemainingTimeCalculator;
    private WakeLock mWakeLock;
    private boolean mSampleInterrupted = false;
    private String mErrorUiMessage = null; // Some error messages are displayed
    // in the UI,
    // not a dialog. This happens when a recording
    // is interrupted for some reason.

    static final String AUDIO_3GPP = "audio/3gpp";
    static final String AUDIO_AMR = "audio/amr";
    static final String AUDIO_ANY = "audio/*";
    static final String ANY_ANY = "*/*";

    static final int BITRATE_NORMAL = 5900;
    static final int BITRATE_AMR = 5900; // bits/sec
    static final int BITRATE_3GPP = 5900;

    public Recorder getmRecorder() {
        return mRecorder;
    }

    public MonitorProtocolDealBase getmMonitorProtocolDealBase() {
        return mMonitorProtocolDealBase;
    }

    public void setmMonitorProtocolDealBase(MonitorProtocolDealBase mMonitorProtocolDealBase) {
        this.mMonitorProtocolDealBase = mMonitorProtocolDealBase;
    }

    static {
        mMediaRecordUtil = new MediaRecordUtil(ComuServiceApp.getInstance().getApplicationContext());
    }

    public synchronized static MediaRecordUtil getInstance() {
        if (null == mMediaRecordUtil) {
            mMediaRecordUtil = new MediaRecordUtil(ComuServiceApp.getInstance().getApplicationContext());
        }

        return mMediaRecordUtil;
    }

    private MediaRecordUtil(Context context) {
        mContext = context;
    }

    /*
     * Make sure we're not recording music playing in the background, ask the
     * MediaPlaybackService to pause playback.
     */
    private void stopAudioPlayback() {
        // Shamelessly copied from MediaPlaybackService.java, which
        // should be public, but isn't.
        Intent i = new Intent("com.android.music.musicservicecommand");
        i.putExtra("command", "pause");

        mContext.sendBroadcast(i);
    }

    /*
     * Called when Recorder changed it's state.
     */
    public void onStateChanged(int state) {
        LogUtil.printLog(TAG, " " + state);
        if (state == Recorder.RECORDING_STATE) {
            mWakeLock.acquire(); // we don't want to go to sleep while recording
        } else {
            if (mWakeLock.isHeld())
                mWakeLock.release();
        }
        switch (state) {
            case Recorder.RECORDING_STATE:
                LogUtil.printLog(TAG, "RECORDING_STATE");
                // 说明成功启动录音
                // 录音请求应答：接收成功可以开始录音
                if (mMonitorProtocolDealBase != null) {
                    if (mMonitorProtocolDealBase instanceof MonitorRecordFunciton) {
                        ((MonitorRecordFunciton) mMonitorProtocolDealBase).responseStartRecord((byte) 1);
                    }
                }
                mSampleInterrupted = false;
                mErrorUiMessage = null;

                break;
            case Recorder.RECORD_COMPLETED_NORMAL_STATE:
                LogUtil.printLog(TAG, "RECORD_COMPLETED_NORMAL_STATE");
                // 录音结果通知请求应答：录音成功，录音完成
                // 填充剩余内容
                this.getmRecorder().fillOrResetOthersAttr();
                // 保存到数据库
                MediaInfoManage.getInstance().SaveAudioFileInfo(this.getmRecorder().getmAudioFileInfo());
                // 发送应答
                if (mMonitorProtocolDealBase != null) {
                    if (mMonitorProtocolDealBase instanceof MonitorRecordFunciton) {
                        ((MonitorRecordFunciton) mMonitorProtocolDealBase).RecorderResultNotify(1);
                    }
                }
                // 注销音频
            /*
             * try { ComuServiceApp.mIVoiceSwitchService.unregister(
             * AUDIO_CTRL_APP_NAME_RECORD, "100"); } catch (RemoteException e) {
             * // TODO Auto-generated catch block e.printStackTrace(); }
             */
                break;
            case Recorder.RECORD_COMPLETED_INTERUPTER_STATE:
                LogUtil.printLog(TAG, "RECORD_COMPLETED_INTERUPTER_STATE");
                // 录音结果通知请求应答：录音成功，录音中止
                // 填充剩余内容
                this.getmRecorder().fillOrResetOthersAttr();
                // 保存到数据库
                MediaInfoManage.getInstance().SaveAudioFileInfo(this.getmRecorder().getmAudioFileInfo());
                // 发送应答
                if (mMonitorProtocolDealBase != null) {
                    if (mMonitorProtocolDealBase instanceof MonitorRecordFunciton) {
                        ((MonitorRecordFunciton) mMonitorProtocolDealBase).RecorderResultNotify(2);
                    }
                }
                // 注销音频
            /*
             * try { ComuServiceApp.mIVoiceSwitchService.unregister(
             * AUDIO_CTRL_APP_NAME_RECORD, "100"); } catch (RemoteException e) {
             * // TODO Auto-generated catch block e.printStackTrace(); }
             */
                break;
            case Recorder.RECORD_COMPLETED_BY_CENTER_STATE:
                LogUtil.printLog(TAG, "RECORD_COMPLETED_BY_CENTER_STATE");
                // 停止录音请求应答：停止成功，该部分也可以在MonitorRecordFunciton::dealStopRecord中直接完成
                // 注销音频
            /*
             * try { ComuServiceApp.mIVoiceSwitchService.unregister(
             * AUDIO_CTRL_APP_NAME_RECORD, "100"); } catch (RemoteException e) {
             * // TODO Auto-generated catch block e.printStackTrace(); }
             */
                break;
        }
        // updateUi();
    }

    /*
     * Called when MediaPlayer encounters an error.
     */
    public void onError(int error) {

        if (mRecorder.getmState() == Recorder.ERROR_STATE) {
            // 启动录音过程失败
            switch (error) {
                case Recorder.STORAGE_ACCESS_ERROR:
                    LogUtil.printLog(TAG, "STORAGE_ACCESS_ERROR");
                    // 说明启动录音失败
                    // 录音请求应答：接收失败，条件不满足
                    if (mMonitorProtocolDealBase != null) {
                        if (mMonitorProtocolDealBase instanceof MonitorRecordFunciton) {
                            ((MonitorRecordFunciton) mMonitorProtocolDealBase).responseStartRecord((byte) 2);
                        }
                    }
                    break;
                case Recorder.INTERNAL_PREPARE_ERROR:
                    LogUtil.printLog(TAG, "INTERNAL_PREPARE_ERROR");
                    // 说明启动录音失败
                    // 录音请求应答：接收失败，条件不满足
                    Log.i(TAG, "onError: " + mRecorder.getmState());
                    if (mMonitorProtocolDealBase != null) {
                        if (mMonitorProtocolDealBase instanceof MonitorRecordFunciton) {
                            ((MonitorRecordFunciton) mMonitorProtocolDealBase).responseStartRecord((byte) 2);
                        }
                    }
                    break;

                case Recorder.IN_CALL_RECORD_ERROR:
                    LogUtil.printLog(TAG, "IN_CALL_RECORD_ERROR");
                    // 录音结果通知请求应答:启动录音失败(刚好来电)
                    Log.i(TAG, "onError: " + mRecorder.getmState());
                    if (mMonitorProtocolDealBase != null) {
                        if (mMonitorProtocolDealBase instanceof MonitorRecordFunciton) {
                            ((MonitorRecordFunciton) mMonitorProtocolDealBase).startRecorderResultNotify();
                        }
                    }
                    break;
                case Recorder.INTERNAL_START_ERROR:
                    LogUtil.printLog(TAG, "INTERNAL_START_ERROR");
                    // 录音结果通知请求应答:启动录音失败（除了刚好来电外的其他情况）
                    Log.i(TAG, "onError: " + mRecorder.getmState());
                    if (mMonitorProtocolDealBase != null) {
                        if (mMonitorProtocolDealBase instanceof MonitorRecordFunciton) {
                            ((MonitorRecordFunciton) mMonitorProtocolDealBase).startRecorderResultNotify();
                        }
                    }
                    break;
            }
            // 注销音频
            YxPlatformServiceDelegate.getInstance().unregister(PACKAGE_NAME, APP_FLAG_NAME, ResourceDescription.MIC_CHANNEL_SYSTEM);

        } else {
            Log.i(TAG, "onError 2: " + mRecorder.getmState());
        }

    }

    TimerTask mStopRecordingTask = null;

    class StopRecordingTask extends TimerTask {

        @Override
        public void run() {
            // TODO Auto-generated method stub
            if (mRecorder != null) {
                mRecorder.stopRecordingForTimeOver();
            }
        }

    }

    public void killTimer() {
        if (timer != null) {
            timer.cancel();
        }
        if (mStopRecordingTask != null) {
            mStopRecordingTask.cancel();
        }
    }

    public void createTimerAndTask(long recroderTimeSpan) {
        timer = new Timer(true);
        mStopRecordingTask = new StopRecordingTask();
        timer.schedule(mStopRecordingTask, recroderTimeSpan * 1000);
    }

    public void stopRecordingByCenter() {
        if (mRecorder != null) {
            mRecorder.stopRecordingByCenter();
        }
    }

    public synchronized void startRecording(AudioFileInfo afi) {
        // TODO Auto-generated method stub
        mRecorder = Recorder.getInstance();
        mRecorder.setOnStateChangedListener(this);
        mRemainingTimeCalculator = new RemainingTimeCalculator(false);

        PowerManager pm = (PowerManager) mContext.getSystemService(Context.POWER_SERVICE);
        mWakeLock = pm.newWakeLock(PowerManager.SCREEN_DIM_WAKE_LOCK, "SoundRecorder");

        mRemainingTimeCalculator.reset();

        if (mRemainingTimeCalculator.ismUseSD() && !Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            mSampleInterrupted = true;
            mErrorUiMessage = mContext.getResources().getString(R.string.insert_sd_card);
            // updateUi();
        } else if (!mRemainingTimeCalculator.diskSpaceAvailable()) {
            mSampleInterrupted = true;
            mErrorUiMessage = mContext.getResources().getString(R.string.storage_is_full);
            // updateUi();
        } else {
            LogUtil.printLog(TAG, "startRecording");

            //if (ComuServiceApp.mIVoiceSwitchService != null)
            if (YxPlatformServiceDelegate.getInstance().register(PACKAGE_NAME, APP_FLAG_NAME, ResourceDescription.PRIORITY_RECORD,
                    ResourceDescription.MIC_CHANNEL_SYSTEM)) {

                try {
//                    if (ComuServiceApp.mIVoiceSwitchService.register(PACKAGE_NAME, APP_FLAG_NAME, ResourceDescription.PRIORITY_RECORD,
//                            ResourceDescription.MIC_CHANNEL_SYSTEM))
//                    {
                    LogUtil.printLog(TAG, "register");
                    if (mRecorder.getmState() != Recorder.RECORDING_STATE) {
                        LogUtil.printLog(TAG, "mRecorder.getmState()!=Recorder.RECORDING_STATE");
                        // 说明未启动过录音，或之前的录音已经结束了
                        stopAudioPlayback();
                        mRemainingTimeCalculator.setBitRate(BITRATE_3GPP);
                        mRecorder.startRecording(mContext, MediaRecorder.OutputFormat.THREE_GPP, ".3gpp", afi);
                        if (afi != null && afi.getmDuration() > 0) {
                            killTimer();
                            createTimerAndTask(afi.getmDuration());
                        }
                    } else {
                        LogUtil.printLog(TAG, "mRecorder.getmState()==Recorder.RECORDING_STATE");
                            /*
                             * Log.i(TAG,
                             * "startRecording: "+mRecorder.getmState());
                             */
                        // 说明已经启动过录音，目前正在录音中
                        if (mMonitorProtocolDealBase != null) {
                            if (mMonitorProtocolDealBase instanceof MonitorRecordFunciton) {
                                ((MonitorRecordFunciton) mMonitorProtocolDealBase).responseStartRecord((byte) 3);

                            }
                        }
                    }
                    //} 

                } catch (Exception e) {
                    // TODO Auto-generated catch block
                    // 接收失败条件不满足
                    if (mMonitorProtocolDealBase != null) {
                        if (mMonitorProtocolDealBase instanceof MonitorRecordFunciton) {
                            ((MonitorRecordFunciton) mMonitorProtocolDealBase).responseStartRecord((byte) 2);
                        }
                    }
                    e.printStackTrace();
                }
            } else {
                LogUtil.printLog(TAG, "ComuServiceApp.mIVoiceSwitchService==false");
                // 接收失败条件不满足
                if (mMonitorProtocolDealBase != null) {
                    if (mMonitorProtocolDealBase instanceof MonitorRecordFunciton) {
                        ((MonitorRecordFunciton) mMonitorProtocolDealBase).responseStartRecord((byte) 2);
                    }
                }
            }

        }
        /*
         * if (mMaxFileSize != -1) { mRemainingTimeCalculator.setFileSizeLimit(
         * mRecorder.sampleFile(), mMaxFileSize); }
         */
    }

}
