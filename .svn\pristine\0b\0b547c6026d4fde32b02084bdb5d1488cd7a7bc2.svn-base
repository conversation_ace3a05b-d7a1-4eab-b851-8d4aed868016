package com.yaxon.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.CheckedTextView;
import android.widget.ImageView;
import android.widget.TextView;

import com.yaxon.utils.StringUtil;


public class ViewHolder {
	private final SparseArray<View> mViews;
	private int mPosition;
	private View mConvertView;

	private ViewHolder(Context context, ViewGroup parent, int layoutId,
                       int position) {
		this.mPosition = position;
		this.mViews = new SparseArray<View>();
		mConvertView = LayoutInflater.from(context).inflate(layoutId, parent,
				false);
		// setTag
		mConvertView.setTag(this);
	}

	/**
	 * 拿到一个ViewHolder对象
	 * 
	 * @param context
	 * @param convertView
	 * @param parent
	 * @param layoutId
	 * @param position
	 * @return
	 */
	public static ViewHolder get(Context context, View convertView,
                                 ViewGroup parent, int layoutId, int position) {
		if (convertView == null) {
			return new ViewHolder(context, parent, layoutId, position);
		}
		ViewHolder holder = (ViewHolder) convertView.getTag();
		holder.setPosition(position);
		return holder;
	}

	public View getConvertView() {
		return mConvertView;
	}

	/**
	 * 通过控件的Id获取对于的控件，如果没有则加入views
	 * 
	 * @param viewId
	 * @return
	 */
	public <T extends View> T getView(int viewId) {
		View view = mViews.get(viewId);
		if (view == null) {
			view = mConvertView.findViewById(viewId);
			mViews.put(viewId, view);
		}
		return (T) view;
	}

	/**
	 * 为TextView设置字符串
	 * 
	 * @param viewId
	 * @param text
	 * @return
	 */
	public ViewHolder setText(int viewId, String text) {
		TextView view = getView(viewId);
		view.setText(StringUtil.checkNull(text, ""));
		return this;
	}
	
	 public ViewHolder setTextColor(int viewId, int color) {
	        TextView view = getView(viewId);
	        view.setTextColor(color);  
	        return this;
	    }

	/**
	 * 为TextView设置字符串
	 * 
	 * @param viewId
	 * @param viewId
	 * @return
	 */
	public String getText(int viewId) {
		TextView view = getView(viewId);
		return view.getText().toString();
	}

	/**
	 * 为View设置背景资源
	 * 
	 * @param viewId
	 * @param backgroudId
	 * @return
	 */
	public ViewHolder setBackgroundResource(int viewId, int backgroudId) {
		View view = getView(viewId);
		view.setBackgroundResource(backgroudId);
		return this;
	}

	/**
	 * 为ImageView设置图片
	 * 
	 * @param viewId
	 * @param drawableId
	 * @return
	 */
	public ViewHolder setImageResource(int viewId, int drawableId) {
		ImageView view = getView(viewId);
		view.setImageResource(drawableId);

		return this;
	}

	/**
	 * 为ImageView设置图片
	 * 
	 * @param viewId
	 * @param
	 * @return
	 */
	public ViewHolder setImageBitmap(int viewId, Bitmap bm) {
		ImageView view = getView(viewId);
		view.setImageBitmap(bm);
		return this;
	}

	/**
	 * 设置图片
	 */
	public ViewHolder setImageByUrl(int viewId, String url, int type) {
		ImageView iv = (ImageView) getView(viewId);
		iv.setTag(url);
		//ImgUtil.getInstance().loadBitmap(iv,url, type, false);
		return this;
	}

	/**
	 * 为CheckedTextView设置 文本
	 * 
	 * @param viewId
	 * @param text
	 * @return
	 */
	public ViewHolder setCheckedTextViewText(int viewId, String text) {
		CheckedTextView view = getView(viewId);
		view.setText(text);
		return this;
	}

	/**
	 * 为CheckedTextView设置 状态
	 * 
	 * @param viewId
	 * @param checked
	 * @return
	 */
	public ViewHolder setCheckedTextViewState(int viewId, boolean checked) {
		CheckedTextView view = getView(viewId);
		view.setChecked(checked);
		return this;
	}

	/**
	 * 获取CheckedTextView 选中状态
	 * 
	 * @param viewId
	 * @param
	 * @return
	 */
	public boolean getCheckedTextViewState(int viewId) {
		CheckedTextView view = getView(viewId);
		return view.isChecked();
	}

	/**
	 * 设置View可见性
	 * 
	 * @param viewId
	 * @param visibility
	 * @return
	 */
	public ViewHolder setVisibility(int viewId, int visibility) {
		View view = getView(viewId);
		view.setVisibility(visibility);
		return this;
	}

	public ViewHolder setSelected(int viewId, boolean selected) {
		View view = getView(viewId);
		view.setSelected(selected);
		return this;
	}

	public ViewHolder setOnClickListener(int viewId, OnClickListener l) {
		View view = getView(viewId);
		view.setOnClickListener(l);
		return this;
	}

	public void setTag(int viewId, Object tag) {
		View view = getView(viewId);
		view.setTag(tag);
	}

	public int getPosition() {
		return mPosition;
	}

	public void setPosition(int p) {
		mPosition = p;
	}

	public void setListener(int viewId, OnClickListener l) {
		View view = getView(viewId);
		view.setOnClickListener(l);
	}

}
