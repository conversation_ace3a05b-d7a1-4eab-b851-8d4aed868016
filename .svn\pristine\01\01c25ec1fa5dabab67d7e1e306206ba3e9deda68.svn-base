/*
 * 文 件 名:  NetworkStatus.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2013-4-24
 * 文件描述:  定义网络状态信息的封装类。
 *****************************修改记录********************************
 * 
 * 修 改 者:  
 * 修改单号： [可选]
 * 修改描述:
 *
 *********************************************************************  
 */
package com.yaxon.telematics.service.aidl.main;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 
 * 封装网络信息各个字段，包括网络连接状态，网络信号、TCP和UDP登录情况。
 * 
 * <AUTHOR>
 * @version   V1.0，2013-4-24
 * @see       
 * @since     V1.1.2
 */
public class NetworkStatus implements Parcelable
{
    /**
     * 网络连接状态
     * <br>
     * 00H - 表示没找到SIM卡
     * 10H - 表示找到SIM卡但没入网
     * 20H - 表示已入网，但没连接数据网
     * 30H - 表示中国移动G网建立
     * 31H - 表示中国移动T网建立
     * 32H - 表示中国联通G网建立
     * 33H - 表示中国联通W网建立
     * 34H - 表示中国电信 C网建立
     * 35H - 表示中国电信 C3网建立
     */
    public byte mNetworkLinkStatus = -1;    
    
    /**
     * 网络信号
     */
    public byte mSignal = -1;
    /**
     * TCP登录情况
     */
    public boolean mTcpLogResult = false;
    /**
     * UDP登录情况
     */
    public boolean mUdpLogResult = false;
    
    @Override
    public int describeContents()
    {
        // TODO Auto-generated method stub
        return 0;
    }
    @Override
    public void writeToParcel(Parcel dest, int flags)
    {
        // TODO Auto-generated method stub
        dest.writeByte(mNetworkLinkStatus);
        dest.writeByte(mSignal);
        dest.writeString(Boolean.toString(mTcpLogResult));
        dest.writeString(Boolean.toString(mUdpLogResult));
    }
    
    public static final Parcelable.Creator<NetworkStatus> CREATOR = new Parcelable.Creator<NetworkStatus>()
    {

        @Override
        public NetworkStatus createFromParcel(Parcel source)
        {
            // TODO Auto-generated method stub
            NetworkStatus networkStatus = new NetworkStatus();
            networkStatus.mNetworkLinkStatus = source.readByte();
            networkStatus.mSignal = source.readByte();
            networkStatus.mTcpLogResult = Boolean.valueOf(source.readString());
            networkStatus.mUdpLogResult = Boolean.valueOf(source.readString());
            return networkStatus;
        }

        @Override
        public NetworkStatus[] newArray(int size)
        {
            // TODO Auto-generated method stub
            return new NetworkStatus[size];
        }
        
    };
}
