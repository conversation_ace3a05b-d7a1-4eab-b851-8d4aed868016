<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="isOnHotMap"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvHotArea"
            android:layout_width="@dimen/w250dp"
            android:layout_height="match_parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@drawable/shape_base_bg"
            android:padding="@dimen/w16dp"/>

        <androidx.cardview.widget.CardView
            android:id="@+id/cvMap"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintLeft_toRightOf="@id/rvHotArea"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginLeft="@dimen/w12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:cardCornerRadius="@dimen/w8dp">

            <com.amap.api.maps.TextureMapView
                android:id="@+id/tmvHotInfo"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>

            <RadioGroup
                android:id="@+id/rgHotInfo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/w16dp"
                android:layout_marginTop="@dimen/h16dp"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rbPassager"
                    style="@style/Yx_Textview_White_20"
                    android:layout_width="@dimen/w160dp"
                    android:layout_height="@dimen/h72dp"
                    android:button="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:textColor="@color/selector_hot_info"
                    android:text="@string/main_map_passenger_heat_map"
                    android:background="@drawable/selector_hot_info"/>

                <RadioButton
                    android:id="@+id/rbAmount"
                    android:layout_marginLeft="@dimen/w16dp"
                    style="@style/Yx_Textview_White_20"
                    android:layout_width="@dimen/w160dp"
                    android:layout_height="@dimen/h72dp"
                    android:button="@null"
                    android:gravity="center"
                    android:textColor="@color/selector_hot_info"
                    android:text="@string/main_map_order_money_map"
                    android:background="@drawable/selector_hot_info"/>
            </RadioGroup>

            <ImageView
                android:id="@+id/ivHotMapSwitcher"
                android:layout_width="@dimen/w80dp"
                android:layout_height="@dimen/h80dp"
                android:src="@{isOnHotMap ? @drawable/icon_hot_map_on : @drawable/icon_hot_map_off}"
                android:layout_marginTop="@dimen/h300dp"
                android:layout_marginLeft="@dimen/w16dp"/>
        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>