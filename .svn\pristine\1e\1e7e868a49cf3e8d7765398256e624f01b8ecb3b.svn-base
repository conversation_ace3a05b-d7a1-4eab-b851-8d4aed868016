/*
 * 文 件 名:  YXProgressDialog.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2013-8-13
 * 文件描述:  自定义进度对话框
 *****************************修改记录********************************
 * 
 * 修 改 者:  
 * 修改单号： [可选]
 * 修改描述:
 *
 *********************************************************************  
 */
package com.yaxon.view;

import android.app.Dialog;
import android.content.Context;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.yaxon.base.R;

/**
 * 
 * 封装自定义进度对话框
 * 
 * <AUTHOR>
 * @version   V1.0，2013-8-13
 * @see       
 * @since     V2.0.1
 */
public class YXProgressDialog2
{
    private Dialog mProgressDialog = null;
    private Context mContext = null;
    private int mLayoutId = 0;
    private int mThemeId = 0;
    private boolean mCanCancel=true;//默认可以取消
    
    public YXProgressDialog2(Context context, String msg)
    {
        mLayoutId = R.layout.progress_dialog;
        mThemeId = R.style.Theme_Progress_Dialog;
        mContext = context;
        if (null != mContext)
        {
            LayoutInflater layoutInflater = LayoutInflater.from(mContext);
            View layoutView = layoutInflater.inflate(mLayoutId, null);
            TextView msgView = (TextView)layoutView.findViewById(R.id.text_message);
            mProgressDialog = new YxTestDialog(mContext, mThemeId);             
            mProgressDialog.setContentView(layoutView);
            mProgressDialog.setCanceledOnTouchOutside(false);
            msgView.setText(msg);
                    
        }        
    }
    
    public YXProgressDialog2(Context context, int msgId)
    {
        mLayoutId = R.layout.progress_dialog;
        mThemeId = R.style.Theme_Progress_Dialog;
        mContext = context;
        if (null != mContext)
        {
            LayoutInflater layoutInflater = LayoutInflater.from(context);
            View layoutView = layoutInflater.inflate(mLayoutId, null);
            TextView msgView = (TextView)layoutView.findViewById(R.id.text_message);
            mProgressDialog = new Dialog(mContext, mThemeId);
            mProgressDialog.setContentView(layoutView);
            msgView.setText(mContext.getString(msgId));
        }  
    }
    
    public void show()
    {
        if (null != mProgressDialog)
        {            
            mProgressDialog.show();
        }       
    }
    
    public boolean isShowing()
    {
        if (null == mProgressDialog)
        {
            return false;
        }
        else
        {
            return mProgressDialog.isShowing();
        }        
    }
    
    public void dismiss()
    {
        if (null != mProgressDialog)
        {
            mProgressDialog.dismiss();
        } 
    }
    
    public void setCancel(boolean canCancel)
    {
    	mCanCancel=canCancel;
    }
    
    public class YxTestDialog extends Dialog
    {
        private final String TAG="YxTestDialog";
        
		public YxTestDialog(Context context, int theme)
		{
			super(context, theme);
			// TODO Auto-generated constructor stub
		}

		@Override
		public boolean onKeyDown(int keyCode, KeyEvent event)
		{
			// TODO Auto-generated method stub
			if(keyCode== KeyEvent.KEYCODE_BACK)
			{
				if(!mCanCancel)
				{
					Log.i(TAG, "#### canCancel=" + mCanCancel);
					return true;
				}
			}
			
			return super.onKeyDown(keyCode, event);
		}
    	
		
    }
}
