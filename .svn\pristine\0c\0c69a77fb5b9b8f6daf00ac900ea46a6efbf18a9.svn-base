<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <SurfaceView
        android:id="@+id/surfaceview_detect"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.yaxon.base.face.view.FaceRectView
        android:id="@+id/faceRectView_detect"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


    <TextView
        android:id="@+id/tv_msg_detect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:layout_margin="30dp"
        android:text="未识别"
        android:textColor="@color/red"
        android:textSize="20sp" />

    <TextView
        android:id="@+id/tv_count_detect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_margin="30dp"
        android:textColor="#ffffff"
        android:textSize="40sp"
        android:textStyle="bold"
        android:visibility="gone" />

</FrameLayout>