package com.yaxon.radio.aidl;
import com.yaxon.radio.aidl.IFmStatusResponse;

/**
 * <AUTHOR> sprint
 * @date : 21-08-03
 * @desc :
 *    	private static int STATUS_DEF_CODE = -1;//默认值
 *    	private static int STATUS_ERROR_CODE = 0;//失败
 *   	private static int STATUS_SUCCESS_CODE = 1;//成功
 *     	private static int STATUS_SEARCH_CODE = -2;//搜索中
 *     	private static int STATUS_UNOPEN_CODE = -3;//未打开
 *     	private static int STATUS_PARAM_CODE = -4;//参数异常
 */
interface IFmControlInterface {


	
	boolean reStart();
	/**
     * 重设频率
     * @param freq
     */
	int changeFreq(int freq);

 	/**
     * 切换FM、AM
     * @param type 0--switch to fm ; 1--switch to am
     * @return 
     */
	boolean switchRadioType(int type);
	/**
     * 搜索并切换到下一个或上一个有效频段
     * @param type 0： 上一个， 1： 下一个
     * @return
     */
	int changeToNeighbourValidFreq(int type);

	/**
     * 频率微调, FM步进：100, AM步进：９
     * @param type 0：向前， 1：向后
     */
    int adjustFreq(int type);

	 /**
     * 自动搜索频率
     * @param type 0:向前 1：向后
     */
	int autoSearchFreq(int type);
	
	boolean create();
	
	boolean pause();
	
	boolean destory();
	
	void registerCallBack(IFmStatusResponse response);//注册回调接口

    void unregisterCallBack(IFmStatusResponse response);//取消注册
    
    boolean isStartFm();
    /**
   * 自动搜台或打开试听
   * @param isAudition true:试听模式， false:自动搜台
   * @param isStart true 启动搜索或试听 false 停止搜索或试听
   */
    int autoSearchOrTestPref(boolean isAudition,boolean isStart);
    
    List<String> queryRecordPref();
    
    boolean clearCurRecordPref();
}
