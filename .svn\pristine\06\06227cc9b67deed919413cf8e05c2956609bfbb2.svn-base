package com.yaxon.telematics.service.communicate;

import android.util.Log;

import com.yaxon.telematics.service.util.LogUtil;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;

public class TCPServerCommunicate
{
	private final String TAG=TCPServerCommunicate.class.getName();
	
	private int mPort=0;
	private ServerSocket mServerSocket=null;
	
	private TcpAcceptThread mTcpAcceptThread=null;
	private TCPServerEvent mTCPServerEvent=null;
	
	private boolean mQuit=false;
	
	public TCPServerCommunicate(int port)
	{			
		mPort=port;
		mTcpAcceptThread=new TcpAcceptThread();
		mTcpAcceptThread.start();
	}
	
	public void destroy()
	{
		mQuit=true;
		closeServerSocket();
	}
	
	public void registerTcpServerEventListener(TCPServerEvent l)
	{
		mTCPServerEvent=l;
	}
	
	public void unRegisterTcpServerEventListener()
	{
		mTCPServerEvent=null;
	}	
	
	public boolean isServerCreated()
	{
		if(mServerSocket!=null && !mServerSocket.isClosed())
		{
			return true;
		}
		
		return false;
	}
	
	public void closeServerSocket()
	{
		if(mServerSocket==null)
		{
			return;
		}
		
		try
        {
			mServerSocket.close();
        }
        catch (IOException e)
        {
	        // TODO Auto-generated catch block
	        e.printStackTrace();
        }
		
		mServerSocket=null;
		
	}	
	
	public static interface TCPServerEvent
	{
		public void onClientConnected(Socket client);
	}
	
	private ServerSocket createServerSocket(int port)
	{
		ServerSocket s=null;
		try
        {
	        s = new ServerSocket(mPort);
        }
        catch (IOException e)
        {
	        // TODO Auto-generated catch block
	        e.printStackTrace();
        }
		
		return s;
	}
	
	private void yxSleep(int mili)
	{
		try
        {
	        Thread.sleep(mili);
        }
        catch (InterruptedException e)
        {
	        // TODO Auto-generated catch block
	        e.printStackTrace();
        }
	}
	
	public class TcpAcceptThread extends Thread
	{

		@Override
        public void run()
        {
	        // TODO Auto-generated method stub
	        super.run();
	        
	        yxSleep(30*1000);
	        Log.i(TAG, ">>>>TcpAcceptThread started");
	        
	        while(!mQuit)
	        {
	        	if(!isServerCreated())
	        	{
	        		mServerSocket=createServerSocket(mPort);	        		
	        		if(mServerSocket==null)
	        		{
	        			yxSleep(3000);
	        			continue;
	        		}
	        	}
	        	
	        	Socket s=null;
	        	try
                {
	        		Log.i(TAG, ">>>>>>>>waiting tcp client...");
	                s=mServerSocket.accept();
                }
                catch (IOException e)
                {
	                // TODO Auto-generated catch block
	                e.printStackTrace();
	                yxSleep(500);
                }
	        	
	        	if(s!=null)
	        	{
	        		if(mTCPServerEvent!=null)
	        		{
	        			mTCPServerEvent.onClientConnected(s);
	        		}
	        		Log.i(TAG, ">>>>>>>>tcp client accepted");
	        		new ReadThread(s).start();
	        		new WriteThread(s).start();
	        	}
	        }
        }
		
	}
	
//----------------------------------以下为测试代码-----------------------------------------
	
	private void closeClientSocket(Socket client)
	{
		if(client==null)return;
		
		try
        {
	        client.close();
        }
        catch (IOException e)
        {
	        // TODO Auto-generated catch block
	        e.printStackTrace();
        }
		
		client=null;
	}
	public class ReadThread extends Thread
	{
		private Socket s=null;
		private byte[] readBuf=new byte[4096];
		private int readlen=0;
		public ReadThread(Socket ss)
		{
			s=ss;
		}
		
		@Override
        public void run()
        {
	        // TODO Auto-generated method stub
	        super.run();
	        	        
        	InputStream is=null;	        	
        	try
        	{
        		is=s.getInputStream();
        		if(is==null)return;
        		
        		while(!mQuit)
        		{
        			readlen=is.read(readBuf);
        			if(readlen<0)
        			{
        				break;
        			}
        			
        			if(readlen>0)
        			{
        				LogUtil.printLogHex(TAG, ">>>>tcpserver read:", readBuf, readlen);
        			}
        		}
        	}
        	catch(Exception e)
        	{
        		e.printStackTrace();
        	}
        	
        	closeClientSocket(s);
	        	        
        }
		
	}
	
	public class WriteThread extends Thread
	{
		private Socket s=null;
		private byte[] sendBuf=new byte[256];
		public WriteThread(Socket ss)
		{
			s=ss;
		}
		
		@Override
        public void run()
        {
	        // TODO Auto-generated method stub
	        super.run();
	        
			for(int i=0;i<sendBuf.length;i++)
			{
				sendBuf[i]=(byte) (i+1);
			}
			
	        OutputStream os=null;
	        try
	        {
	        	os=s.getOutputStream();
	        	if(os==null)return;
	        	
	        	while(!mQuit)
	        	{
	        		os.write(sendBuf);
	        		LogUtil.printLogHex(TAG, ">>>>tcpserver write:", sendBuf, sendBuf.length);
	        		yxSleep(1000);
	        	}
	        }
	        catch(Exception e)
	        {
	        	e.printStackTrace();
	        }
	        
	        closeClientSocket(s);
        }
		
	}
	
	
}
