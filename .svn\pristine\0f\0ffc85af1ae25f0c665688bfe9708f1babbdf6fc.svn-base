<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/main_background">
    <!-- 标题栏 -->
    <LinearLayout
        android:id="@+id/explorer_title_bar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content">
        <include layout="@layout/main_title_simple"></include>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginLeft="@dimen/h5dp"
        android:layout_marginRight="@dimen/h5dp"
        android:layout_marginTop="@dimen/h5dp"
        android:layout_marginBottom="@dimen/h5dp"
        android:background="@drawable/main_listview_background">

        <LinearLayout
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/h12dp"
            android:layout_marginBottom="@dimen/h12dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/textView"
                android:layout_width="wrap_content"
                android:layout_height="52px"
                android:layout_weight="1"
                android:textColor="@color/white"
                android:layout_marginLeft="@dimen/w12dp"
                android:gravity="center|left"
                android:textSize="@dimen/h15sp"
                android:text="@string/main_hardware_open_wifi" />
            <ImageView
                android:id="@+id/iv_open_wifi"
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/w24dp"
                android:src="@drawable/main_state_btn_off" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="3px"
            android:layout_marginRight="3px"
            android:background="@color/listview_seperator"/>

        <ListView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="10px"
            android:layout_marginLeft="3px"
            android:layout_marginRight="3px"
            android:cacheColorHint="@android:color/transparent"
            android:divider="@color/listview_seperator"
            android:dividerHeight="1px"
            android:id="@+id/wifi_listview" />
    </LinearLayout>
</LinearLayout>