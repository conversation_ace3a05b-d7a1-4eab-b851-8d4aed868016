/*
 * 文 件 名:  MonitorProtocolBase.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2012-9-18
 * 文件描述:  调度屏协议处理类的基类MonitorProtocolDealBase，提供了处理消息、根据协议号获取应答协议、推送应答数据到应答队列、
 *          组调度屏协议帧、处理协议、循环读取应答数据等接口。
 *****************************修改记录********************************
 * 
 * 修 改 者:  
 * 修改单号： [可选]
 * 修改描述:
 *
 *********************************************************************  
 */
package com.yaxon.telematics.service.protocol.monitor;

import android.util.Log;

import com.yaxon.telematics.service.communicate.MonitorIndustryCommunicateManager;
import com.yaxon.telematics.service.protocol.ProtocolDealBase;
import com.yaxon.telematics.service.util.DataTransformer;
import com.yaxon.telematics.service.util.LogUtil;
import com.yaxon.telematics.service.util.ProtocolDataQueue;
import com.yaxon.telematics.service.util.ProtocolDataQueueElement;
import com.yaxon.telematics.service.util.StringUtil;

/**
 * 调度屏协议处理类的基类，提供了处理消息、根据协议号获取应答协议、推送应答数据到应答队列、组调度屏协议帧
 * 、处理协议、循环读取应答数据等接口。
 *
 * <AUTHOR>
 * @version V1.0.1，2012-9-18
 * @see ProtocolDealBase
 * @since V1.0.1
 */
public abstract class MonitorProtocolDealBase {
    private static final String TAG = "MonitorProtocolBase";

    /*
     * 读取接口调用应答数据的最大次数
     */
    protected static final int MAX_READ_RESPONSE_PROTOCOL_COUNTS = 10;

    /*
     * 读取应答数据时的休眠时间（默认）
     */
    protected static final long SLEEP_TIME_DEFAULT = 500;

    /*
     * 协议数据的最大长度
     */
    protected static final int MAX_PROTOCOL_DATABUF_LENGTH = 10240;

    /*
     * 接口调用的应答数据队列
     */
    protected static ProtocolDataQueue mResponseDataQueue = null;


    static {
        mResponseDataQueue = new ProtocolDataQueue("Monitor");
    }


    /**
     * 根据协议号获取协议数据
     * <p>
     * <br>
     * 备注：
     * <pre>
     * </pre>
     *
     * @param id 协议数据的协议号
     * @return 如果存在协议数据则返回ProtocolDataQueueElement对象，否则返回null
     * @see
     * @since V1.0.1
     */
    public static ProtocolDataQueueElement getProtocolDataById(int id) {
        return mResponseDataQueue.popDataByMsgId(id);
    }

    /**
     * 将接口调用产生的应答协议推送到应答队列
     * <p>
     * <br>
     * 备注：
     * <pre>
     * </pre>
     *
     * @param protocolDataQueueElement 协议数据
     * @see
     * @since V1.0.1
     */
    public static void pushResponseDataToQueue(ProtocolDataQueueElement protocolDataQueueElement) {
        mResponseDataQueue.pushData(protocolDataQueueElement);
    }

    /**
     * 封装协议数据
     * <p>
     * <br>
     * 备注：
     * <pre>
     * </pre>
     *
     * @param protocolId：协议号
     * @param userData：用户数据
     * @return 协议数据
     * @throws
     * @see
     * @since V1.0.1
     */
    protected byte[] makeProtocol(int protocolId, byte[] userData) {
        // TODO Auto-generated method stub
        //LogUtil.printLog(TAG, ">>>makeProtocol");
        // dataBuf:标志位（1）+校验位（1）+厂商编号（1）+外设类型（1）+协议号（1）+用户数据（n）+标志位（1）
        byte[] dataBuf = new byte[MAX_PROTOCOL_DATABUF_LENGTH];// 临时数据
        byte[] protocolDataBuf = null;// 保存真实的协议数据
        byte[] parityBuf = new byte[1];// 校验位 

        int dataBufLen = 2;// 协议数据的真实长度
        int parityDataBufLen = 3;// 需要计算校验位的数据长度

        // 厂商编号
        dataBuf[dataBufLen++] = (byte) 0x01;

        // 外设编号
        dataBuf[dataBufLen++] = (byte) 0x11;//120nd 的外设编号时0x11，不是0x1b
        //长沙渣土车的外设标号有点不同

        // 协议号
        dataBuf[dataBufLen++] = (byte) protocolId;

        if (null != userData) {
            //LogUtil.printLog(TAG, "null != userData");

        	/*LogUtil.printLogHex(TAG,
                    ">>> In makeProtocol",
    				userData, userData.length);*/

            parityDataBufLen += userData.length;

            // 用户数据拷贝
            System.arraycopy(userData, 0, dataBuf, dataBufLen, userData.length);

            dataBufLen += userData.length;
        } else {
            //LogUtil.printLog(TAG, "null == userData");
        }

        // 获取校验位并填充到协议数据字段
        // 校验位：厂商编号到用户数据依次累加的累加和，然后取累加的低8位作为校验码
        boolean result = DataTransformer.getLocalDataParity(dataBuf, 2, parityDataBufLen, parityBuf);
        if (result) {
            //LogUtil.printLog(TAG, "result ==  true");
            dataBuf[1] = parityBuf[0];

            // 数据转义，从校验位到用户数据都需要转义
            byte[] escapeDataBuf = DataTransformer.escapeData(dataBuf, 1, dataBufLen - 1);
            if (null != escapeDataBuf) {
                //LogUtil.printLog(TAG, "null != escapeDataBuf");
                protocolDataBuf = new byte[escapeDataBuf.length + 2];
                System.arraycopy(escapeDataBuf, 0, protocolDataBuf, 1, escapeDataBuf.length);
            } else {
                LogUtil.printLog(TAG, "null == escapeDataBuf");
                return null;
            }

            // 加上头尾标志
            protocolDataBuf[0] = (byte) 0x7E;
            protocolDataBuf[escapeDataBuf.length + 1] = (byte) 0x7E;

            return protocolDataBuf;
        } else {
            LogUtil.printLog(TAG, "result ==  false");
            return null;
        }

    }

    /**
     * 根据具体协议进行业务处理
     * <p>
     * <br>
     * 备注：
     * <pre>
     * </pre>
     *
     * @param protocolDataQueueElement 包含协议数据的对象
     * @throws
     * @see ProtocolDataQueueElement
     * @since V1.0.1
     */
    protected abstract void dealProtocol(
            ProtocolDataQueueElement protocolDataQueueElement);

    /**
     * 发送数据到110R
     * <p>
     * <br>
     * 备注：
     * <pre>
     * 调用示例代码
     * </pre>
     *
     * @param dataBuf    待发送的数据
     * @param dataLength 数据长度
     * @return true:成功；false：失败
     * @see
     * @since V1.0.1
     */
    protected boolean sendData(byte[] dataBuf, int dataLength) {
        if (null == dataBuf) {
            Log.i(TAG, "sendData: data NULL");
            return false;
        }
        return MonitorIndustryCommunicateManager.sendData(dataBuf, dataLength);
    }

    /**
     * 循环读取应答队列中的指定协议号的数据
     * <p>
     * 备注：
     * <pre>
     * </pre>
     *
     * @param msgId     协议号
     * @param readTimes 读取次数
     * @return 如果成功返回ProtocolDataQueueElement类对象，否则返回null
     * @throws
     * @see MonitorProtocolDealBase#getProtocolDataById
     * @since V1.0.1
     */
    public static ProtocolDataQueueElement readResponseProtocol(int msgId, int readTimes, long sleepTime) {
        int readCounts = 0;
        ProtocolDataQueueElement protocolDataQueueElement = null;

        if (readTimes < 0) {
            readTimes = MAX_READ_RESPONSE_PROTOCOL_COUNTS;
        }
        if (sleepTime <= 0) {
            sleepTime = SLEEP_TIME_DEFAULT;
        }


        //统一在这里加快扫描，省得调用的地方都要做修改 ++++++++++++++++++++++++++++++++++++++++
        int totalSleep = (int) (readTimes * sleepTime);//单位：ms
        if (totalSleep < 10) {
            totalSleep = 10000;//数据有误，则默认等待10s
        }

        final int SLEEP_PERIOD = 20;//扫描时间间隔20ms
        readTimes = totalSleep / SLEEP_PERIOD;
        sleepTime = SLEEP_PERIOD;

        //统一在这里加快扫描，省得调用的地方都要做修改--------------------------------------------

        while (readCounts++ < readTimes) {
            try {
                Thread.sleep(sleepTime);
            } catch (Exception e) {
                // TODO: handle exception
                e.printStackTrace();
            }

            protocolDataQueueElement = getProtocolDataById(msgId);

            if (null != protocolDataQueueElement) {
                break;
            }


        }

        return protocolDataQueueElement;
    }

    /**
     * 发送应答协议
     * <p>
     * <br>
     * 备注：
     * <pre>
     * </pre>
     *
     * @param protocolId 协议号
     * @param userData   协议的用户数据部分
     * @return true：成功；false：失败
     * @see
     * @since V1.1.2
     */
    protected boolean sendResponse(int protocolId, byte[] userData) {
        byte[] protocolData = makeProtocol(protocolId, userData);
        boolean result = false;
        if (null == protocolData) {
            return result;
        }
        Log.e(TAG, "sendResponse:" + StringUtil.byteArrayToHexString(protocolData));
        result = MonitorIndustryCommunicateManager.sendData(protocolData, protocolData.length);
        return result;
    }

    public boolean sendFrame(int protocolId, byte[] userData) {
        return sendResponse(protocolId, userData);
    }

    protected static void sleep(int mili) {
        try {
            Thread.sleep(mili);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }


}
