package com.yaxon.telematics.service.taxi.aidl;

import com.yaxon.telematics.service.annotation.ByteArrayOrm;
import com.yaxon.telematics.service.annotation.FieldType;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 调度屏查询预约大厅订单信息请求订单项定义
 * 当前时间之后，已经抢单成功的预约电召订单项
 * 字段类型	字段长度	备注
 * 订单号	4	
 * 订单所在时间段	6	北京时间，固定6字节
 *                     年：取值范围00H~FFH
 *                     月：取值范围01H~0CH
 *                     日：取值范围01H~1FH
 *                     时：取值范围00H~3BH
 *                     分：取值范围00H~3BH
 *                     秒：取值范围00H~3BH
 *                     年份是以当前年份减去2000年
 * <AUTHOR>
 */
public class YxDzHallQueryItem implements Parcelable {
	
	/** 订单ID */
	@ByteArrayOrm(id = 0, primaryLength = 4, type = FieldType.INT)
	public int orderID = 0;
	
	/** 取消原因描述内容 取消原因描述内容 */
	@ByteArrayOrm(id = 3, primaryLength = 6, type = FieldType.INT)
	public long orderTime = 0;
	
	@Override
	public int describeContents() {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public void writeToParcel(Parcel dest, int flags) {
		// TODO Auto-generated method stub
		dest.writeInt(orderID);
		dest.writeLong(orderTime);
	}
	
	public static final Parcelable.Creator<YxDzHallQueryItem> CREATOR = new Parcelable.Creator<YxDzHallQueryItem>() {

		@Override
		public YxDzHallQueryItem createFromParcel(Parcel source) {
			// TODO Auto-generated method stub
			YxDzHallQueryItem info = new YxDzHallQueryItem();

			info.orderID = source.readInt();
			info.orderTime = source.readLong();

			return info;
		}

		@Override
		public YxDzHallQueryItem[] newArray(int size) {
			// TODO Auto-generated method stub
			return new YxDzHallQueryItem[size];
		}
	};

	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return getClass().getSimpleName() + '@' + Integer.toHexString(hashCode()) + 
				"[ orderID : " + orderID + 
				", orderTime : " + orderTime + 
				" ]";
	}
}
