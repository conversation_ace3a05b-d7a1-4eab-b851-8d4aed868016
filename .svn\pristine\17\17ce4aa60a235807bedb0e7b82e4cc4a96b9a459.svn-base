package com.yaxon.adapter;

import android.content.Context;
import android.util.Log;
import android.view.View;
import com.yaxon.base.R;
import com.yaxon.base.databinding.RvItemDriverNameBinding;
import java.util.List;
import androidx.databinding.DataBindingUtil;

public class DriverNameAdapter extends BindingAdapter<String> implements View.OnClickListener {

    public DriverNameAdapter(Context context, List<String> listData) {
        super(context, listData);
    }

    @Override
    public int getConvertViewId() {
        return R.layout.rv_item_driver_name;
    }

    @Override
    public void bindViewDatas(BindingHolder holder, String entity,
                              int position) {
        RvItemDriverNameBinding binding = DataBindingUtil.getBinding(holder.itemView);
        binding.setNum(entity);

        binding.executePendingBindings();
    }

    @Override
    public void onClick(View v) {

    }

//    @Override
//    protected void onItemClick(View v, int position) {
//
//        v.setBackgroundResource(R.drawable.driver_sel);
//        notifyItemChanged(position);
//        super.onItemClick(v, position);
//    }
}

