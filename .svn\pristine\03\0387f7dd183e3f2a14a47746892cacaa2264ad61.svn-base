package com.yaxon.business.order.fragment;


import com.yaxon.base.R;
import com.yaxon.base.common.BaseFragment;
import com.yaxon.base.databinding.FragmentExecutingOrderBinding;
import com.yaxon.business.order.viewmodel.ExecutingOrderViewModel;

/**
 * 正在执行中的电召界面
 */
public class ExecutingOrderFragment extends BaseFragment<FragmentExecutingOrderBinding, ExecutingOrderViewModel> {

    public static ExecutingOrderFragment newInstance(){
        return new ExecutingOrderFragment();
    }

    @Override
    public void bindingVariable() {

    }

    @Override
    public void initParam() {
        //PostUtil.postSuccessDelayed(mLoadService);

    }

    @Override
    public int layoutId() {
        return R.layout.fragment_executing_order;
    }

    @Override
    protected String getCurTag() {
        return ExecutingOrderFragment.class.getSimpleName();
    }
}
