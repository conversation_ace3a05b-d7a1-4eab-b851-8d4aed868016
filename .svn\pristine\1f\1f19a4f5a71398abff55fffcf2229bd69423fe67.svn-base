/*
 * 文   件  名:  AudioParam.java
 * 版          权:  厦门雅迅网络股份有限公司
 * 创   建  人:  whl，2012-9-18
 * 文件描述:  定义音频参数封装类
 *****************************修改记录********************************
 * 修   改  者:  
 * 修改 单号： [可选]
 * 修改描述:
 *********************************************************************  
 */
package com.yaxon.telematics.service.aidl.dvr;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 采样参数封装类
 * <AUTHOR>
 * @version   版本号，2012-10-16
 * @see       [相关类/方法]
 * @since     [产品/模块版本]
 */

public class SamplingParam implements Parcelable
{

	/**
	 * 采样率
	 * 0x01：8000
	 * 0x02：16000
	 * 0x03：32000
	 * 0x04：44100
	 * 0x05：48000
	 */
	int mSampleRate /*= 0*/;
	/**
	 * 采样大小
	 * 0x01：4位
	 * 0x02：8位
	 * 0x03：16位
	 */
	int mSampleSize /*= 0*/;

	
	public SamplingParam(Parcel in)
	{
		mSampleRate = in.readInt();
		mSampleSize = in.readInt();
	}
	
	@Override
	public int describeContents()
	{
		// TODO Auto-generated method stub
		return 0;
	}

	/* (non-Javadoc)
	 * @see android.os.Parcelable#writeToParcel(android.os.Parcel, int)
	 */
	@Override
	public void writeToParcel(Parcel dest, int flags)
	{
		dest.writeInt(mSampleRate);
		dest.writeInt(mSampleSize);
	}

	public static final Parcelable.Creator<SamplingParam> CREATOR = new Parcelable.Creator<SamplingParam>()
	{
		@Override
		public SamplingParam createFromParcel(Parcel in)
		{
			return new SamplingParam(in);
		}

		@Override
		public SamplingParam[] newArray(int size)
		{
			return new SamplingParam[size];
		}
	};


	public int getmSampleRate() {
		return mSampleRate;
	}

	public void setmSampleRate(int mSampleRate) {
		this.mSampleRate = mSampleRate;
	}

	public int getmSampleSize() {
		return mSampleSize;
	}

	public void setmSampleSize(int mSampleSize) {
		this.mSampleSize = mSampleSize;
	}

	public SamplingParam() {
		super();
		// TODO Auto-generated constructor stub
		mSampleRate = 0;		
		mSampleSize = 0;
	}

	public SamplingParam(int mSampleRate, int mSampleSize) {
		super();
		this.mSampleRate = mSampleRate;
		this.mSampleSize = mSampleSize;
	}
	
	
	
}
