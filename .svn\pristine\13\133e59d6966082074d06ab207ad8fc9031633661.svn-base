package com.yaxon.base.maintain.video;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.DialogInterface.OnKeyListener;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.hardware.Camera;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.SurfaceHolder.Callback;
import android.view.SurfaceView;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.Toast;

import com.yaxon.base.R;
import com.yaxon.base.YXActivity;
import com.yaxon.base.YXApplication;
import com.yaxon.common.YXDefine;
import com.yaxon.common.YxClientCode;
import com.yaxon.datasource.api.ControlApi;
import com.yaxon.telematics.service.aidl.main.VideoViewInfo;
import com.yaxon.utils.YXAsyncTask;

import java.util.ArrayList;
import java.util.List;

//视频回放主页面
public class VideoPlaybackActivity extends YXActivity implements Callback, android.os.Handler.Callback
{

	private final String TAG="VideoPlaybackActivity";
	
	private boolean mPreviewing=false;
	
	private boolean mIsSurfaceCreated=false;
	
	private ImageButton mBtnExit;
	
	private Camera mCamera;
	private SurfaceView mSurfaceView;
	private SurfaceHolder mSurfaceHolder;
	
	private static final int MSG_START_PREVIEW=0x01;
	private static final int MSG_STOP_PREVIEW=0x02;
	private static final int MSG_PREVIEW_PARAM=0x03;
	Handler mHandler;

	private Dialog dialog;

	private EditText edittext;

	private ArrayList<VideoViewInfo> mVideoInfo;

	private float mPushX;

	private float mPushY;

	private float mPopX;

	private float mPopY;
	
	public int mNewChannel;
	ControlApi mControlApi = ControlApi.getInstance();

	private float mRatioLength;

	private float mRatioWidth;
	ArrayList<VideoViewInfo> mRealVideoInfo = new ArrayList<VideoViewInfo>();

	private View mView;
	
	
	
	@SuppressLint("MissingSuperCall")
	@Override
	protected void onCreate(Bundle savedInstanceState)
	{
		// TODO Auto-generated method stub
		super.onCreate(savedInstanceState, R.layout.video_pb_main);
				
		Log.i(TAG, "    VideoPlaybackActivity    onCreate");
		mView = (View)findViewById(R.id.layout_display);
		mBtnExit=(ImageButton)findViewById(R.id.title_btn_exit);
		mBtnExit.setOnClickListener(this);
		
		mSurfaceView=(SurfaceView)findViewById(R.id.surfaceViewCamera);
		mSurfaceHolder=mSurfaceView.getHolder();
		mSurfaceHolder.addCallback(this);
		
		mHandler=new Handler(this);
				
	}

	@Override
	public void onClick(View v)
	{
		// TODO Auto-generated method stub
		super.onClick(v);
		
		int id=v.getId();
		switch(id)
		{
		case R.id.btnSearch://查找视频
			if(YXDefine.IS_HIDE_PSW)
    		{
    			PasswordInput(v.getId());
	    	}else 
	    	{
	    		mStartActivity(VideoPlaybackSearchActivity.class);
	    	}
			break;
		case R.id.title_btn_exit:
			StopPreview();
			if(YxClientCode.ClientNumber == YxClientCode.Client_Yaxon) {
				//main
				//mStartActivity(HomeActivity.class);
			} else {
				finish();
			}
			break;
		}
		
	}
	
   
    private void showDialog(View view)
	{
		dialog=new Dialog(this, R.style.Theme_dialog);
		dialog.setContentView(view);
		dialog.setOnKeyListener(new OnKeyListener()
		{
			@Override
			public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event)
			{
				if(keyCode== KeyEvent.KEYCODE_BACK&&dialog!=null)
				{
					dialog.dismiss();
					dialog=null;
					edittext = null;
					return true;
				}
				return false;
			}
		});
		//显示
		dialog.show();
	}
	
    private void PasswordInput(final int btn_id)
	{
		if(dialog != null && dialog.isShowing())
		{
			return;
		}
		View view = LayoutInflater.from(this).inflate(R.layout.edit_psw_dialog, null);
		Button btn_ok = (Button) view.findViewById(R.id.btn_ok);
		Button btn_cancel = (Button) view.findViewById(R.id.btn_cancel);
		edittext = (EditText) view.findViewById(R.id.edit_param);
		btn_ok.setOnClickListener(new OnClickListener() {
			
			@Override
			public void onClick(View arg0) {
				String psw = edittext.getText().toString().trim();
				if(psw != null && !psw.equals(""))
				{
					if(psw.equals(YXDefine.PSW))
					{
						switch (btn_id) {
						case R.id.btnSearch://查询视频
							mStartActivity(VideoPlaybackSearchActivity.class);
				    		break;
						default:
							break;
						}
						if(dialog != null)
						{
							dialog.dismiss();
							dialog = null;
							edittext = null;
						}
						
					}else
					{
						Toast.makeText(VideoPlaybackActivity.this, R.string.password_fal, Toast.LENGTH_SHORT).show();
					}
				}else
				{
					Toast.makeText(VideoPlaybackActivity.this, R.string.input_password_hint, Toast.LENGTH_SHORT).show();
				}
				
				
			}
		}); 
		btn_cancel.setOnClickListener(new OnClickListener() {
			
			@Override
			public void onClick(View v) {
				// TODO Auto-generated method stub
				if(dialog != null)
				{
					dialog.dismiss();
					dialog = null;
					edittext = null;
				}
			}
		});
		showDialog(view);
	}

	/**
     * 启动其它界面
     * @param obj
     */
    private void mStartActivity(Class<?> obj)
    {
    	Intent intent=new Intent(this,obj);
		intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
		startActivity(intent);

    }
    /* 点击屏幕响应事件，点击就退出全屏显示
	 * @see android.app.Activity#onTouchEvent(android.view.MotionEvent)
	 */
	@Override
	public boolean onTouchEvent(MotionEvent event) {
		// TODO Auto-generated method stub
	     int[] origin = new int[2]; 
	        switch (event.getAction()) 
	        {
	        //触摸屏幕时刻
	        case MotionEvent.ACTION_DOWN:
	        	mPushX = event.getX();
	        	mPushY = event.getY();
//	             showXY(event.getX(), event.getY()); 获取x,y的坐标
	        break;
	        //触摸并移动时刻
	        case MotionEvent.ACTION_MOVE:
	        break;
	        //终止触摸时刻
	        case MotionEvent.ACTION_UP:
	        	int j = 0;
	        	mPopX = event.getX();
	        	mPopY = event.getY();
	        	if(mRealVideoInfo !=null) {
		        	for(j = 0; j<mRealVideoInfo.size();j++) {
		        		if((mPopX >= mRealVideoInfo.get(j).mStart_x) 
		        			&&(mPopX <(mRealVideoInfo.get(j).mStart_x+mRealVideoInfo.get(j).mPicture_lengh))
		        			&&(mPopY >= mRealVideoInfo.get(j).mStart_y) 
		        			&&(mPopY <(mRealVideoInfo.get(j).mStart_y+mRealVideoInfo.get(j).mPicture_width)))  {
		        			mNewChannel= mRealVideoInfo.get(j).mChannel;
		        			Log.i(TAG, "the mNewChannel is %d"+mNewChannel);
		        			break;
		        		} else {
		        			Log.i(TAG, "has not Channel");
		        		}	
		        	}
		        	SendPlayAllScreenTask playAllScreenTask  = new SendPlayAllScreenTask(this,mView,true,null);
	        	} else {
	        		Log.i(TAG, "mRealVideoInfo == null");
	        	}
//	            showXY(event.getX(), event.getY()); 获取x,y的坐标,并判断两次的坐标点是否都在预览范围
	        	
	        break;
	        }
		return super.onTouchEvent(event);
	}
    


	@Override
	public void surfaceChanged(SurfaceHolder holder, int format, int width, int height)
	{
		// TODO Auto-generated method stub
		Log.i(TAG, "###################surfaceChanged: format=" + format + " width=" + width + " height=" + height);
		
	}

	@Override
	public void surfaceCreated(SurfaceHolder holder)
	{
		// TODO Auto-generated method stub
		Log.i(TAG, "###############surfaceCreated");
		mIsSurfaceCreated=true;
	}

	@Override
	public void surfaceDestroyed(SurfaceHolder holder)
	{
		// TODO Auto-generated method stub
		Log.i(TAG, "######################surfaceDestroyed");
	}

	private boolean StartPreview()
	{
		if(mPreviewing)
			return false;
		
		try
		{
			mCamera= Camera.open();
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
		
		
		if(mCamera!=null)
		{
			Log.i(TAG, "init camera");
			
			try 
			{
				mCamera.setPreviewDisplay(mSurfaceHolder);
				
				Camera.Parameters params=mCamera.getParameters();
				params.setPictureFormat(PixelFormat.JPEG);
				
				List<Camera.Size> s=params.getSupportedPreviewSizes();
				
				try
				{
					if(s!=null)
					{
						for(int i=0;i<s.size();i++)
						{
							Log.i(TAG, "Supported size " + i+1 + " " + s.get(i).width + "x" + s.get(i).height );
						}
					}
					
					//params.setPreviewSize(352, 288);
					//mSurfaceHolder01.setFixedSize(352, 288);
					
					
					mCamera.setParameters(params);
					
					mCamera.setPreviewDisplay(mSurfaceHolder);
					
					mCamera.startPreview();
					
					mPreviewing=true;
					
				}
				catch(Exception e)
				{
					e.printStackTrace();
				}
				
				
			} 
			catch (Exception e)
			{
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
			
		}
		
		return true;
	}	
	
	private void StopPreview()
	{
		if(mPreviewing)
		{			
			mCamera.stopPreview();
			mCamera.release();
			mPreviewing=false;			
		}
		
	}

	

	@Override
	protected void onDestroy() 
	{
		// TODO Auto-generated method stub
		super.onDestroy();
		Log.i(TAG, "######## onDestroy");
		StopPreview();
	}


	@Override
	protected void onPause() 
	{
		// TODO Auto-generated method stub
		super.onPause();
		Log.i(TAG, "######## onPause");
		StopPreview();
	}

	

	@Override
	protected void onResume() 
	{
		// TODO Auto-generated method stub
		super.onResume();
		Log.i(TAG, "######## onResume");
		mHandler.sendEmptyMessageDelayed(MSG_START_PREVIEW, 500);//延迟1s，等surfaceview创建完毕		
		mHandler.sendEmptyMessageDelayed(MSG_PREVIEW_PARAM, 1200);//延迟获取预览参数
	}


	@Override
	public boolean handleMessage(Message msg)
	{
		// TODO Auto-generated method stub
		
		switch(msg.what)
		{
		case MSG_START_PREVIEW:
			if(mIsSurfaceCreated)
			{
				StartPreview();
			}
			else
			{
				Log.i(TAG, "######## SurfaceView not created");
			}
			break;
		case MSG_PREVIEW_PARAM:
			VideoViewInfo mRealViewInfo = null;//实际每个通道的大小
			mVideoInfo = YXApplication.getInstance().getVideoViewInfo();
			Log.i(TAG, "send MSG_PREVIEW_PARAM");
			int i =0;
			int len =0;
			if(mVideoInfo !=null) {
				len = mVideoInfo.size();
				Log.i(TAG,"The len is %d"+len);
				if(len >=1) {
					Log.i(TAG, "mVideoInfo.get(1).mSrc_video_lengh"+mVideoInfo.get(1).mSrc_video_lengh);
					Log.i(TAG, "mVideoInfo.get(1).mSrc_video_lengh"+mVideoInfo.get(1).mSrc_vide_width);
					Log.i(TAG, "*mVideoInfo.get(0.mPicture_lengh"+mVideoInfo.get(0).mPicture_lengh);
					mRatioLength = ((float)640/mVideoInfo.get(1).mSrc_video_lengh);
					mRatioWidth =( (float)480/mVideoInfo.get(1).mSrc_vide_width);		
					for(i = 0; i<len; i++) {
						mRealViewInfo.mSrc_vide_width = 480;
						mRealViewInfo.mSrc_video_lengh = 640;
						mRealViewInfo.mChannel = mVideoInfo.get(i).mChannel;
						mRealViewInfo.mPicture_lengh = (int)(mRatioLength*mVideoInfo.get(i).mPicture_lengh);
						mRealViewInfo.mPicture_width = (int)(mRatioWidth*mVideoInfo.get(i).mPicture_width);
						mRealViewInfo.mStart_x =(int)(mRatioLength*mVideoInfo.get(i).mStart_x);
						mRealViewInfo.mStart_y =(int)(mRatioWidth*mVideoInfo.get(i).mStart_y);
						mRealVideoInfo.add(mRealViewInfo);
					}
				}
			} else {
				Log.i(TAG,"##### get mVideoInfo Param is null");
			}
			break;
		}
		return false;
	}	
	
	private class SendPlayAllScreenTask extends YXAsyncTask<Void, Void, Boolean>
	{
		Context mCtx;

		/**
		 * @param context
		 * @param view
		 * @param isShowPrompt
		 * @param content
		 */
		public SendPlayAllScreenTask(Context context, View view,
                                     boolean isShowPrompt, String content) {
			super(context, view, isShowPrompt, content);
			// TODO Auto-generated constructor stub
			context = mCtx;
		}
		
		@Override
		protected Boolean doInBackground(Void... params)
		{
			
			// TODO Auto-generated method stub
			int type=0x02;
			Boolean result = false;
			try 
			{
				result=mControlApi.requestVideoAllScreen(type,mNewChannel);
			} 
			catch (Exception e)
			{
				// TODO Auto-generated catch block
				e.printStackTrace();
			}	
			
			return result;
		}
		/* (non-Javadoc)
		 * @see com.yaxon.until.YXAsyncTask#onPostExecute(java.lang.Object)
		 */
		@Override
		protected void onPostExecute(Boolean result) {
			// TODO Auto-generated method stub
			super.onPostExecute(result);
			if(result !=false) {
				Toast.makeText(mCtx, "请求全屏播放失败！", Toast.LENGTH_SHORT).show();
			} else {
				//打开对应的视频通道。
		    	Intent intent=new Intent(VideoPlaybackActivity.this,VideoPlayAllScreenActivity.class);
				intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
				startActivity(intent);
			}
		}
		
	}

	
	
}
