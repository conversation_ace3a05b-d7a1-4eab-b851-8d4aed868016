/*
 * 文 件 名:  PhoneBook.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2012-9-28
 * 文件描述:  定义电话本信息的封装类。
 *****************************修改记录********************************
 * 
 * 修 改 者:  
 * 修改单号： [可选]
 * 修改描述:
 *
 *********************************************************************  
 */
package com.yaxon.telematics.service.aidl.comm;

import java.util.ArrayList;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 
 * 封装电话本信息类，包括电话本属性、限制条件、姓名、电话号码信息。
 * 
 * <AUTHOR>
 * @version   V1.0.1，2012-9-28
 * @see       
 * @since     V1.0.1
 */
public class PhoneBook implements Parcelable
{
    /**
     * 流水号，用于标志所存储的电话本的版本
     */
    public int mFlowNo;
    
    /**
     * 属性
     * 0x00 - 删除原有的全部记录，不下发新的记录
     * 0x01 - 用本次下发的记录覆盖原有的全部记录
     * 0x02 - 将本次下发的记录追加到原有的记录后
     * 0x03 - 用本次下发的记录修改原有的对应记录
     */
    public int mAttribute = -1;
    
    /**
     * 限制条件
     * 0x01 - 只能呼入，不能呼出
     * 0x02 - 只能呼出，不能呼入
     * 0x03 - 可以出入也可以呼出
     */
    public ArrayList<Integer> mLimite = new ArrayList<Integer>();
    
    /**
     * 姓名
     */
    public ArrayList<String> mName = new ArrayList<String>();
    
    /**
     * 电话号码
     */
    public ArrayList<String> mPhoneNumber = new ArrayList<String>();
    
    @Override
    public int describeContents()
    {
        // TODO Auto-generated method stub
        return 0;
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags)
    {
        // TODO Auto-generated method stub
        dest.writeInt(mFlowNo);
        dest.writeInt(mAttribute);
        dest.writeList(mLimite);
        dest.writeList(mName);
        dest.writeList(mPhoneNumber);
    }
    
    public static final Parcelable.Creator<PhoneBook> CREATOR = new Parcelable.Creator<PhoneBook>()
    {

        @Override
        public PhoneBook createFromParcel(Parcel source)
        {
            // TODO Auto-generated method stub
            PhoneBook phoneBook = new PhoneBook();
            
            phoneBook.mFlowNo = source.readInt();
            phoneBook.mAttribute = source.readInt();
            source.readList(phoneBook.mLimite, PhoneBook.class.getClassLoader());
            source.readList(phoneBook.mName, PhoneBook.class.getClassLoader());
            source.readList(phoneBook.mPhoneNumber, PhoneBook.class.getClassLoader());
            
            return phoneBook;
        }

        @Override
        public PhoneBook[] newArray(int size)
        {
            // TODO Auto-generated method stub
            return new PhoneBook[size];
        }
        
    };
    
}
