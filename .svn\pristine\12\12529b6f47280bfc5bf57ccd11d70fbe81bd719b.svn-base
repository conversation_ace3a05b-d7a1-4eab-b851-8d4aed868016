package com.yaxon.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;

import com.yaxon.base.R;

import java.util.List;

public abstract class BindingAdapter<T> extends BaseRVAdapter<T, BindingHolder> {

    public BindingAdapter(Context context, List<T> listData) {
        super(context, listData);
    }

    @Override
    public View buildConvertView(LayoutInflater layoutInflater, ViewGroup parent, int viewType) {
        ViewDataBinding binding = DataBindingUtil.inflate(layoutInflater, getConvertViewId(), parent, false);
        if(binding == null){
            return inflate(getConvertViewId(),parent,false);
        }

        View itemView = binding.getRoot();
        itemView.setTag(R.id.dataBinding,binding);
        return  itemView;
    }


    @Override
    public BindingHolder buildHolder(View convertView, int viewType) {
        return new BindingHolder(convertView);
    }

    public abstract int getConvertViewId();
}
