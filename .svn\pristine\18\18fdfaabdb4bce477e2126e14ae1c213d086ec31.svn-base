<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_wifi_icon"
        android:layout_width="@dimen/w24dp"
        android:layout_height="@dimen/h24dp"
        android:layout_gravity="left|center_vertical"
        android:layout_marginTop="@dimen/h12dp"
        android:layout_marginBottom="@dimen/h12dp"
        android:layout_marginLeft="@dimen/w24dp"
        app:srcCompat="@drawable/icon_wifi_0" />

    <TextView
        android:id="@+id/tv_wifi_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/white"
        android:textSize="@dimen/h16sp"
        android:layout_marginLeft="@dimen/w12dp"
        android:layout_marginRight="@dimen/w12dp"
        android:layout_gravity="center_vertical"

        tools:text="wifi名称\n已经连接" />

    <ImageView
        android:id="@+id/iv_wifi_pwd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/h12dp"
        android:layout_gravity="center_vertical"
        android:src="@drawable/wifi_pwd" />

    <ImageView
        android:id="@+id/iv_wifi_detail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/h12dp"
        android:visibility="gone"
        android:layout_gravity="center_vertical"
        android:src="@drawable/ic_white_right" />
</LinearLayout>
