package com.yaxon.yx_control.taxi.kaitian;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Handler.Callback;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import com.yaxon.telematics.service.aidl.main.ExtInfo0x40;
import com.yaxon.until.ControlApi;
import com.yaxon.until.YXAsyncTask;
import com.yaxon.yx_control.R;
import com.yaxon.yx_control.YXActivity;
import com.yaxon.yx_control.YXApplication;

import java.util.HashMap;

//检定输入/输出信号
public class CheckSignalDiffActivity extends YXActivity implements Callback, OnItemClickListener {
    private final String TAG = "CheckSignalDiffActivity";
    private ControlApi mControlApi = null;

    private ListView mListView;
    private TestAdapter mAdapter;
    private LayoutInflater mInflater;
    HashMap<Integer, View> mViewMap = new HashMap<Integer, View>();
    private TextView mTxtTitle;
    private ImageView mBack;

    private Handler mHandler = null;
    private final int MSG_UPDATE_UI = 1;
    private ExtInfo0x40 mExtInfo0x40 = null;

    private int mMileagePulse = -1;

    private boolean bStarted = false;//是否有检测过，有的话，在退出的时候要发送退出指令

    int[] mMenuId = new int[]
            {
                    R.string.product_test_chengdu_taxi_2d_check_mileage_error,
                    R.string.product_test_chengdu_taxi_2d_check_speed_sensor,
                    R.string.product_test_chengdu_taxi_2d_check_real_time,
            };

    int[] mMenuHint = new int[]
            {
                    R.string.product_test_chengdu_taxi_2d_check_mileage_error_hint,
                    R.string.product_test_chengdu_taxi_2d_check_speed_sensor_hint,
                    R.string.product_test_chengdu_taxi_2d_check_real_time_hint,
            };

    int[] mMenuIcon = new int[]
            {
                    R.drawable.main_listview_point_normal,
                    R.drawable.main_listview_point_normal,
                    R.drawable.main_listview_point_normal,
            };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // TODO Auto-generated method stub
        super.onCreate(savedInstanceState, R.layout.kaitian2d_check_signal_diff);

        mControlApi = YXApplication.getInstance().mControlApi;

        mHandler = new Handler(this);

        mInflater = getLayoutInflater();
        mListView = (ListView) findViewById(R.id.list_testing_menu);
        mAdapter = new TestAdapter();
        mListView.setAdapter(mAdapter);
        mListView.setOnItemClickListener(this);

        mBack = (ImageView) findViewById(R.id.title_btn_exit);
        mBack.setOnClickListener(this);

        mTxtTitle = (TextView) findViewById(R.id.title_text_name);
        mTxtTitle.setText(R.string.product_test_chengdu_taxi_2d_check_signal);

    }


    @Override
    protected void onPause() {
        // TODO Auto-generated method stub
        super.onPause();

        mHandler.removeMessages(MSG_UPDATE_UI);

        if (bStarted) {
            CheckSignalDiffTask task = new CheckSignalDiffTask(this, mViewMap.get(0), true, null);
            task.execute(0xe4);
        }
    }


    @Override
    protected void onResume() {
        // TODO Auto-generated method stub
        super.onResume();

    }


    @Override
    public void onClick(View v) {
        // TODO Auto-generated method stub
        super.onClick(v);

        int id = v.getId();

        Log.i(TAG, ">>>>>>>>onClick:" + id);

        switch (id) {
            case R.id.title_btn_exit:
                finish();
                break;
        }

    }

    private void dealUpdateUi() {
        ExtInfo0x40 info;
        info = mControlApi.getExt0x40Info();
        if (info != null) {
            mExtInfo0x40 = info;
        }

        mAdapter.notifyDataSetChanged();

    }

    @Override
    public boolean handleMessage(Message msg) {
        // TODO Auto-generated method stub
        switch (msg.what) {
            case MSG_UPDATE_UI:
                dealUpdateUi();
                mHandler.removeMessages(MSG_UPDATE_UI);
                mHandler.sendEmptyMessageDelayed(MSG_UPDATE_UI, 2000);
                break;
            default:
                break;
        }
        return false;
    }


    private class TestAdapter extends BaseAdapter {

        @Override
        public int getCount() {
            // TODO Auto-generated method stub
            return mMenuId.length;
        }

        @Override
        public Object getItem(int arg0) {
            // TODO Auto-generated method stub
            return mViewMap.get(arg0);
        }

        @Override
        public long getItemId(int arg0) {
            // TODO Auto-generated method stub
            return arg0;
        }

        @Override
        public View getView(int position, View itemView, ViewGroup arg2) {
            // TODO Auto-generated method stub

            int menu_id = mMenuId[position];//
            itemView = mViewMap.get(position);

            ItemViewHolder holder;

            if (null == itemView) {
                itemView = mInflater.inflate(R.layout.kaitian2d_check_signal_diff_item_new, null);
                mViewMap.put(position, itemView);

                ImageView imgView = (ImageView) itemView.findViewById(R.id.project_menu_image);
                TextView txtMenu = (TextView) itemView.findViewById(R.id.project_menu_data);
                TextView txtMenuHint = (TextView) itemView.findViewById(R.id.project_menu_title);

                imgView.setBackgroundResource(mMenuIcon[position]);
                txtMenu.setText(mMenuId[position]);
                txtMenuHint.setText(mMenuHint[position]);

                holder = new ItemViewHolder();
                holder.mTxtResult = (TextView) itemView.findViewById(R.id.txtResult);
                holder.mBtnStart = (Button) itemView.findViewById(R.id.btnStart);
                holder.mBtnStop = (Button) itemView.findViewById(R.id.btnStop);
                itemView.setTag(holder);

                switch (menu_id) {
                    case R.string.product_test_chengdu_taxi_2d_check_mileage_error:
                        holder.mTxtResult.setVisibility(View.VISIBLE);
                        break;
                    default:
                        break;
                }

            } else {
                holder = (ItemViewHolder) itemView.getTag();
            }

            updateItemView(menu_id, itemView, holder);

            return itemView;
        }

    }

    private void updateItemView(int menu_id, View itemView, ItemViewHolder holder) {
        switch (menu_id) {
            case R.string.product_test_chengdu_taxi_2d_check_mileage_error:
                if (null != mExtInfo0x40) {
                    if (mMileagePulse != mExtInfo0x40.mPulseNum) {
                        mMileagePulse = mExtInfo0x40.mPulseNum;
                        holder.mTxtResult.setText("" + mMileagePulse);
                    }
                } else {
                    holder.mTxtResult.setText("未知");
                }
                break;
            default:
                break;
        }
    }

    private class ItemViewHolder {
        public TextView mTxtResult;
        public Button mBtnStart;
        public Button mBtnStop;
    }

    @Override
    public void onItemClick(AdapterView<?> arg0, View itemView, int position, long row_id) {
        // TODO Auto-generated method stub

        int menu_id = mMenuId[position];//
        bStarted = true;
        switch (menu_id) {
            case R.string.product_test_chengdu_taxi_2d_check_mileage_error://由0x40获得，自动刷新
                CheckSignalDiffTask task1 = new CheckSignalDiffTask(this, itemView, true, null);
                task1.execute(0xe1);
                mHandler.sendEmptyMessage(MSG_UPDATE_UI);
                break;
            case R.string.product_test_chengdu_taxi_2d_check_speed_sensor://车速传感器信号
                CheckSignalDiffTask task2 = new CheckSignalDiffTask(this, itemView, true, null);
                task2.execute(0xe2);
                break;
            case R.string.product_test_chengdu_taxi_2d_check_real_time://实时时钟信号
                CheckSignalDiffTask task3 = new CheckSignalDiffTask(this, itemView, true, null);
                task3.execute(0xe3);
                break;
            default:
                break;
        }

    }

    //检定输入输出信号
    //type:  0xe1:里程误差测量(指令发送成功后，刷新0x40里的里程脉冲字段)  0xe2:车速传感器信号  0xe3:实时时钟信号
    //退出信号检定时，发送0xE4通知车台停止检定
    private class CheckSignalDiffTask extends YXAsyncTask<Integer, Void, Boolean> {
        int type = 0;
        Context ctx;

        public CheckSignalDiffTask(Context context, View view,
                                   boolean isShowPrompt, String content) {
            super(context, view, isShowPrompt, content);
            // TODO Auto-generated constructor stub
            ctx = context;
        }

        @Override
        protected Boolean doInBackground(Integer... params) {
            // TODO Auto-generated method stub
            super.doInBackground(params);

            if (params != null) {
                type = params[0];
            }

            return mControlApi.checkSignalDiff(type);

        }

        @Override
        protected void onPostExecute(Boolean result) {
            // TODO Auto-generated method stub
            super.onPostExecute(result);

            String strPrompt = "设置 ";

            switch (type) {
                case 0xe1://里程脉冲误差检测
                    strPrompt += "里程误差测量";
                    break;
                case 0xe2://车速传感器信号
                    strPrompt += "车速传感器信号";
                    break;
                case 0xe3://实时时钟信号
                    strPrompt += "实时时钟信号";
                    break;
                case 0xe4://退出检测
                    strPrompt = "停止检测";
                    break;
            }

            if (result) {
                strPrompt += "成功";
            } else {
                strPrompt += "失败";
            }

            Toast.makeText(ctx, strPrompt, Toast.LENGTH_SHORT).show();

        }


    }

}
