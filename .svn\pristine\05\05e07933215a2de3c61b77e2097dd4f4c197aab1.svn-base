package com.yaxon.telematics.service.taxi.aidl;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * @description 2017-9-13下午2:04:40
 * @com.yaxon.telematics.service.taxi.aidl
 */
public class YxFingerprintAnsInfo implements Parcelable {
    /* 操作类型 */
    public int type = 0;
    /* 应答结果 */
    public int result = 0;

    @Override
    public int describeContents() {
        // TODO Auto-generated method stub
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        // TODO Auto-generated method stub
        dest.writeInt(type);
        dest.writeInt(result);
    }

    public static final Parcelable.Creator<YxFingerprintAnsInfo> CREATOR = new Parcelable.Creator<YxFingerprintAnsInfo>() {

        @Override
        public YxFingerprintAnsInfo createFromParcel(Parcel source) {
            // TODO Auto-generated method stub
            YxFingerprintAnsInfo ansInfo = new YxFingerprintAnsInfo();
            ansInfo.type = source.readInt();
            ansInfo.result = source.readInt();
            return ansInfo;
        }

        @Override
        public YxFingerprintAnsInfo[] newArray(int size) {
            // TODO Auto-generated method stub
            return new YxFingerprintAnsInfo[size];
        }
    };

}
