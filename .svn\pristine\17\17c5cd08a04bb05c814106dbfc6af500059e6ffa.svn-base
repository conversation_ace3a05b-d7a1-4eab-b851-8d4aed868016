<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

   <data>
      <import type="com.yaxon.utils.StringUtil"/>
      <variable
          name="isSelected"
          type="boolean" />

      <variable
          name="fmChannel"
          type="String" />

      <variable
          name="fmChannelName"
          type="String" />
   </data>

   <LinearLayout
       android:layout_width="@dimen/w144dp"
       android:layout_height="match_parent"
       android:orientation="vertical"
       android:background="@{isSelected ? @drawable/shape_gray_with_circle_corner : @drawable/shape_grab_order_normal }">

      <TextView
          android:id="@+id/tvFmChannel"
          style="@style/Yx_Textview_White_20"
          android:layout_marginLeft="@dimen/w16dp"
          android:layout_marginTop="@dimen/h16dp"
          android:textStyle="bold"
          android:textColor="@{isSelected ? @color/black : @color/white}"
          android:text="@{fmChannel}"/>

      <TextView
          android:id="@+id/tvUnit"
          style="@style/Yx_Textview_White_20"
          android:layout_marginLeft="@dimen/w16dp"
          android:text="@string/media_fm_unit"
          android:textSize="@dimen/w12sp"
          android:textColor="@{isSelected ? @color/gray : @color/white}"/>

      <TextView
          android:id="@+id/tvFmChannelName"
          style="@style/Yx_Textview_White_20"
          android:layout_marginTop="@dimen/h25dp"
          android:layout_marginLeft="@dimen/w16dp"
          android:singleLine="true"
          android:text="@{StringUtil.isNotNullOrEmpty(fmChannelName) ? fmChannelName : @string/fal_load}"
          android:textSize="@dimen/w12sp"
          android:textColor="@{isSelected ? @color/black : @color/white}"/>
   </LinearLayout>
</layout>

