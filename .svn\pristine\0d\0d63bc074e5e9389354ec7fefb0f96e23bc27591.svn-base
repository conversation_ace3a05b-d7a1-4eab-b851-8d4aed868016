/*
 * 文 件 名:  DriverInfo.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2013-4-25
 * 文件描述:  定义驾驶员信息封装类。
 *****************************修改记录********************************
 * 
 * 修 改 者:  
 * 修改单号： [可选]
 * 修改描述:
 *
 *********************************************************************  
 */
package com.yaxon.telematics.service.aidl.main;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 
 * 封装电子服务证信息的各个字段，包括
 * 
 * <AUTHOR> @version   V1.0，2013-10-31
 * @see       
 * @since     V1.1.2
 */
public class DianZhaoXMSpecialDriverInfo implements Parcelable
{
	/**
	 * 登录（签到）状态
	 * 0x01:登录/签到
	 * 0x02:登出/签退
	 */
	public int mLogStatus = 0;    
    /**
     * 司机工号
     */
    public int mDriverId = 0;
    /**
     * 司机姓名
     */
    public String mDriverName = null;
    /**
     * 司机所属公司
     */
    public String mDriverCompany= null;
    /**
     * 投诉电话
     */
    public String mPhone= null;
    /**
     *司机车牌号
     */
    public String mCarNum= null;
    
    @Override
    public int describeContents()
    {
        // TODO Auto-generated method stub
        return 0;
    }
    @Override
    public void writeToParcel(Parcel dest, int flags)
    {
        // TODO Auto-generated method stub
    	dest.writeInt(mLogStatus);
    	dest.writeInt(mDriverId);
        dest.writeString(mDriverName);
        dest.writeString(mDriverCompany);
        dest.writeString(mPhone);
        dest.writeString(mCarNum);
    }
    
    public static final Parcelable.Creator<DianZhaoXMSpecialDriverInfo> CREATOR = new Parcelable.Creator<DianZhaoXMSpecialDriverInfo>()
    {

        @Override
        public DianZhaoXMSpecialDriverInfo createFromParcel(Parcel source)
        {
            // TODO Auto-generated method stub
            DianZhaoXMSpecialDriverInfo driverInfo = new DianZhaoXMSpecialDriverInfo();
            driverInfo.mLogStatus = source.readInt();
            driverInfo.mDriverId = source.readInt();
            driverInfo.mDriverName = source.readString();
            driverInfo.mDriverCompany = source.readString();
            driverInfo.mPhone = source.readString();
            driverInfo.mCarNum = source.readString();
            return driverInfo;
        }

        @Override
        public DianZhaoXMSpecialDriverInfo[] newArray(int size)
        {
            // TODO Auto-generated method stub
            return new DianZhaoXMSpecialDriverInfo[size];
        }
        
    };
}
