<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal">

    <ProgressBar
        android:id="@+id/pb_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminateDrawable="@drawable/rotate_rv_loading" />

    <TextView
        android:id="@+id/tv_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/w15dp"
        android:text="@string/xm_rv_footer_loading_tip"
        android:textColor="@color/white"
        android:textSize="@dimen/w22sp" />

    <LinearLayout
        android:id="@+id/ll_end"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/w10dp"
        android:layout_marginRight="@dimen/w10dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="gone">

        <View
            android:layout_width="0dp"
            android:layout_height="@dimen/w1dp"
            android:layout_weight="1"
            android:alpha="0.5"
            android:background="@color/white"
            android:gravity="start" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="@dimen/w15dp"
            android:text="@string/xm_rv_footer_end_tip"
            android:textColor="@color/white"
            android:textSize="@dimen/w22sp" />

        <View
            android:layout_width="0dp"
            android:layout_height="@dimen/w1dp"
            android:layout_weight="1"
            android:alpha="0.5"
            android:background="@color/white"
            android:gravity="end" />

    </LinearLayout>

</LinearLayout>