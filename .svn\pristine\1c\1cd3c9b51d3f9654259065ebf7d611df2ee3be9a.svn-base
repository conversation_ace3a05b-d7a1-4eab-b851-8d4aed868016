package com.yaxon.datasource.database;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

/**
 * 数据库服务，创建表
 * <AUTHOR>
 * @version 2013-4-27
 */
public class  DB<PERSON>elper extends SQLiteOpenHelper
{
	private static final String DB_NAME="nd120.db";//数据库名称
	private static int DB_VERSION=2;//数据库版本
	
	private static DBHelper INSTANCE = null;
	
	private DBHelper(Context context)
	{
		super(context,DB_NAME,null,DB_VERSION);
	}
	
	public static synchronized DBHelper getInstance(Context context) {
		if (INSTANCE == null) {
			INSTANCE = new DBHelper(context);
		}
		return INSTANCE;
	}

	@Override
	public void onCreate(SQLiteDatabase db)
	{
/*************************************************************************************
 * ***********************************************************************************
 * 修改表项时，一定注意不要随便打乱已经定义的字段的顺序，因为代码里很多都是用字段的偏移量来获取字段的值 *
 * 如果要添加字段时，要添加到末尾***********************************************************
 **************************************************************************************/
		//调度信息: 协议号：0x6B
		String attempterSql="create table attempter(time varchar,type integer,content varchar,state integer,_id integer,flowno integer,PRIMARY KEY (_id) )";
		//中心提问
		String questionSql="create table question(question_index integer,question_content varchar,answer_content varchar,is_answer integer,rec_time varchar,_id integer,PRIMARY KEY (_id) )";
		//保存中心提问的答案，之前的保存有问题，因此建这张表来表存所有问题的答案
		String qAnsSql="create table question_ans(question_index integer,answer_content varchar,answer_index integer,_id integer,PRIMARY KEY (_id) )";
		//电召sql
//		String callTaxiSql="CREATE TABLE IF NOT EXISTS dianzhao"
//				+ " ("
//				+ "_id INTEGER NOT NULL,"
//				+ "ordernum INTEGER NOT NULL,"  //订单号
//				+ "ordertype INTEGER NOT NULL,"  //订单类型
//				+ "needcartime VARCHAR NOT NULL,"   //要车时间
//				+ "needcardes VARCHAR,"    //要车描述信息 即电召描述信息0x75
//				+ "passengernum VARCHAR,"  //乘客电话
//				+ "confirmdes VARCHAR,"  //抢答确认 描述信息 0x77
//				+ "result INTEGER,"  //收到电召请求（待抢答）0x00，    抢单发送成功：0x01；   抢单发送失败:0x02；         司机超时未处理电召请求0x03；
//				//司机点击放弃0x04;   中心确认电召:0x05  ，电召执行成功：0x06，  司机取消：0x07，    中心取消：0x08
//				+ "canceltype INTEGER,"  //电召取消类型， 事故：0x00，路堵：0x01，其它：0x02
//				+ "reporttime VARCHAR,"  //电召结果上报时间，只保存成功、取消的时间
//				+ "recv_ordertime VARCHAR,"  //收到电召的时间 格式：整数 (转成Date对象后的毫秒值)
//				+ "latitu INTEGER,"		 //纬度
//				+ "longi INTEGER,"		 //经度
//				+ "showflag INTEGER,"		 //显示的标志
//				+ "navtype  INTEGER,"      //导航类型
//				+ "positionshow VARCHAR,"      //位置描述
//				+ "des_latitu INTEGER,"//终点纬度
//				+ "des_longi INTEGER,"//终点经度
//				+ "fee INTEGER,"//电召费
//				+ "exe_time BIGINT,"//抢单成功后的时间点
//				+ "PRIMARY KEY (_id)"
//				+ ");";
		//执行创建表
		db.execSQL(attempterSql);
		db.execSQL(questionSql);
		db.execSQL(qAnsSql);
		//db.execSQL(callTaxiSql);
	}

	@Override
	public void onUpgrade(SQLiteDatabase db, int arg1, int arg2)
	{
		String attempterSql="drop table if exists attempter";
		String questionSql="drop table if exists question";
		String questionAnsSql="drop table if exists question_ans";
//		String callTaxiSql="drop table if exists dianzhao";
		db.execSQL(attempterSql);
		db.execSQL(questionSql);
		db.execSQL(questionAnsSql);
//		db.execSQL(callTaxiSql);
	}
}
