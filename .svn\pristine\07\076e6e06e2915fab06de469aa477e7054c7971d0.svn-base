package com.yaxon.telematics.service.taxi.aidl;

import android.content.Context;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.format.DateUtils;

import com.yaxon.telematics.service.annotation.ByteArrayOrm;
import com.yaxon.telematics.service.annotation.FieldType;
import com.yaxon.telematics.service.taxi.TaxiConstDef;

/**
 * 字段			类型	描述及要求
 * 订单号			4	平台下发的电召订单号
 * 电召类型		1	0x01:即时电召   0x02:预约电召
 * 要车时间		6	北京时间，固定6字节
 *                     年：取值范围00H~FFH
 *                     月：取值范围01H~0CH
 *                     日：取值范围01H~1FH
 *                     时：取值范围00H~3BH
 *                     分：取值范围00H~3BH
 *                     秒：取值范围00H~3BH
 *                   年份是以当前年份减去2000年。
 *                 
 * 乘客上车位置经度		4	以度为单位的经度值乘以10的6次方，精确到百万分之一度
 * 乘客上车位置纬度		4	以度为单位的纬度值乘以10的6次方，精确到百万分之一度
 * 
 * 下列两个字段为订单详情信息下发和查询历史订单信息特有的字段
 * 乘客目的地位置经度	4	以度为单位的经度度值乘以10的6次方，精确到百万分之一度
 * 乘客目的地位置纬度	4	以度为单位的纬度值乘以10的6次方，精确到百万分之一度
 * 
 * 乘客上车位置地址长度		1	假定长度为N
 * 乘客上车位置地址内容		N	GBK编码
 * 乘客目的地位置地址长度	1	假定长度为N1
 * 乘客目的地位置地址内容	N1	GBK编码
 * 
 * 下列字段为
 * 
 * 下列字段为订单详情信息下发增加的字段
 * 乘客电话号码长度	1	假定乘客电话号码长度为N3
 * 乘客电话	N3	ASCII字符
 * 乘客姓名长度	1	假定乘客姓名长度为N4
 * 乘客姓名内容	N4	ASCII码
 * 
 * 下列字段为中心同步电召订单状态新增字段
 * 电召状态	1	0x00：超时
 *              0x01：执行失败
 *              0x02：执行成功
 *              0x03：乘客取消
 *              0x04：司机取消
 *              0x05：待执行
 *              0x06：执行中
 *              
 * 下列字段为查询历史订单信息新增字段
 * 订单金额	4	单位：分。
 * 
 * <AUTHOR>
 *
 */
public class YxDzOrderInfo implements Parcelable {
	
	/** 订单ID */
	@ByteArrayOrm(id = 0, primaryLength = 4, type = FieldType.INT)
	public int orderID = 0;
	
	/** 电召类型 */
	@ByteArrayOrm(id = 1, primaryLength = 1, type = FieldType.INT)
	public int orderType = TaxiConstDef.ORDER_TYPE_UNKNOW;

	/** 要车时间 */
	@ByteArrayOrm(id = 2, primaryLength = 6, type = FieldType.COUSTOM_PROCESSOR)
	public String orderTimeStamp = "";
	
	/** 乘客上车位置经度 Longitude */
	@ByteArrayOrm(id = 3, primaryLength = 4, type = FieldType.INT)
	public int passengerLng = 0;

	/** 乘客上车位置纬度 Latitude */
	@ByteArrayOrm(id = 4, primaryLength = 4, type = FieldType.INT)
	public int passengerLat = 0;

	/** 乘客目的地位置经度 Longitude */
	@ByteArrayOrm(id = 5, primaryLength = 4, type = FieldType.INT)
	public int destinationLng = 0;

	/** 乘客目的地位置纬度 Latitude */
	@ByteArrayOrm(id = 6, primaryLength = 4, type = FieldType.INT)
	public int destinationLat = 0;
	
	/** 乘客上车位置地址 */
	@ByteArrayOrm(id = 7, type = FieldType.LSTRING)
	public String srcAddr = "";

	/** 乘客目的地位置地址 */
	@ByteArrayOrm(id = 8, type = FieldType.LSTRING)
	public String desAddr = "";

	/** 乘客电话 该字段为订单详情信息 */
	@ByteArrayOrm(id = 9, type = FieldType.LSTRING)
	public String passengerPhone = "";

	/** 乘客姓名 该字段为订单详情信息 */
	@ByteArrayOrm(id = 10, type = FieldType.LSTRING)
	public String passengerName = "";

	/** 车载播放电召文本信息标识 */
	@ByteArrayOrm(id = 11, primaryLength = 1, type = FieldType.INT)
	public int ttsPlayFlag = 0;
	
	/** 电召描述信息  */
	@ByteArrayOrm(id = 12, type = FieldType.LSTRING)
	public String orderDescription = "";
	
	/** 订单里程 实际或者预估 */
	@ByteArrayOrm(id = 13, primaryLength = 2, type = FieldType.INT)
	public int tradeMileage = -1;
	
	/** 订单金额 实际或者预估 */
	@ByteArrayOrm(id = 14, primaryLength = 2, type = FieldType.INT)
	public int tradeVal = -1;

	@ByteArrayOrm(id = 15, primaryLength = 6, type = FieldType.COUSTOM_PROCESSOR)
	public String uniformOrderNum = "";
	
	/** 电招状态 该字段为中心同步电召订单状态 */
	public int orderState = TaxiConstDef.ORDER_STATE_UNKNOW;

    /** 订单时间 */
    //public String friendlyTimeStamp = "";
    public long timestamp = 0;
    
	@Override
	public int describeContents() {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public void writeToParcel(Parcel dest, int flags) {
		// TODO Auto-generated method stub
		dest.writeInt(orderID);
		dest.writeInt(orderType);
		dest.writeString(orderTimeStamp);
		dest.writeInt(passengerLng);
		dest.writeInt(passengerLat);
		dest.writeInt(destinationLng);
		dest.writeInt(destinationLat);
		dest.writeString(srcAddr);
		dest.writeString(desAddr);
		dest.writeString(passengerPhone);
		dest.writeString(passengerName);
		dest.writeInt(tradeMileage);
		dest.writeInt(tradeVal);
		dest.writeString(uniformOrderNum);
		dest.writeInt(orderState);
        //dest.writeString(friendlyTimeStamp);
        dest.writeLong(timestamp);
	}

	public static final Parcelable.Creator<YxDzOrderInfo> CREATOR = new Parcelable.Creator<YxDzOrderInfo>() {

		@Override
		public YxDzOrderInfo createFromParcel(Parcel source) {
			// TODO Auto-generated method stub
			YxDzOrderInfo info = new YxDzOrderInfo();

			info.orderID = source.readInt();
			info.orderType = source.readInt();
			info.orderTimeStamp = source.readString();
			info.passengerLng = source.readInt();
			info.passengerLat = source.readInt();
			info.destinationLng = source.readInt();
			info.destinationLat = source.readInt();
			info.srcAddr = source.readString();
			info.desAddr = source.readString();
			info.passengerPhone = source.readString();
			info.passengerName = source.readString();
			info.tradeMileage = source.readInt();
			info.tradeVal = source.readInt();
			info.uniformOrderNum = source.readString();
			info.orderState = source.readInt();
			//info.friendlyTimeStamp = source.readString();
            info.timestamp = source.readLong();

			return info;
		}

		@Override
		public YxDzOrderInfo[] newArray(int size) {
			// TODO Auto-generated method stub
			return new YxDzOrderInfo[size];
		}
	};
	
	@Override
	public boolean equals(Object o) {
		if(this == o)
			return true;
		
		if(!(o instanceof YxDzOrderInfo))
			return false;
		
		YxDzOrderInfo info = (YxDzOrderInfo) o;
		
		return this.orderID == info.orderID && this.orderType == info.orderType;
	}

	public String getBriefInfo() {
		return TaxiConstDef.getOrderTypeName(orderType) + "：" + orderID;
	}
	
	public boolean isExecuting() {
		return orderState == TaxiConstDef.ORDER_STATE_EXECUTING_TO_PASSENGER || 
				orderState == TaxiConstDef.ORDER_STATE_EXECUTING_TO_DESTINATION;
	}
	
	public boolean isToBeExecute() {
		return orderType == TaxiConstDef.ORDER_TYPE_RESERVATION &&
				orderState == TaxiConstDef.ORDER_STATE_TO_BE_EXEC;
	}
	
	public String getFriendTimeStamp(Context context) {
	    
        String ret = null;
        if(timestamp > 0){
            CharSequence charSequence = DateUtils.getRelativeDateTimeString(context,
                    timestamp, DateUtils.MINUTE_IN_MILLIS, DateUtils.WEEK_IN_MILLIS,
                    DateUtils.FORMAT_SHOW_TIME | DateUtils.FORMAT_SHOW_WEEKDAY | 
                    DateUtils.FORMAT_SHOW_YEAR | DateUtils.FORMAT_NO_YEAR);
            
            if(charSequence != null)
                ret = charSequence.toString();
        }
        
        if(ret != null){
            return ret;
        } else {
            return orderTimeStamp;
        }
        
    }
	
	public void set(YxDzOrderInfo target){
	    this.orderID = target.orderID;
	    this.orderType = target.orderType;
	    this.orderTimeStamp = target.orderTimeStamp;
	    this.srcAddr = target.srcAddr;
	    this.desAddr = target.desAddr;
	    this.passengerLat = target.passengerLat;
        this.passengerLng = target.passengerLng;
        this.passengerName = target.passengerName;
        this.passengerPhone = target.passengerPhone;
        this.destinationLat = target.destinationLat;
        this.destinationLng = target.destinationLng;
        this.orderDescription = target.orderDescription;
        this.tradeMileage = target.tradeMileage;
        this.tradeVal = target.tradeVal;
        this.uniformOrderNum = target.uniformOrderNum;
        //this.friendlyTimeStamp = target.friendlyTimeStamp;
        this.timestamp = target.timestamp;
	}

	@Override
	public String toString() {
		return "YxDzOrderInfo [orderID=" + orderID + ", orderType=" + orderType
				+ ", orderTimeStamp=" + orderTimeStamp + ", passengerLng="
				+ passengerLng + ", passengerLat=" + passengerLat
				+ ", destinationLng=" + destinationLng + ", destinationLat="
				+ destinationLat + ", srcAddr=" + srcAddr + ", desAddr="
				+ desAddr + ", passengerPhone=" + passengerPhone
				+ ", passengerName=" + passengerName + ", ttsPlayFlag="
				+ ttsPlayFlag + ", orderDescription=" + orderDescription
				+ ", tradeMileage=" + tradeMileage + ", tradeVal=" + tradeVal + ", uniformOrderNum=" + uniformOrderNum
				+ ", orderState=" + orderState + ", timestamp=" + timestamp
				+ "]";
	}
	
	
}
