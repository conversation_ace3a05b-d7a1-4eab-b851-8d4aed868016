/*
 * 文 件 名:  BroadcastSend.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2012-9-13
 * 文件描述:  定义广播发送类
 *****************************修改记录********************************
 *
 * 修 改 者: lrm
 * 修改单号：
 * 修改日期: 2013.1.21
 * 修改描述: 增加函数sendOrderedBroadcast
 *
 *********************************************************************
 */
package com.yaxon.telematics.service.broadcast;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import com.yaxon.telematics.service.ComuServiceApp;

/**
 * 提供广播发送方法
 *
 * <AUTHOR>
 * @version V1.0.1，2012-9-13
 * @see
 * @since V1.0.1
 */
public class BroadcastSend {
    private static Context mContext = ComuServiceApp.getInstance().getApplicationContext();

    /**
     * 发送普通广播
     * <p>
     * <br>
     * 备注：
     * <pre>
     * </pre>
     *
     * @param intent Intent对象
     * @return true：成功；false：失败
     * @throws
     * @see
     * @since V1.0.1
     */
    public static boolean sendBroadcast(Intent intent) {
        if (null != mContext && null != intent) {
            Log.i("sendBroadcast", intent.getAction());
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
            }
            mContext.sendBroadcast(intent);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 发送有序广播
     * <p>
     * <br>
     * 备注：
     * <pre>
     * </pre>
     *
     * @param intent             Intent对象
     * @param receiverPermission 广播接收者需要的权限，如果为null，则不需要权限
     * @return true：成功；false：失败
     * @throws 
     * @see
     * @since V1.1.2
     */
    public static boolean sendOrderedBroadcast(Intent intent, String receiverPermission) {
        if (null != mContext && null != intent) {
            mContext.sendOrderedBroadcast(intent, receiverPermission);
            return true;
        } else {
            return false;
        }
    }
}
