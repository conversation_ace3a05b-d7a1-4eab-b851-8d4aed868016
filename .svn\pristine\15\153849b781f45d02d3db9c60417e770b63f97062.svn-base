package com.yaxon.base.maintain.recorder;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.yaxon.base.R;
import com.yaxon.datasource.api.ControlApi;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * 行驶记录仪列界面
 * <AUTHOR>
 * @version   版本号，2013-5-7
 * @see       [相关类/方法]
 * @since     [产品/模块版本]
 */
public class DrivingDataAdapter extends BaseAdapter {
	
	private Context context;
	
	private ArrayList<Data> data_list = new ArrayList<Data>(); //数据源
	private HashMap<Integer, View> view_map = new HashMap<Integer, View>();//控件map
	private HashMap<Integer, TextView> text_map = new HashMap<Integer, TextView>();//Textview控件map
//	private ArrayList<String> titile_list = new ArrayList<String>();//提示数据源
	
	private LayoutInflater inflater;
	
	TextView text_title;
	
	
	public DrivingDataAdapter(Context context, ArrayList<Data> data_list) {
		super();
		this.context = context;
		setList(data_list);
//		titile_list.add(GetString(context, R.string.car_classification));
//		titile_list.add(GetString(context, R.string.load_state));
//		titile_list.add(GetString(context, R.string.driver_no));
//		titile_list.add(GetString(context, R.string.print_time));
		inflater = (LayoutInflater)context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
	}

	@Override
	public int getCount() {
		// TODO Auto-generated method stub
		return data_list.size();
	}

	@Override
	public Object getItem(int position) {
		// TODO Auto-generated method stub
		
		return position;
	}

	@Override
	public long getItemId(int position) {
		// TODO Auto-generated method stub
		return position;
	}

	@Override
	public View getView(int position, View convertView, ViewGroup parent) {
		// TODO Auto-generated method stub
		convertView = view_map.get(position);
		Data data = data_list.get(position);
		TextView text_data;
			int left = 30;
			switch (position) {
			case 0:
				convertView = inflater.inflate(R.layout.driving_data_title, null);
				left = 30;
				break;
			default:
				
				if(data.title.equals("#15#"))
				{
					convertView = inflater.inflate(R.layout.driving_data_title, null);
					left = 30;
				}else if(data.title == null || data.title.equals(""))
				{
					convertView = inflater.inflate(R.layout.driving_data_title, null);
					left = 160;
				}else
				{
					
					convertView = inflater.inflate(R.layout.driving_data_item, null);
					left = 0;
					text_title = (TextView) convertView.findViewById(R.id.text_title);
					
					if(data.title.equals("#1#"))
					{
						text_title.setVisibility(View.GONE);
					}else if(data.title.equals("#nodata#"))
					{
						text_title.setText(data.data);
						text_title.setVisibility(View.VISIBLE);
					}else
					{
						text_title.setText(data.title);
						text_title.setVisibility(View.VISIBLE);
					}
				}
				break;
			}
			text_data = (TextView) convertView.findViewById(R.id.text_data);
			
			if(data.title.equals("#nodata#"))
			{
				text_data.setVisibility(View.GONE);
			}else
			{
				text_data.setText(data.data);
				text_data.setVisibility(View.VISIBLE);
			}
			
		
			if(position == 0)
			{
				text_data.setBackgroundResource(R.drawable.listitem_bg);
				text_data.setTextSize(24);
			}else
			{
				text_data.setHeight(40);
			}
			text_data.setPadding(left, 0, 0, 0);
			text_map.put(position, text_data);
			view_map.put(position, convertView);
			
		return convertView;
	}

	
	private String GetString(Context context, int resId)
	{
		return context.getString(resId);
	}
	
	public void notifyDataSetChanged(ArrayList<Data> data_list) {
		view_map.clear();
		setList(data_list);
		super.notifyDataSetChanged();
	}
	
	//重新设置数据
	private void setList(ArrayList<Data> data_list)
	{
		this.data_list = data_list;
//		System.out.println("size=============================="+data_list.size());
		for (int i = 0; i < data_list.size(); i++) {
//			System.out.println("========================="+data_list.get(i));
		}
		data_list.add(0, new Data("", GetString(context, R.string.Driving_data)));
		if(this.data_list.size() == 1)
		{
			if(ControlApi.isUse)
			{
				this.data_list.add(1, new Data("#1#", GetString(context, R.string.load_data)));
			}else
			{
				this.data_list.add(1, new Data("#1#", GetString(context, R.string.load_fal)));
			}
		}
		System.out.println(this.data_list.size());
	}
	
	public void updataLoadText(String str)
	{
		text_map.get(1).setText(str);
	}
	
	
	public  static class Data{
		public String title;
		public String data;
		
		public Data() {
			// TODO Auto-generated constructor stub
		}
		
		public Data(String title, String data) {
			this.title = title;
			this.data = data;
		}
	}
}
