<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

    <string name="app_name">YX_MainInterface_120ND_Taxi_Master_Uniform</string>
    <!--
   归档版本号格式：客户编号版本号 + 设备型号 + 协议号 + 行业类型 + 系统版本号
   例如：yx001-120nd712-702-cz2-1.01
   yx001     120nd712    702     cz2      1.01
     |           |        |       |        |
   客户编号    设备型号    协议号   行业类型  系统版本号
   (基线版yx)  (分体712) (两标协议)(出租车行业二期)
   (版本号001)
   -->
    <!--      <string name="app_ver_name">yx007-tu737s011-901-cz3</string>-->

    <!--部标版本号-->
    <!--    <string name="app_ver_name">yx009-meigs01-702</string>-->
    <!---->
    <string name="app_ver_name">c1608009-meigs01-901-cz4</string>
    <!-- 分体屏 请切换到此版本
         基线版
    <string name="app_ver_name">yx001-120nd712-702-cz2</string>
    <string name="app_ver_name">yx004-120nd705-702-cz2</string>
    <string name="app_ver_name">yx012-120nd725-702-cz2</string>
    <string name="app_ver_name">yx00f-120nd755-702-cz2</string>
    -->

    <!--px3基线版 请切换到此版本
     <string name="app_ver_name">yx011-tu737s010-901-cz2</string>
    -->

    <!-- 一体机 请切换到此版本
    <string name="app_ver_name">yx011-tu600v62-702-cz2</string>
    -->


    <string name="back_car_notify">请检查周围情况，确保安全！</string>
    <!-- 相同 -->
    <string name="main_confirm">确定</string>
    <string name="main_cancel">取消</string>
    <string name="dialog_title">提示信息</string>
    <string name="exit">返回</string>
    <string name="new_data_now">这已经是最新一条信息</string>
    <string name="old_data_now">这已经是最后一条信息</string>
    <string name="blank"> </string>
    <string name="common_yes">是</string>
    <string name="common_no">否</string>
    <!-- 主界面相关 -->
    <string name="main_main_entertainment">影音娱乐</string>
    <string name="main_main_message">信息服务</string>
    <string name="main_main_log">行驶记录</string>
    <string name="main_main_function">功能业务</string>
    <string name="main_main_set">系统设置</string>
    <string name="xiamen_main_date_format">yyyy/MM/dd,EEEEE,HH:mm:ss</string>
    <string name="main_main_date_format">yyyy/MM/dd,HH:mm,EEEEE</string>
    <string name="yxapplication_date_format">yyyy,MM,dd,HH,mm</string>
    <string name="main_main_date_split">,</string>
    <string name="car_speed_noraml">车速异常</string>
    <string name="speed_kmh">km/h</string>
    <string name="mileage_m">km</string>
    <!-- 调度信息 -->
    <string name="main_attempter_title_name">调度信息</string>
    <string name="main_attempter_type_inform">通知信息</string>
    <string name="main_attempter_type_notice">公告信息</string>
    <string name="main_attempter_type_road_info">路况信息</string>
    <string name="main_attempter_type_bug_info">故障信息</string>
    <!-- 信息点播 -->
    <string name="main_unicast_title_name">信息点播</string>
    <string name="main_unicast_setting_title_name">点播设置</string>
    <string name="main_unicast_no_book">未点播</string>
    <string name="main_unicast_booked">已点播</string>
    <string name="main_common_all_select">全选</string>
    <string name="main_common_turn_select">反选</string>
    <string name="main_common_cancel_select">取消点播</string>
    <string name="main_common_confirm_select">确认点播</string>
    <!-- 周边查询 -->
    <string name="main_around_title_name">周边查询</string>
    <string name="main_around_refresh_fail">请求失败,请稍后重试...</string>
    <!-- 路况信息 -->
    <string name="main_road_info_title_name">路况信息</string>
    <!-- 通知文本信息 -->
    <string name="main_notify_text_title_name">文本通知</string>
    <!-- 故障信息 -->
    <string name="main_bug_info_title_name">故障信息</string>
    <!-- 事件上报 -->
    <string name="main_event_upload_title_name">事件上报</string>
    <string name="main_event_sate_1">未上报</string>
    <string name="main_event_sate_2">已上报</string>
    <string name="main_event_upload_message">是否确认上报事件！</string>
    <string name="main_event_upload_no_event">没有事件上报信息！</string>
    <string name="main_event_upload_btn">设为上报事件</string>
    <string name="main_event_cancel_upload_btn">取消上报事件</string>
    <!-- 电子路单 -->
    <string name="main_elec_way_bill_title_name">电子运单</string>
    <string name="main_elec_way_bill_no">NO</string>
    <string name="main_elec_way_bill_time">上报时间</string>
    <string name="main_elec_way_bill_info">信息</string>
    <string name="main_elec_way_bill_upload">上报</string>
    <string name="main_elec_way_bill_date_format">yyyy/MM/dd HH:mm</string>
    <string name="main_road_bill_upload_hint">请输入上报内容</string>
    <string name="main_elec_way_bill_msg_too_long">上报内容过长</string>
    <string name="main_road_bill_upload_message">路单上报中,请稍后...</string>
    <string name="main_elec_way_bill_upload_fal">上报失败,请稍后重试...</string>
    <!-- 中心提问 -->
    <string name="main_center_question_title_name">中心提问</string>
    <string name="main_center_question_no_answer">未回答</string>
    <string name="main_center_question_no_question">没有中心提问信息！</string>
    <string name="main_center_question_answer">回答</string>
    <string name="main_center_question_prev">上一题</string>
    <string name="main_center_question_next">下一题</string>
    <string name="main_center_question_commit">提交答案</string>
    <string name="main_center_question_info">答案:</string>
    <!-- 硬件设置相关 -->
    <string name="main_hardware_title_name">硬件设置</string>
    <string name="main_hardware_clock">时钟</string>
    <string name="main_hardware_soundeffect">音效</string>
    <string name="main_unsetted">未设置</string>
    <string name="main_hardware_silencemod">铃声静音设置</string>
    <string name="main_hardware_keyvoice">按键音设置</string>
    <string name="main_hardware_LED_set">LED屏亮度自动调整</string>
    <string name="main_hardware_mediavoice">多媒体音量</string>
    <string name="main_hardware_screen_brightness">屏幕亮度</string>
    <string name="main_hardware_reset">恢复出厂设置</string>
    <string name="main_hardware_reset_content">清除所有用户信息</string>
    <string name="main_hardware_wifi_sta">wifi</string>
    <string name="main_hardware_wifi_ap">个人热点</string>
    <string name="main_hardware_open_wifi">开启WLAN</string>
    <string name="main_hardware_open_wifi_ap">开启WLAN热点</string>
    <string name="main_hardware_net_name">网络名称</string>
    <string name="main_hardware_wlan_ap_setting">设置WLAN热点</string>
    <string name="main_hardware_wifi_add">手动添加网络</string>
    <string name="main_hardware_wlan_connect">连接</string>
    <string name="main_hardware_wlan_cancel">取消</string>
    <string name="main_hardware_wlan_cancel_save">取消保存</string>


    <string name="main_timepicker_year">年</string>
    <string name="main_timepicker_month">月</string>
    <string name="main_timepicker_day">日</string>
    <string name="main_timepicker_hour">时</string>
    <string name="main_timepicker_minute">分</string>
    <string name="main_hardware_settime">时间设置</string>
    <string name="main_hardware_verify_password">验证密码</string>
    <string name="main_hardware_input_password">请输入验证密码：</string>
    <string name="input_password_hint">请输入验证密码</string>
    <string name="password_fal">密码错误</string>
    <string name="password_null">密码为空</string>
    <!-- 音效相关 -->
    <string name="main_seteq_title_name">音效设置</string>
    <string name="main_seteq_mix">混响</string>
    <string name="main_seteq_low">低音</string>
    <string name="main_seteq_middle">中音</string>
    <string name="main_seteq_high">高音</string>
    <string name="main_seteq_frontleft">前左</string>
    <string name="main_seteq_frontright">前右</string>
    <string name="main_seteq_rearleft">后左</string>
    <string name="main_seteq_rearright">后右</string>
    <string name="main_seteq_horizontal">左右</string>
    <string name="main_seteq_vertical">前后</string>
    <string name="main_seteq_flat">平滑</string>
    <string name="main_seteq_classic">古典</string>
    <string name="main_seteq_pop">流行</string>
    <string name="main_seteq_rock">摇滚</string>
    <string name="main_seteq_jazz">爵士</string>
    <string name="main_seteq_custom">(自定义)</string>
    <!-- 信息提示 -->
    <string name="toast_show_prompt_normal">提示</string>
    <string name="toast_show_prompt_warn">警告</string>
    <string name="toast_show_prompt_error">错误</string>
    <string name="toast_show_prompt_test">测试</string>
    <string name="app_log">行驶记录仪</string>
    <string name="app_titel">行驶记录</string>
    <string name="export_data">数据导出</string>
    <string name="control_media_data">多媒体数据操作</string>
    <string name="car_detection">车辆检测</string>
    <string name="set_load_state">设置载重状态</string>
    <string name="driver_data_analysis">数据分析</string>
    <string name="print_data">打印</string>
    <string name="load_data">加载数据中,请稍侯...</string>
    <string name="load_fal">加载数据失败...</string>
    <string name="car_type">加危险品车辆</string>
    <string name="start_latitude_and_longitude">起点经纬度：</string>
    <string name="end_latitude_and_longitude">终点经纬度：</string>
    <string name="mileage_msg">里程信息：</string>
    <string name="start_end_time">起止时间：</string>
    <string name="zhi">至</string>
    <string name="no_driving_data">无疲劳数据！</string>
    <string name="driving_data">疲劳驾驶数据</string>
    <string name="Driving_data">行驶记录数据</string>
    <string name="average_speed">停车前15分钟平均速度：</string>
    <string name="export_data_message">请插入U盘！</string>
    <string name="no_print_data_message">无打印数据！</string>
    <string name="has_print_data_message">有打印数据！</string>
    <string name="message_notify">信息提示</string>
    <string name="udisk_mounted">U盘已挂载</string>
    <string name="export_data_suc">导出成功</string>
    <string name="export_data_fal">导出失败</string>
    <string name="print_data_fal">打印失败</string>
    <string name="print_data_suc">打印成功</string>
    <string name="has_insert">已插入</string>
    <string name="no_insert">未插入</string>
    <string name="event_suc">事件上报成功</string>
    <string name="event_fal">事件上报失败</string>
    <string name="device_busy">设备正忙稍候操作</string>
    <string name="no_data">无数据！</string>
    <string name="with_data_out">有数据需要导出</string>
    <string name="no_data_out">无数据需要导出</string>
    <string name="car_classification">车辆分类：</string>
    <string name="load_state">载重状态：</string>
    <string name="driver_no">驾驶员证号：</string>
    <string name="print_time">打印时间：</string>
    <string name="current_load_state">车辆当前载重状态：</string>
    <string name="load_state_message">你希望改变当前的载重状态为：</string>
    <string name="null_load">空载</string>
    <string name="heavy_load">重载</string>
    <string name="half_load">半载</string>
    <string name="fal_load">未知</string>
    <string name="center_question_sus">回答成功</string>
    <string name="center_question_fail">回答提交失败，请稍后再试</string>
    <string name="center_electricbill_commit_sus">上报成功</string>
    <string name="center_electricbill_commit_fail">上报失败，请稍后再试</string>
    <string name="center_event_commit_sus">事件上报成功</string>
    <string name="center_event_commit_fail">事件上报失败，请稍后再试</string>

    <string name="center_order_commit_sus">点播信息成功</string>
    <string name="center_order_commit_fail">点播信息失败，请稍后再试</string>
    <string name="center_order_cancel_sus">取消点播信息成功</string>
    <string name="center_order_cancel_fail">取消点播信息失败，请稍后再试</string>

    <!-- setting -->
    <string name="deal_backgroud_prompt">操作正在进行，请稍侯...</string>
    <string name="pwd">请输入验证密码:</string>
    <string name="confirm_pwd">密码确认</string>
    <string name="machine_num">本机号码</string>
    <string name="machine_id">车辆ID</string>
    <string name="sms_center_num">短信中心号</string>
    <string name="main_gprs_parameters">主GPRS参数</string>
    <string name="vice_gprs_parameters">副GPRS参数</string>
    <string name="net_state">网络状态</string>
    <string name="characteristic_coefficent">特征系数</string>
    <string name="car_characteristic_coefficent">车辆特征系数：</string>
    <string name="r_km">r/km</string>
    <string name="host_parameters_set">主机参数设置</string>
    <string name="no">无</string>
    <string name="has_been_set">已设置</string>
    <string name="ls_no_set">未设置</string>
    <string name="network_conn">网络已连接</string>
    <string name="network_no_conn">网络未连接</string>
    <string name="save">保存</string>
    <string name="set_fal">设置失败！</string>
    <string name="set_fal_last">设置失败！原因：设置的载重状态与当前状态一致！</string>
    <string name="conn_fal">连接失败！</string>
    <string name="set_suc">设置成功！</string>
    <string name="conn_suc">连接成功！</string>
    <string name="null_data">内容为空无法设置！</string>
    <string name="apn">APN</string>
    <string name="main_tcp_ip">主TCP IP</string>
    <string name="main_tcp_port">主TCP端口</string>
    <string name="vice_tcp_ip">副TCP IP</string>
    <string name="vice_tcp_port">副TCP端口</string>
    <string name="udp_ip">UDP IP</string>
    <string name="udp_port">UDP端口</string>
    <string name="vice_udp_ip">UDP IP（副）</string>
    <string name="vice_udp_port">UDP端口（副）</string>
    <string name="cc_updata">更新特征系数</string>
    <string name="cc_query">查询特征系数</string>
    <string name="cc_setmileage">设置里程</string>
    <string name="cc_check">特征系数检测</string>
    <string name="set_mileage">设置里程</string>
    <string name="km">公里</string>
    <string name="plese_input_cc">请输入新的特征系数...</string>
    <string name="edittext_setmileage">输入要设置的里程值</string>
    <string name="plese_input_m">请输入新的里程...</string>
    <string name="no_beging_again">无法开始,请稍后重试...</string>
    <string name="no_over_again">无法结束,请稍后重试...</string>
    <string name="set_mileage_fal">里程设置失败...</string>
    <string name="set_mileage_sus">里程设置成功</string>
    <string name="has_cc_fal">特征系数获取失败...</string>
    <string name="beging">开始</string>
    <string name="over">结束</string>
    <string name="back">返回</string>
    <string name="no_beging">未开始</string>
    <string name="checking">检测中...</string>
    <string name="checking_fal">检测失败...</string>
    <string name="car_no">车牌号：  </string>
    <string name="car_color">车辆颜色：  </string>
    <string name="area_id">所在区域ID：  </string>
    <string name="car_message">车辆身份信息</string>
    <string name="zd_id">终端ID号：  </string>
    <string name="color_bule">蓝色</string>
    <string name="color_yellor">黄色</string>
    <string name="color_black">黑色</string>
    <string name="color_white">白色</string>
    <string name="color_other">其他</string>
    <string name="povince_id">省域ID：  </string>
    <string name="city_id">市域ID：  </string>
    <string name="car_type1">车牌分类：  </string>
    <string name="car_vin">车辆VIN：  </string>
    <string name="mf_id">制造商ID：  </string>
    <string name="zd_type">终端型号：  </string>
    <string name="registered">注册</string>
    <string name="unregistered">注销</string>
    <string name="registered_suc">注册成功</string>
    <string name="registered_fal">注册失败,请稍后重试...</string>
    <string name="unregistered_suc">注销成功</string>
    <string name="unregistered_fal">注销失败,请稍后重试...</string>
    <string name="center_question">中心提问</string>
    <string name="center_question_message">确认要注销车辆信息吗？</string>
    <string name="cocon">：</string>
    <string name="zd_id_no_conform">终端id为6位字,请输入正确...</string>
    <string name="area_id_no_conform">位数不够,请输入正确...</string>
    <string name="plese_input_complete">请把信息输入完整...</string>
    <string name="current_car_color">当前车辆颜色：</string>
    <string name="car_color_message">你希望改变当前的车辆颜色为：</string>
    <string name="zd_id_no_init">000000</string>
    <string name="normal_registered_suc">基本信息注册成功</string>
    <string name="car_vin_registered_suc">车辆VIN注册成功</string>
    <string name="car_no_type_registered_suc">车牌分类注册成功</string>
    <string name="normal_registered_fal">基本信息注册失败,请稍后重试...</string>
    <string name="car_vin_registered_fal">车辆VIN注册失败,请稍后重试...</string>
    <string name="car_no_type_registered_fal">车牌分类注册失败,请稍后重试...</string>
    <string name="future_generations">全选</string>
    <string name="selected">反选</string>
    <string name="report">文件上传</string>
    <string name="enven_report">事件上报</string>
    <string name="udiskoutput">U盘导出</string>
    <string name="search">搜索</string>
    <string name="sensor_state">传感器状态</string>
    <string name="alarm_state">报警状态</string>
    <string name="again_check">重新检测</string>
    <string name="sensor_checking">传感器状态检测中，请稍后...</string>
    <string name="alarm_checking">报警状态检测中，请稍后...</string>
    <string name="cancle_ing">正在取消...</string>
    <string name="sensor_result">传感器状态检测有</string>
    <string name="alarm_result">报警状态检测有</string>
    <string name="invalid">个无效</string>
    <string name="signal_invalid">个有效</string>
    <string name="detection">检测</string>
    <string name="again_detection">重新检测</string>
    <string name="title_beging_checking">第一步：请点击【开始】开始检测！</string>
    <string name="title_over_checking">第二步：请点击【结束】结束检测！</string>
    <string name="title_input_km">第二步：请点击【下一步】开始输入检测里程！</string>
    <string name="title_auto_cpt">第三步：请输入检测里程数后，点击【下一步】  按钮，实现特征系数自动计算！</string>
    <string name="title_cc_num">您的车辆特征系数为：</string>
    <string name="quit">退出</string>
    <string name="next_step">下一步</string>
    <string name="cpt_result">计算结果</string>
    <string name="start_again">重新开始</string>
    <string name="cc_time_init">00:00:00</string>
    <string name="image">图像</string>
    <string name="audio">音频</string>
    <string name="video">视频</string>
    <string name="time_hini">时间格式：年-月-日</string>
    <string name="data_hasing">数据获取中...</string>
    <string name="data_retrieval">数据检索</string>
    <string name="begin_time">开始时间：</string>
    <string name="over_time">结束时间：</string>
    <string name="begin_time2">开始时间</string>
    <string name="over_time2">结束时间</string>
    <string name="retrieval">查询</string>
    <string name="data_retrieval_title">起止时间不完整！请输入完整！</string>
    <string name="alarm_1">速度状态异常</string>
    <string name="alarm_2">怠速报警</string>
    <string name="alarm_3">车辆非法移位</string>
    <string name="alarm_4">非法点火</string>
    <string name="alarm_5">车辆被盗</string>
    <string name="alarm_6">车辆油量异常</string>
    <string name="alarm_7">车辆VSS故障</string>
    <string name="alarm_8">路线偏离报警</string>
    <string name="alarm_9">路段行驶时间不足/过长</string>
    <string name="alarm_10">进出路线</string>
    <string name="alarm_11">进出区域</string>
    <string name="alarm_12">超时停车</string>
    <string name="alarm_13">当天累计驾驶超时</string>
    <string name="alarm_14">疲劳驾驶</string>
    <string name="alarm_15">超速报警</string>
    <string name="alarm_16">计价器故障</string>
    <string name="alarm_17">摄像头故障</string>
    <string name="alarm_18">TTS模块故障</string>
    <string name="alarm_19">LCD显示器故障</string>
    <string name="alarm_20">主电源掉电</string>
    <string name="alarm_21">主电源欠压</string>
    <string name="alarm_22">GPS天线短路</string>
    <string name="alarm_23">GPS天线开路</string>
    <string name="alarm_24">GPS模块故障</string>
    <string name="alarm_25">预警</string>
    <string name="alarm_26">紧急报警</string>
    <string name="sensor_1">前车门信号</string>
    <string name="sensor_2">后车门信号</string>
    <string name="sensor_3">左转向灯信号</string>
    <string name="sensor_4">右转向灯信号</string>
    <string name="sensor_5">手刹信号</string>
    <string name="sensor_6">脚刹信号</string>
    <string name="sensor_7">前大灯信号</string>
    <string name="sensor_8">后大灯信号</string>
    <string name="sensor_9">ACC信号</string>
    <string name="sensor_10">电子喇叭信号</string>
    <string name="sensor_11">空重车信号</string>
    <string name="sensor_12">正反转信号</string>
    <string name="sensor_13">中控锁开锁</string>
    <string name="sensor_14">中控锁关锁</string>
    <string name="sensor_15">车门信号</string>
    <string name="sensor_16">报警指示线</string>
    <string name="sensor_17">GPS天线开路</string>
    <string name="sensor_18">GPS天线短路</string>
    <string name="sensor_19">主电源欠压</string>
    <string name="sensor_20">车灯信号</string>
    <string name="sensor_21">远光灯信号</string>
    <string name="sensor_22">近光灯信号</string>
    <string name="sensor_23">防劫报警信号</string>
    <string name="sensor_24">左车门信号</string>
    <string name="sensor_25">右车门信号</string>
    <string name="sensor_26">气喇叭信号</string>
    <string name="sensor_27">抢劫报警信号</string>
    <string name="sensor_28">小灯信号</string>
    <string name="project_menu">工程菜单</string>
    <string name="project_menu_face_detect">人脸识别、人数统计功能测试</string>
    <string name="project_menu_face_detect_title">测试人脸注册、人脸识别、人数统计功能</string>
    <string name="project_menu_face_manager">人脸库管理</string>
    <string name="project_menu_face_manager_title">包含已注册司机管理、APP_ID、SDK_KEY、人脸识别错误码管理等</string>
    <string name="project_menu_tts">TTS语音测试</string>
    <string name="project_menu_tts_title">测试TTS语音播报功能是否正常</string>
    <string name="project_menu_gps">GPS实时测试</string>
    <string name="project_menu_gps_title">GPS信号测试</string>
    <string name="project_menu_print">打印机检测</string>
    <string name="project_menu_print_link">检测打印机是否正常连接</string>
    <string name="project_menu_gsm">GSM检测</string>
    <string name="project_menu_gsm_state">查看SIM卡工作状态及信号强弱</string>
    <string name="project_menu_camera">摄像头检测</string>
    <string name="project_menu_camera_state">检测各路摄像头工作状态是否正常</string>
    <string name="project_menu_record">录音检测</string>
    <string name="project_menu_record_title">测试录音功能是否正常</string>
    <string name="project_menu_udisk_export">U盘数据导出</string>
    <string name="project_menu_udisk_export_title">通过U盘导出行驶记录仪数据</string>
    <string name="project_menu_app_updata">U盘主机升级</string>
    <string name="project_menu_app_updata_title">通过U盘对主机程序进行升级</string>
    <string name="project_menu_apk_updata">Android应用升级</string>
    <string name="project_menu_apk_updata_show">可以采用TF、U盘、SD进行应用程序升级</string>
    <string name="project_menu_apk_version">运行APK版本号查询</string>
    <string name="project_menu_apk_version_title">可查询运行中的程序版本号</string>
    <string name="project_menu_testing">生产检测</string>
    <string name="project_menu_testing_title">生产检测</string>
    <!-- lulinhua20140410 工程菜单-->
    <string name="project_menu_sz_device_test">设备检测</string>
    <string name="project_menu_sz_device_test_title">检测设备是否正常工作</string>
    <string name="project_menu_sz_alarm_test_show">请于10秒内按劫警开关进行报警测试</string>


    <string name="project_menu_rk_debug">rk调试菜单</string>
    <string name="project_menu_rk_debug_hint">显示屏调试功能的相关控制</string>
    <string name="project_menu_rk_debug_title">rk调试菜单</string>
    <string name="project_menu_rk_debug_com0">串口0</string>
    <string name="project_menu_rk_debug_com1">串口1</string>
    <string name="project_menu_rk_debug_com2">串口2</string>

    <string name="project_menu_rk_debug_enable_log">开启数据打印</string>
    <string name="project_menu_rk_debug_disable_log">关闭数据打印</string>
    <string name="project_menu_rk_debug_menu">菜单控制</string>
    <string name="project_menu_rk_debug_unlock_menu">解除菜单锁定</string>
    <string name="project_menu_rk_debug_restore">恢复菜单控制</string>
    <string name="project_menu_rk_debug_com2_mode">Com2模式</string>
    <string name="project_menu_rk_debug_com2_mode_debug">debug模式</string>
    <string name="project_menu_rk_debug_com2_mode_peripheral">外设模式</string>
    <string name="project_menu_rk_debug_power_mode">省电模式</string>
    <string name="project_menu_rk_debug_power_mode_enable">省电模式</string>
    <string name="project_menu_rk_debug_power_mode_disable">不省电模式</string>

    <string name="project_menu_host_machine_function_test_sensor_alarm">传感器、报警信号检测</string>
    <string name="project_menu_host_machine_function_test_sensor_alarm_hint">传感器、报警信号检测</string>

    <string name="project_menu_host_machine_function_test">主机功能检测</string>
    <string name="project_menu_host_machine_function_test_hint">检测主机的各项功能</string>
    <string name="project_menu_host_machine_function_record_test">录音效果测试</string>
    <string name="project_menu_host_machine_function_record_test_hint">测试主机录音效果</string>
    <string name="project_menu_host_machine_function_camera1_test">摄像头1测试</string>
    <string name="project_menu_host_machine_function_camera2_test">摄像头2测试</string>
    <string name="project_menu_host_machine_function_camera3_test">摄像头3测试</string>
    <string name="project_menu_host_machine_function_camera4_test">摄像头4测试</string>
    <string name="project_menu_host_machine_function_camera_test_hint1">测试主机摄像头1</string>
    <string name="project_menu_host_machine_function_camera_test_hint2">测试主机摄像头2</string>
    <string name="project_menu_host_machine_function_camera_test_hint3">测试主机摄像头3</string>
    <string name="project_menu_host_machine_function_camera_test_hint4">测试主机摄像头4</string>
    <string name="project_menu_host_machine_function_ad_test">车辆AD检测</string>
    <string name="project_menu_host_machine_function_ad_test_hint">车辆AD检测</string>

    <string name="project_menu_host_machine_function_test_pcaudio">pc音频测试</string>
    <string name="project_menu_host_machine_function_test_pcaudio_hint">pc音频测试</string>

    <string name="project_menu_host_machine_function_test_pcaudio_up">pc音量增加</string>
    <string name="project_menu_host_machine_function_test_pcaudio_up_hint">pc音量增加</string>

    <string name="project_menu_host_machine_function_test_pcaudio_down">pc音量减少</string>
    <string name="project_menu_host_machine_function_test_pcaudio_down_hint">pc音量减少</string>

    <string name="project_menu_host_machine_function_videoplayback">视频监控回放</string>
    <string name="project_menu_host_machine_function_videoplayback_hint">视频监控回放</string>

    <string name="project_menu_host_machine_function_driving_record">行驶记录</string>
    <string name="project_menu_host_machine_function_driving_record_hint">行驶记录</string>

    <string name="project_menu_special_control">特殊业务控制</string>
    <string name="project_menu_special_control_title">特殊业务功能打开与关闭</string>
    <string name="project_menu_host_machine_manage">主机管理</string>
    <string name="project_menu_host_machine_manage_hint">对R8进行管理</string>
    <string name="host_machine_manage_reboot">重启主机</string>
    <string name="host_machine_manage_reboot_query">确认是否需要重启主机？</string>
    <string name="host_machine_manage_reboot_hint">重启主机</string>
    <string name="host_machine_manage_reset_record">复位记录</string>
    <string name="host_machine_manage_reset_record_hint">查询主机的复位记录</string>
    <string name="host_machine_manage_reset_to_default">恢复出厂设置</string>
    <string name="host_machine_manage_reset_to_default_hint">让主机恢复出厂设置</string>
    <string name="host_machine_manage_reset_to_default_query">确认是否需要恢复出厂设置？</string>
    <string name="host_machine_manage_clear_param">清空主机参数</string>
    <string name="host_machine_manage_clear_param_hint">清空主机参数</string>
    <string name="host_machine_manage_clear_param_query">确认是否需要清空主机参数？</string>

    <string name="host_machine_manage_export_vehicle_log">车台日志导出</string>
    <string name="host_machine_manage_export_vehicle_log_hint">U盘导出车台日志文件</string>
    <string name="host_machine_manage_export_vehicle_log_query">确认是否需要导出车台日志文件？</string>
    <string name="host_machine_manage_set_out_ping">设置室外屏</string>
    <string name="host_machine_manage_set_out_ping_hint"> 渣土车专用设置</string>
    <string name="host_machine_manage_set_in_ping">设置室内屏</string>
    <string name="project_menu_apk_updata_title">检测系统及SD卡中是否存在升级Android应用程序并自动升级</string>
    <!-- added by DerekGuo -->
    <string name="project_menu_position_type">定位类型设置</string>
    <string name="project_menu_position_type_title">设置车载系统的卫星定位类型，支持北斗、GPS以及混合定位</string>
    <string name="position_type">定位类型设置：</string>
    <string name="gps_type">GPS</string>
    <string name="beidou_type">北斗</string>
    <string name="hunhe_type">混合</string>
    <string name="unknow_type">未知</string>
    <string name="position_type_text">定位类型：</string>
    <string name="project_menu_active_gprs">激活GPRS</string>
    <string name="project_menu_active_gprs_title">激活车载系统的GPRS</string>
    <string name="project_menu_active_gprs_failure">激活GPRS失败</string>
    <string name="project_menu_active_gprs_success">激活GPRS成功</string>
    <string name="project_menu_user_info_setting">用户信息设置</string>
    <string name="project_menu_user_info_setting_title">设置用户的基本信息，包括姓名、号码等</string>
    <string name="tv_user_label">用户姓名：</string>
    <string name="tv_user_num_label">用户号码：</string>
    <string name="system_state">系统状态</string>
    <string name="sim">SIM卡：</string>
    <string name="gps">GPS：</string>
    <string name="tcp">TCP：</string>
    <string name="udp">UDP：</string>
    <string name="print">打印机：</string>
    <string name="udisk">U盘：</string>
    <string name="login">已登录</string>
    <string name="not_login">未登录</string>
    <string name="not_locate">未定位</string>
    <string name="locate">已定位</string>
    <string name="normal_text">正常</string>
    <string name="no_normal_text">异常</string>
    <string name="sim_no_find">未找到SIM卡</string>
    <string name="sim_no_net">未入网</string>
    <string name="sim_no_link">未连接数据网</string>
    <string name="gsm_sim_state">SIM卡状态：</string>
    <string name="gsm_pot">运营商：</string>
    <string name="gsm_signal">信号强度：</string>
    <string name="gsm_net_link_state">无线网络连接状态：</string>
    <string name="gsm_strong">强</string>
    <string name="gsm_weak">弱</string>
    <string name="gsm_no_signal">无信号</string>
    <string name="gsm_mid">中</string>
    <string name="gsm_td">中国移动</string>
    <string name="gsm_tlm">中国电信</string>
    <string name="gsm_ltd">中国联通</string>
    <string name="export">导出</string>
    <string name="stop">停止</string>
    <string name="check">检测</string>
    <string name="exporting">U盘数据导出中...</string>
    <string name="cant_export">无法导出</string>
    <string name="export_again">重新导出</string>
    <string name="no_exit">正在导出，无法退出...</string>
    <string name="stop_export">正在结束导出，请稍后...</string>
    <string name="has_stop">已停止</string>
    <string name="no_stop">无法停止</string>
    <string name="udisk_stop">U盘被拔出，尝试停止导出...</string>
    <string name="position_type_setting">设置</string>
    <string name="udisk_updata_external_devices">外设程序升级</string>
    <string name="udisk_updata_car_frame">车台程序升级</string>
    <string name="udisk_updata_positioning">定位模块升级</string>
    <string name="udisk_updata_positioning_hint">升级定位模块</string>
    <string name="udisk_updata_file_export">U盘文件导入</string>
    <string name="udisk_updata_upgrade_by_configfile">主机升级</string>
    <string name="udisk_set_param_by_configfile">配置参数</string>
    <string name="udisk_export_multimedia_data">多媒体数据导出</string>

    <string name="plese_cw_ed">请确认是否升级【外设程序】？</string>
    <string name="plese_cw_cf">请确认是否升级【车台程序】？</string>
    <string name="plese_cw_pt">请确认是否升级【定位模块】？</string>
    <string name="plese_cw_fe">请确认是否导入【U盘文件】？</string>
    <string name="updating">升级中，请勿拔出U盘，请稍后...</string>
    <string name="again_updating">重新升级</string>
    <string name="again_exporting">重新导入</string>
    <string name="udisk_updata_fal_un">U盘被拔出，升级失败！请插入U盘重新升级！</string>
    <string name="udisk_updata_fal">升级失败！</string>
    <string name="udisk_updata_suc">升级成功！</string>
    <string name="udisk_export_fal">导入失败！</string>
    <string name="udisk_export_suc">导入成功！</string>
    <string name="version">版本信息</string>
    <string name="load_version">载入版本信息中...</string>
    <string name="map_version">地图版本号：</string>
    <string name="main_version">主程序版本号：</string>
    <string name="sys_version">系统版本号：</string>
    <string name="zd_version">主机版本号：</string>
    <string name="mf_version">终端厂家：</string>
    <string name="mf_phone_num">客服电话：</string>
    <string name="mf_name">厦门雅迅网络股份有限公司</string>
    <string name="mf_phone_num_data">0592-2956888</string>
    <string name="android_version">android</string>
    <string name="main_version_data">SSIBDS2D-t32g2m-701-3.03</string>
    <string name="zd_version_data">SSIBDS-2D01</string>
    <string name="btn_record_text">按下录音</string>
    <string name="time_init">00:00</string>
    <string name="driverL_log_fal">司机登入失败</string>
    <string name="driverL_log_suc">司机登入成功</string>
    <string name="driverL_log_quit_fal">司机登出成功</string>
    <string name="driverL_log_shuaka">司机尽快刷卡</string>
    <string name="recovering">恢复出厂设置中...</string>
    <string name="recover_default_set_fal">恢复出厂设置失败！</string>
    <string name="recover_default_set_suc">恢复出厂设置成功！</string>
    <string name="camera_test">摄像头检测</string>
    <string name="tts_test_text">这是一段测试文字，测试TTS音频播放结果！</string>
    <string name="menusystem_update_noresource">未找到升级文件,请确认升级文件是否已放在U盘根目录中的update文件夹中!</string>
    <string name="menusystem_update_select">选择升级文件</string>
    <string name="menusystem_updateing_notify">正在升级,请稍后重试!</string>
    <string name="menusystem_updateing">正在升级应用程序,请稍候...</string>
    <string name="apk_update_title">应用程序升级</string>
    <string name="new_app_info_default">新应用程序信息</string>
    <string name="old_app_info_default">旧应用程序信息</string>
    <string name="productesting_title">生产检测</string>

    <string name="spectrl_set_sus">设置成功</string>
    <string name="spectrl_set_fail">设置失败</string>
    <string name="spectrl_postion_mode">定位模式设置</string>
    <string name="spectrl_postion_mode_title">选择定位模式</string>
    <string name="spectrl_collision_mode">碰撞模式设置</string>
    <string name="spectrl_collision_mode_title">正常模式、测试模式</string>
    <string name="spectrl_gprs">数据通信开关</string>
    <string name="spectrl_gprs_title">打开或者关闭GPRS网络</string>
    <string name="spectrl_gsm">移动网络开关</string>
    <string name="spectrl_gsm_title">打开或者关闭GSM网络</string>
    <string name="spectrl_watch_car">设置看车模式</string>
    <string name="spectrl_watch_car_hint">开启或者关闭看车模式</string>
    <string name="spectrl_set_watch_car_mode">进入看车模式</string>
    <string name="spectrl_exit_watch_car_mode">退出看车模式</string>
    <string name="spectrl_v8_password">V8控制</string>
    <string name="spectrl_v8_password_hint">发送特定指令到V8以激活相应功能</string>

    <string name="spectrl_led_power_ctrl">LED电源控制</string>
    <string name="spectrl_led_power_ctrl_hint">控制LED电源打开或关闭</string>
    <string name="spectrl_led_power_up">上 电</string>
    <string name="spectrl_led_power_down">掉 电</string>
    <string name="spectrl_led_power">LED电源</string>

    <!-- 以下为按钮上的文字 -->
    <string name="spectrl_enable_gsm">打开移动网络</string>
    <string name="spectrl_disable_gsm">关闭移动网络</string>
    <string name="spectrl_enable_gprs">打开gprs</string>
    <string name="spectrl_disable_gprs">关闭gprs</string>
    <string name="spectrl_collision_mode_normal">正常模式</string>
    <string name="spectrl_collision_mode_test">测试模式</string>


    <!-- 生产检测字符串 -->
    <!-- 生产检测菜单标题 -->
    <string name="testing_menu_com1">测试串口一</string>
    <string name="testing_menu_com2">测试串口二</string>
    <string name="testing_menu_com3">测试串口三</string>
    <string name="testing_menu_tf">测试TF卡</string>
    <string name="testing_menu_udisk">测试U盘</string>
    <string name="testing_menu_screen">测试屏幕</string>
    <string name="testing_menu_tts">测试TTS</string>
    <string name="testing_menu_button">测试侧面五键</string>
    <string name="testing_menu_audio">测试音频</string>
    <string name="testing_menu_vedio">测试视频</string>
    <!-- 生产检测菜单说明 -->
    <string name="testing_menu_com1_data">测试串口一</string>
    <string name="testing_menu_com2_data">测试串口二</string>
    <string name="testing_menu_com3_data">测试串口三</string>
    <string name="testing_menu_tf_data">测试TF卡</string>
    <string name="testing_menu_udisk_data">测试U盘</string>
    <string name="testing_menu_screen_data">测试屏幕</string>
    <string name="testing_menu_tts_data">测试TTS</string>
    <string name="testing_menu_button_data">测试侧面五键</string>
    <string name="testing_menu_audio_data">测试音频</string>
    <string name="testing_menu_vedio_data">测试视频</string>
    <!-- 生产检测其他 -->
    <string name="testing_common_nocheck">未检测</string>
    <string name="testing_common_check">已检测</string>
    <string name="testing_common_error">故障</string>
    <string name="testing_common_normal">正常</string>
    <string name="testing_wait">请稍候</string>
    <string name="testing_com_prom">正在进行串口测试，请稍候</string>
    <string name="testing_nofinished">此功能正在规划中...</string>
    <!-- 生产检测侧面五键-->
    <string name="testing_button_hint">请勾选正常按钮</string>
    <string name="testing_button_menu">【功能】</string>
    <string name="testing_button_confirm">【确定】</string>
    <string name="testing_button_cancel">【取消】</string>
    <string name="testing_button_up">【向上】</string>
    <string name="testing_button_down">【向下】</string>

    <!-- 电召 -->
    <string name="dianzhao_submit_result_title">电召结果上报</string>
    <string name="dianzhao_submit_result_sus">电召结果上报成功</string>
    <string name="dianzhao_submit_result_fail">电召结果上报失败</string>
    <string name="dianzhao_pop_button_giveup_sus">电召取消成功</string>
    <string name="dianzhao_pop_button_execute_now_sus">电召现在开始执行上报成功</string>
    <string name="dianzhao_pop_button_execute_now_fail">电召现在开始执行上报失败</string>
    <string name="dianzhao_pop_button_giveup_fail">电召取消失败</string>
    <string name="dianzhao_pop_button_answer">抢单</string>
    <string name="dianzhao_item_detail_dial">一键拨号</string>
    <string name="dianzhao_item_detail_dial_error">拨号失败，请稍后重试</string>
    <string name="dianzhao_item_detail_dial_busy">电话繁忙，请稍后重试</string>
    <string name="dianzhao_item_detail_dial_numerror">电话号码错误</string>
    <string name="dianzhao_item_detail_result_sus">电召成功</string>
    <string name="dianzhao_item_detail_result_order_complete">订单完成</string>
    <string name="dianzhao_item_detail_result_cancel">电召取消</string>
    <string name="dianzhao_pop_button_answer_sus">抢单信息发送成功</string>
    <string name="dianzhao_pop_button_answer_fail">抢单信息发送失败</string>
    <string name="dianzhao_pop_button_giveup">放弃</string>
    <string name="dianzhao_popup_title">电召请求</string>
    <string name="dianzhao_popup_ordernum">订单号：</string>
    <string name="dianzhao_popup_ordertime">要车时间：</string>
    <string name="dianzhao_popup_time">电召时间：</string>
    <string name="dianzhao_popup_orderdetail">订单详情：</string>
    <string name="dianzhao_popup_order_year">年</string>
    <string name="dianzhao_popup_order_month">月</string>
    <string name="dianzhao_popup_order_day">日</string>
    <string name="dianzhao_popup_order_hour">时</string>
    <string name="dianzhao_popup_order_minute">分</string>
    <string name="dianzhao_popup_order_second">秒</string>
    <string name="dianzhao_type_immediate">即时电召</string>
    <string name="dianzhao_type_assign">指派电召</string>
    <string name="dianzhao_type_appoint">预约电召</string>
    <string name="dianzhao_type">电召类型：</string>
    <string name="dianzhao_check_order">订单查看</string>
    <string name="dianzhao_cancel_order">取消订单</string>
    <string name="dianzhao_cancel_ordernum">取消订单号：</string>
    <string name="dianzhao_cancel_reason_accident">事故 </string>
    <string name="dianzhao_cancel_reason_traffic">路堵 </string>
    <string name="dianzhao_cancel_reason_other">其它 </string>
    <string name="dianzhao_execute_result_sus">成功</string>
    <string name="dianzhao_confirm_passenger_phone">乘客电话：</string>
    <string name="dianzhao_confirm_description">描述信息：</string>
    <string name="dianzhao_info_no_data">无</string>
    <string name="dianzhao_info_title_data">电召</string>

    <string name="dianzhao_servicelev_text_company">公司：雅迅网络</string>
    <string name="dianzhao_servicelev_text_level">星级：五星</string>
    <string name="dianzhao_servicelev_text_name">姓名：上官慕容</string>
    <string name="dianzhao_servicelev_text_number">编号：1234567</string>

    <string name="dianzhao_service_five_stars">非常满意</string>
    <string name="dianzhao_service_four_stars">满  意</string>
    <string name="dianzhao_service_three_stars">较满意</string>
    <string name="dianzhao_service_two_stars">不满意</string>
    <string name="dianzhao_service_one_star">投  诉</string>
    <string name="dianzhao_service_thx">谢谢您的评价</string>

    <string name="dianzhao_result_wait_tobe_handled">未抢单</string>
    <string name="dianzhao_result_sent_ans_sus">抢单中</string>
    <string name="dianzhao_result_sent_ans_fail">失败</string>
    <string name="dianzhao_result_wait_tobe_handled_timeout">过期</string>
    <string name="dianzhao_result_active_giveup">放弃</string>
    <string name="dianzhao_result_receive_confirm">执行中</string>
    <string name="dianzhao_result_business_done">成功</string>
    <string name="dianzhao_result_driver_cancel">司机取消</string>
    <string name="dianzhao_result_center_cancel">中心取消</string>
    <string name="dianzhao_result_cancel_timeout">执行超时</string>
    <string name="dianzhao_result_unsuccess">未成功</string>
    <string name="dianzhao_result_state_unknown">未知</string>
    <string name="dianzhao_result_state_error">错误</string>
    <string name="dianzhao_result_text">是否接到客人?</string>
    <string name="dianzhao_result_text_show">请按《接到》或《没有》报告执行的结果</string>
    <string name="dianzhao_recv">接到</string>
    <string name="dianzhao_notrecv">没有</string>
    <string name="dianzhao_order_executed">已执行</string>
    <string name="dianzhao_order_executing">现在执行</string>
    <string name="dianzhao_order_discard">放弃执行</string>
    <string name="dianzhao_order_execute_title">预约电召执行</string>
    <!-- added by zhongkehua  GPS卫星 -->
    <string name="satellite_pos_status">定位: </string>
    <string name="satellite_visiable_num">可见卫星数: </string>
    <string name="satellite_trace_num">跟踪卫星数: </string>
    <string name="satellite_time">时间: </string>
    <string name="satellite_speed">速度: </string>
    <string name="satellite_atitude">海拔: </string>
    <string name="satellite_lon">经度: </string>
    <string name="satellite_lat">纬度: </string>
    <string name="satellite_pdop">PDOP: </string>
    <string name="req_gps_open_success">请求打开gps应用成功</string>
    <string name="req_gps_open_fail">请求打开gps应用失败</string>
    <string name="req_gps_close_success">请求关闭gps应用成功</string>
    <string name="req_gps_close_fail">请求关闭gps应用失败</string>

    <!-- 主机参数设置-->
    <string name="hostparam_first_server">第一服务器参数设置</string>
    <string name="hostparam_second_server">第二服务器参数设置</string>
    <string name="hostparam_stream_server">流媒体服务器参数设置</string>
    <string name="hostparam_main_para">主GPRS参数</string>
    <string name="hostparam_vice_para">副GPRS参数</string>
    <string name="hostparam_activate_gprs">激活网络连接</string>
    <string name="hostparam__gprs_connection_activated">已连接</string>
    <string name="hostparam__gprs_connection_not_activated">未连接</string>
    <string name="hostparam__gprs_connection_unknown">未知</string>
    <string name="hostparam__gprs_connection_unset">未设置</string>
    <string name="hostparam__gprs_connection_fail">连接状态查询失败</string>
    <string name="hostparam_first_server_Main">第一服务器主参数设置</string>
    <string name="hostparam_first_server_Vice">第一服务器副参数设置</string>
    <string name="hostparam_second_server_Main">第二服务器主参数设置</string>
    <string name="hostparam_second_server_Vice">第二服务器副参数设置</string>
    <string name="hostparam_stream_server_Main">流媒体服务器主参数设置</string>
    <string name="hostparam_stream_server_Vice">流媒体服务器副参数设置</string>
    <string name="hostparam_tcp_ip">TCP IP</string>
    <string name="hostparam_tcp_port">TCP端口</string>
    <string name="hostparam_first_server_activate_sus">第一服务器激活成功</string>
    <string name="hostparam_first_server_activate_fail">第一服务器激活失败</string>
    <string name="hostparam_second_server_activate_sus">第二服务器激活成功</string>
    <string name="hostparam_second_server_activate_fail">第二服务器激活失败</string>
    <string name="hostparam_stream_server_activate_sus">流媒体服务器激活成功</string>
    <string name="hostparam_stream_server_activate_fail">流媒体服务器激活失败</string>
    <string name="hostparam_clear_ip">清除主机参数</string>
    <string name="hostparam_clear_ip_yes">清除</string>
    <string name="hostparam_clear_ip_no">取消</string>
    <string name="hostparam_clear_ip_hint">是否清除主机参数？</string>
    <string name="hostparam_clear_ip_sus">参数清除成功，短时间内将无法与主机通信</string>
    <string name="hostparam_clear_ip_fail">主机参数清除失败</string>

    <string name="hostparam_car_message">车辆信息</string>

    <string name="hostparam_upg_host_title">主机配置升级</string>
    <string name="hostparam_upg_host_hint">是否通过U盘配置文件进行主机升级？</string>
    <string name="hostparam_upg_set_param_hint">是否通过U盘进行主机参数配置？</string>
    <string name="hostparam_upg_host_yes">确定</string>
    <string name="hostparam_upg_host_no">取消</string>
    <string name="hostparam_upg_host_no_usb">未检测到U盘，请插入U盘后重试</string>
    <string name="hostparam_upg_host_text_hint">进度信息：</string>
    <string name="hostparam_upg_host_progress">升级进度：</string>
    <string name="hostparam_upg_host_retry"> 重试 </string>
    <string name="udisk_upgrade_sent_request">已经发送请求，等待主机应答</string>
    <string name="udisk_upgrade_request_sus">请求成功，等待主机读取配置文件...</string>
    <string name="udisk_upgrade_request_fail">请求失败，请重试...</string>

    <string name="upgrade_main_r7">传统车台升级</string>
    <string name="upgrade_main_r7_hint">传统车台升级：R7、R8等</string>
    <string name="upgrade_main_dvr">DVR车台升级</string>
    <string name="upgrade_main_dvr_hint">DVR车台升级:V8等</string>
    <string name="project_menu_mcu_upgrade">MCU程序升级</string>
    <string name="projece_menu_mcu_hint">升级MCU程序</string>
    <string name="project_menu_phone_upgrade">手机模块升级</string>
    <string name="project_menu_phone_hint">升级手机模块程序</string>

    <string name="upgrade_dvr_application">车台应用升级</string>
    <string name="upgrade_dvr_application_hint">车台应用升级</string>
    <string name="upgrade_dvr_system">车台系统升级</string>
    <string name="upgrade_dvr_system_hint">车台系统升级</string>

    <string name="request_upgrade_dvr_sus">升级请求成功</string>
    <string name="request_upgrade_dvr_error">升级请求失败</string>

    <string name="video_playback_date_example">时间格式(2014年二月十四号十点八分八秒): 20140214100808:</string>
    <string name="video_playback_date_start">起始时间：</string>
    <string name="video_playback_date_end">结束时间：</string>
    <string name="video_playback_channel">通道号(1~8)</string>
    <string name="video_playback_default_settings">2015年12月 通道1</string>
    <string name="video_playback_default_year">2014年</string>
    <string name="video_playback_default_month">11月</string>
    <string name="video_playback_default_channel">1</string>
    <string name="video_playback_settings">日期通道设置</string>
    <string name="video_playback_search_channel">通道</string>
    <string name="video_playback_date_search">检 索</string>
    <string name="video_playback_search_play">播 放</string>

    <string name="video_playback_setting_yes">确 认</string>
    <string name="video_playback_setting_no">取 消</string>
    <string name="video_playback_search">查找视频</string>
    <string name="video_playback">视频回放</string>
    <string name="video_playback_play">播放</string>
    <string name="video_playback_stop">结束</string>
    <string name="car_paramers">车辆参数</string>
    <string name="LED_AdjustLight">LED亮度调节</string>
    <!-- 通用版协议相关字串 -->
    <string name="MachineParam_SetTitle_GPRS">设置GPRS参数</string>
    <string name="MachineParam_SetTitle_CDMA">设置CDMA参数</string>
    <string name="MachineParam_PhoneNum">本机号码</string>
    <string name="MachineParam_SMS_CENTER">短信中心号</string>
    <string name="MachineParam_Commu_GPRS_Param">GPRS通信参数</string>
    <string name="MachineParam_Commu_CDMA_Param">CDMA通信参数</string>
    <string name="MachineParam_Commu_Phone_num">手机号</string>
    <string name="MachineParam_Commu_APN">APN</string>
    <string name="MachineParam_Commu_IP">IP</string>
    <string name="MachineParam_Commu_Tcp_port">tcp端口</string>
    <string name="MachineParam_Commu_Udp_port">udp端口</string>
    <string name="MachineParam_Commu_CDMA_Account">用户名</string>
    <string name="MachineParam_Commu_CDMA_Password">密码</string>

    <!-- 深圳出租车主界面 -->
    <string name="main_dianzhao">电召业务 </string>
    <string name="main_phonebook">电话本</string>
    <string name="main_dispatch_text">调度信息</string>
    <string name="shenzhen_main_driver_name">姓        名:</string>
    <string name="shenzhen_main_driver_certificate">准 许 证:</string>
    <string name="shenzhen_main_driver_starslev">星        级:</string>
    <string name="shenzhen_main_driver_company">公司名称:</string>
    <!-- 工程菜单中的设备检测界面 -->
    <string name="shenzhen_projectmenu_test_POS">POS机检测</string>
    <string name="shenzhen_projectmenu_test_POS_title">测试POS机连接和工作是否正常</string>
    <string name="shenzhen_projectmenu_test_count">计价器检测</string>
    <string name="shenzhen_projectmenu_test_count_title">测试计价器连接和工作是否正常</string>
    <string name="shenzhen_projectmenu_test_host">主机状态检测</string>
    <string name="shenzhen_projectmenu_test_host_title">测试主机连接和工作是否正常</string>
    <string name="shenzhen_projectmenu_test_alarm">报警功能检测</string>
    <string name="shenzhen_projectmenu_test_alarm_title">模拟测试报警功能是否可用</string>


    <!-- 系统设置 -->
    <string name="shenzhen_systemset_aim_place">目的地:</string>
    <string name="shenzhen_systemset_av_export_type">导出类型选择:</string>
    <string name="shenzhen_systemset_audio_export">音频导出</string>
    <string name="shenzhen_systemset_video_export">视频导出</string>
    <string name="shenzhen_systemset_av_export">音视频导出</string>
    <string name="shenzhen_systemset_max_min_show">输入内容一个0-100之间纯数字，eg:10表示的是整个数值的10%</string>
    <string name="shenzhen_systemset_min_value">最小值:</string>
    <string name="shenzhen_systemset_max_value">最大值:</string>
    <string name="shenzhen_systemset_screen_voice">屏音量最大最小值设置</string>
    <string name="shenzhen_systemset_screen_light">屏亮度最大最小值设置</string>
    <string name="shenzhen_systemset_led_light">LED亮度最大最小值设置</string>
    <string name="shenzhen_systemset_phone_recv">电话接听方式设置</string>
    <string name="shenzhen_systemset_phone_recv_hint">可设置电话接听的方式:自动、手动</string>
    <string name="shenzhen_systemset_auto">自动接听</string>
    <string name="shenzhen_systemset_select">手动接听</string>
    <!-- 菜单界面 -->
    <string name="shenzhen_mainmenu_workoff_msg">交班信息</string>
    <string name="shenzhen_mainmenu_cotaxi_msg">拼车信息</string>

    <!-- 收入查询 -->
    <string name="shenzhe_pos_number">设备版本号：</string>
    <string name="shenzhe_pos_hardver">硬件版本：</string>
    <string name="shenzhe_pos_softfirst">软件主版本：</string>
    <string name="shenzhe_pos_softsec">软件次版本：</string>
    <string name="shenzhe_pos_status">设备状态：</string>
    <string name="shenzhe_pos_signedstatus">签到状态：</string>
    <string name="shenzhe_pos_unsend">未上传交易：</string>
    <string name="shenzhe_pos_driverid">驾驶员ID：</string>
    <string name="shenzhe_pos_drivername">驾驶员姓名：</string>
    <string name="shenzhen_pos_all_info">汇总信息</string>
    <string name="shenzhen_pos_allinfo_type">查询类型:</string>
    <string name="shenzhen_pos_allinfo_date">查询日期:</string>
    <string name="shenzhen_pos_nowday_info">当日详情</string>
    <string name="shenzhen_pos_nowday_index">索引号 ：</string>
    <!-- 厦门出租车 -->
    <string name="xiamen_title_name">厦门市出租汽车智能管理与服务系统</string>
    <string name="xiamen_menumain_message">信息</string>
    <string name="xiamen_menumain_service">服务</string>
    <string name="xiamen_menumain_nav">导航</string>
    <string name="xiamen_menumain_setting">设置</string>
    <string name="xiamen_menumain_other">其他</string>
    <string name="xiamen_inform_alarm">110通知</string>
    <string name="xiamen_inform_center">中心通知</string>
    <string name="xiamen_inform_company">公司通知</string>
    <string name="xiamen_inform_manage">运管处通知</string>
    <string name="xiamen_inform_other">其他通知</string>
    <string name="xiamen_service_dianzhao">电召服务</string>
    <string name="xiamen_service_gps">GPS维修</string>
    <string name="xiamen_service_valuation">计价器维修</string>
    <string name="xiamen_service_e">E通卡客服</string>
    <string name="xiamen_service_getinfo">GPS客服</string>
    <string name="xiamen_service_findthings">失物查找</string>
    <string name="sanming_title_name">莆田市出租汽车智能管理与服务系统</string>
    <string name="SensorDataInvalid">传感器数据无效</string>
    <string name="AlarmDataInvalid">报警数据无效</string>
    <!-- 新的传感器与报警信号定义 -->
    <string name="SensorStatUnknown">未知</string>
    <string name="SenByteF7">前车门信号</string>
    <string name="SenByteF6">后车门信号</string>
    <string name="SenByteF5">左转向灯信号</string>
    <string name="SenByteF4">右转向灯信号</string>
    <string name="SenByteF3">手刹信号</string>
    <string name="SenByteF2">脚刹信号</string>
    <string name="SenByteF1">前大灯信号</string>
    <string name="SenByteF0">后大灯信号</string>

    <string name="SenByteS7">ACC信号</string>
    <string name="SenByteS6">电子喇叭信号</string>
    <string name="SenByteS5">空重车信号</string>
    <string name="SenByteS4">正反转信号</string>
    <string name="SenByteS3">中控锁开锁</string>
    <string name="SenByteS2">中控锁关锁</string>
    <string name="SenByteS1">车门信号</string>
    <string name="SenByteS0">报警指示线</string>

    <string name="SenByteT7">GPS天线开路</string>
    <string name="SenByteT6">GPS天线短路</string>
    <string name="SenByteT5">主电源欠压</string>
    <string name="SenByteT4">车灯信号</string>
    <string name="SenByteT3">远光灯信号</string>
    <string name="SenByteT2">近光灯信号</string>
    <string name="SenByteT1">防劫报警信号</string>
    <string name="SenByteT0">保留填0</string>

    <string name="SenByteR7">硬盘状态</string>
    <string name="SenByteR6">录像状态</string>
    <string name="SenByteR5">保留填0</string>
    <string name="SenByteR4">保留填0</string>
    <string name="SenByteR3">小灯信号</string>
    <string name="SenByteR2">保留填0</string>
    <string name="SenByteR1">保留填0</string>
    <string name="SenByteR0">保留填0</string>

    <string name="AlarmStatUnknown">未知</string>

    <string name="AlaByteF7">速度状态异常</string>
    <string name="AlaByteF6">IC卡模块故障</string>
    <string name="AlaByteF5">怠速报警</string>
    <string name="AlaByteF4">车辆非法位移</string>
    <string name="AlaByteF3">车辆非法点火</string>
    <string name="AlaByteF2">车辆被盗</string>
    <string name="AlaByteF1">车辆油量异常</string>
    <string name="AlaByteF0">车辆VSS故障</string>

    <string name="AlaByteS7">路线偏离报警</string>
    <string name="AlaByteS6">路段行驶时间不足/过长</string>
    <string name="AlaByteS5">进出路线</string>
    <string name="AlaByteS4">进出区域</string>
    <string name="AlaByteS3">超时停车</string>
    <string name="AlaByteS2">当天累计驾驶超时</string>
    <string name="AlaByteS1">疲劳驾驶</string>
    <string name="AlaByteS0">超速报警</string>

    <string name="AlaByteT7">保留填0</string>
    <string name="AlaByteT6">保留填0</string>
    <string name="AlaByteT5">保留填0</string>
    <string name="AlaByteT4">计价器故障</string>
    <string name="AlaByteT3">摄像头故障</string>
    <string name="AlaByteT2">TTS模块故障</string>
    <string name="AlaByteT1">LCD显示器故障</string>
    <string name="AlaByteT0">主电源掉电</string>

    <string name="AlaByteR7">主电源欠压</string>
    <string name="AlaByteR6">GPS天线短路</string>
    <string name="AlaByteR5">GPS天线未接</string>
    <string name="AlaByteR4">GPS模块故障</string>
    <string name="AlaByteR3">碰撞报警</string>
    <string name="AlaByteR2">侧翻报警</string>
    <string name="AlaByteR1">预警</string>
    <string name="AlaByteR0">紧急报警</string>

    <!-- 公交车通用版 -->
    <string name="bus_yaxon_move_up">上行</string>
    <string name="bus_yaxon_move_down">下行</string>
    <string name="bus_yaxon_move_circle">环行</string>
    <string name="bus_yaxon_working">营运</string>
    <string name="bus_yaxon_not_working">非营运</string>
    <string name="bus_yaxon_into_station">进站</string>
    <string name="bus_yaxon_out_station">出站</string>

    <string name="bus_yaxon_signin">签到：</string>
    <string name="bus_yaxon_start">开始：</string>
    <string name="bus_yaxon_cur_station">本站名称:</string>
    <string name="bus_yaxon_next_station">下站名称:</string>
    <string name="bus_yaxon_bottom_hint">F1~F7:公共信息播报;  F8：营运切换;    F9：非营运切换;    F0：状态上报(路堵、报警等)</string>

    <string name="bus_yaxon_consective_drivetime">连续驾驶:</string>
    <string name="bus_yaxon_accumulative_drivetime">累积驾驶:</string>
    <string name="bus_yaxon_lessthan">&lt;</string>
    <string name="bus_yaxon_speed_unit">km/h</string>
    <string name="bus_yaxon_num_0">0</string>

    <string name="bus_yaxon_no_sim">无sim卡</string>
    <string name="bus_yaxon_not_work">NO</string>
    <string name="bus_yaxon_work">WORK</string>

    <!-- 公交通用版菜单栏 -->
    <string name="bus_yaxon_txt_msg">信息查询</string>
    <string name="bus_yaxon_work_type">营运类型</string>
    <string name="bus_yaxon_select_line">线路选择</string>
    <string name="bus_yaxon_send_msg">发送信息</string>
    <string name="bus_yaxon_play_auido">播报提示</string>
    <string name="bus_yaxon_maintain_system">系统维护</string>
    <string name="bus_yaxon_phone">语音通话</string>
    <string name="bus_yaxon_attendance">考勤管理</string>
    <string name="bus_yaxon_list_illegal">违规浏览</string>
    <string name="bus_yaxon_system_settings">系统设置</string>
    <string name="bus_yaxon_video_monitor">视频监控</string>

    <string name="bus_yaxon_main_menu">主界面</string>
    <string name="bus_yaxon_btn_confirm">确 认</string>
    <string name="bus_yaxon_btn_back">返 回</string>
    <string name="bus_yaxon_btn_next_page">下一页</string>
    <string name="bus_yaxon_btn_prev_page">上一页</string>

    <string name="bus_yaxon_stat_working">营运</string>
    <string name="bus_yaxon_stat_notworking">非营运</string>
    <string name="bus_yaxon_stat_request_sche">申请计划</string>
    <string name="bus_yaxon_switch_work_stat">切换营运状态</string>
    <string name="bus_yaxon_title_switch_to_work">切换至营运状态</string>
    <string name="bus_yaxon_title_switch_to_notwork">切换至非营运状态</string>

    <!-- 营运 -->
    <string name="bus_yaxon_working_main">运营状态：主站</string>
    <string name="bus_yaxon_working_to_vice">运营状态：>>主</string>
    <string name="bus_yaxon_working_to_main">运营状态：>>副</string>
    <string name="bus_yaxon_working_start_task">开始执行任务</string>
    <string name="bus_yaxon_working_stop_task">结束执行任务</string>
    <string name="bus_yaxon_working_broadcast_mode">自动/手动报站</string>
    <string name="bus_yaxon_working_broadcast_mode_auto">自动</string>
    <string name="bus_yaxon_working_broadcast_mode_manu">手动</string>
    <string name="bus_yaxon_working_req_start_work">营运请求1</string>
    <string name="bus_yaxon_working_req_join_work">加入运营请求</string>
    <string name="bus_yaxon_working_req_quit_work">退出运营请求</string>
    <string name="bus_yaxon_working_req_up_line">请求转入上行</string>
    <string name="bus_yaxon_working_req_down_line">请求转入下行</string>
    <string name="bus_yaxon_working_req_goin_cmd_center">进总站调度请求</string>

    <!-- 非营运 -->
    <string name="bus_yaxon_notworking_add_oil">加油</string>
    <string name="bus_yaxon_notworking_charter_bus">包车</string>
    <string name="bus_yaxon_notworking_pull_over">出场</string>
    <string name="bus_yaxon_notworking_pull_in">回场</string>
    <string name="bus_yaxon_notworking_stop">停场</string>
    <string name="bus_yaxon_notworking_idle">空放</string>
    <string name="bus_yaxon_notworking_repair">修车</string>
    <string name="bus_yaxon_notworking_keep_good">保养</string>
    <string name="bus_yaxon_notworking_add_gas">加气</string>
    <string name="bus_yaxon_notworking_waiting_orders">机动</string>
    <string name="bus_yaxon_notworking_resting">休班</string>
    <string name="bus_yaxon_notworking_else">其它</string>
    <string name="bus_yaxon_notworking_eating">就餐</string>

    <string name="func_F1">F1:</string>
    <string name="func_F2">F2:</string>
    <string name="func_F3">F3:</string>
    <string name="func_F4">F4:</string>
    <string name="func_F5">F5:</string>
    <string name="func_F6">F6:</string>


    <!-- 申请计划 -->

    <string name="signal_auto_reflesh">自动刷新</string>
    <string name="gsm_provider_mobile_G">移动G网</string>
    <string name="gsm_provider_mobile_T">移动T网</string>
    <string name="gsm_provider_unicom_G">联通G网</string>
    <string name="gsm_provider_unicom_W">联通W网</string>
    <string name="gsm_provider_telecom_C">电信C网</string>
    <string name="gsm_provider_telecom_C3">电信C3网</string>
    <string name="gsm_provider_telecom_EVDO">电信EVDO</string>
    <string name="gsm_provider_unknown">未知网络</string>

    <!-- 长沙渣土车 -->
    <string name="start_selfcheck_topcap">顶盖</string>
    <string name="start_selfcheck_leftup">举升</string>
    <string name="start_selfcheck_carstatus">空重载</string>
    <string name="start_selfcheck_leftup_way">举升机制</string>
    <string name="start_selfcheck_ECU">发动机ECU</string>
    <string name="start_selfcheck_topcap_sensor">顶盖传感器</string>
    <string name="start_selfcheck_leftups_sensor">举升传感器</string>
    <string name="start_selfcheck_carstatus_sensor">空重载传感器</string>
    <string name="start_selfcheck_mark">检测分数</string>
    <string name="start_selfcheck_right">正常</string>
    <string name="start_selfcheck_error">不正常</string>
    <string name="start_selfcheck_topcap_status">顶盖状态</string>
    <string name="start_selfcheck_leftups_status">举升状态</string>
    <string name="start_selfcheck_carstatus_status">空重载状态</string>
    <!-- 三明公交出租车 -->
    <string name="sanming_driver_info_detail">出租车服务监督卡</string>
    <string name="sanming_driver_info_dan">单</string>
    <string name="sanming_driver_info_wei">位：</string>
    <string name="sanming_driver_info_company_text">海亿联合</string>
    <string name="sanming_driver_info_carnum_one">姓</string>
    <string name="sanming_driver_info_carnum_two">名：</string>
    <string name="sanming_driver_info_carnum_text">001127</string>

    <!-- 原姓名 -->
    <string name="sanming_driver_info_name_one">证</string>
    <string name="sanming_driver_info_name_two">号：</string>
    <string name="sanming_driver_info_name_text">王星</string>
    <string name="sanming_driver_info_driverid_one">车</string>
    <string name="sanming_driver_info_driverid_two">牌：</string>
    <string name="sanming_driver_info_driverid_text">12328</string>
    <string name="sanming_driver_info_telnum">投诉电话： </string>
    <string name="sanming_driver_info_telnum_text">8083878</string>
    <string name="sanming_driver_info_servicenum_text">服务热线：</string>
    <string name="sanming_driver_info_valite_text">有效期：</string>
    <string name="sanming_title_text_one">树文明行业形象</string>
    <string name="sanming_title_text_two">展海西交通风采</string>
    <string name="sanming_title_text_three">三明市运管处监管</string>

    <string name="putian_taxi_supervisor">监督电话：物价12358    交通：2692559、6726201</string>

    <string name="cs_lockcar_limitspd"> 锁车/限速:</string>
    <string name="cs_num_seventeen">17</string>
    <string name="cs_num_twenty">20</string>
    <string name="cs_num_twentytwo">22</string>
    <string name="cs_num_twentyfour">24</string>
    <string name="cs_num_2013">2013</string>
    <string name="cs_num_07">07</string>
    <string name="cs_num_10">10</string>
    <string name="cs_dalishi_yunshu">厦门大理石运输</string>
    <string name="cs_car_num">闽d123456d</string>
    <string name="cs_status_info">状态信息</string>
    <string name="cs_lift_limit">举升限制</string>

    <string name="titlebar_back">返回</string>
    <string name="nd_comm_rk_appver">RK程序版本信息</string>
    <string name="nd_comm_check_appver">设备程序版本信息</string>
    <string name="nd_comm_appver_rk_ui">RK主界面程序版本号：</string>
    <string name="nd_comm_appver_host_app">车台应用程序版本号：</string>
    <string name="nd_comm_appver_mcu">MCU程序版本号：</string>
    <string name="nd_comm_ver_gps_module">定位模块版本号：</string>
    <string name="nd_comm_appver_phone">手机模块版本号：</string>
    <string name="nd_comm_appver_gps">GPS应用进程版本号：</string>
    <string name="nd_comm_appver_dvr">DVR应用进程版本号：</string>
    <string name="nd_comm_appver_firmware_upgrade">固件升级应用进程版本号：</string>
    <string name="nd_comm_appver_network_service">网络服务进程版本号：</string>
    <string name="nd_comm_appver_res_service">资源服务进程版本号：</string>
    <string name="nd_comm_appver_dvr_service">DVR服务进程版本号：</string>
    <string name="nd_comm_appver_monitor_service">监控服务进程版本号：</string>
    <string name="project_comm_ai_appver">算法版本：</string>
    <string name="nd_comm_appver_imei">设备imei：</string>
    <string name="project_menu_product_test_nd_comm">RV生产检测</string>
    <string name="project_menu_product_test_nd_comm_hint">RV生产检测</string>
    <string name="project_menu_product_test_nd_comm_auto">自动检测项</string>
    <string name="project_menu_product_test_nd_comm_auto_hint">可以自动进行生产检测的项目</string>
    <string name="project_menu_product_test_nd_comm_manual">手动检测项</string>
    <string name="project_menu_product_test_nd_comm_manual_hint">手动进行生产检测的项目</string>
    <string name="product_test_nd_comm_software_version">程序版本号</string>
    <string name="product_test_nd_comm_software_version_hint">屏、主机的程序版本号</string>
    <string name="product_test_nd_comm_rk_app_version">程序版本号</string>
    <string name="product_test_nd_comm_rk_app_version_hint">RK的应用程序版本信息</string>
    <string name="product_test_nd_comm_cefan">车台侧翻检测</string>
    <string name="product_test_nd_comm_cefan_hint">车台侧翻检测</string>
    <string name="product_test_nd_comm_mileag_pulse">里程脉冲检测</string>
    <string name="product_test_nd_comm_mileag_pulse_hint">主机里程脉冲检测</string>
    <string name="product_test_nd_comm_screen_key">液晶、按键检测</string>
    <string name="product_test_nd_comm_screen_key_hint">进行液晶和按键检测</string>
    <string name="product_test_nd_comm_screen_key_prompt">进入屏幕测试后，按取消键外的任意键切换屏幕</string>
    <string name="product_test_nd_comm_mileag_pulse_unknown">未知</string>
    <string name="product_test_nd_comm_cutoff_power">断油断电检测</string>
    <string name="product_test_nd_comm_cutoff_power_hint">车台断油断电检测</string>
    <string name="product_test_nd_comm_test_can">车台CAN检测</string>
    <string name="product_test_nd_comm_test_can_hint">车台CAN信号检测</string>
    <string name="product_test_nd_comm_input_signal">车台输入检测</string>
    <string name="product_test_nd_comm_input_signal_hint">车台IO输入信号线检测</string>
    <string name="product_test_nd_comm_output_signal">车台输出检测</string>
    <string name="product_test_nd_comm_output_signal_hint">车台IO输出信号检测</string>
    <string name="product_test_nd_comm_check_camera">摄像头检测</string>
    <string name="product_test_nd_comm_check_camera_hint">摄像头检测</string>
    <string name="product_test_nd_comm_check_ad">车台AD检测</string>
    <string name="product_test_nd_comm_check_ad_hint">车台AD检测</string>
    <string name="product_test_nd_comm_check_sd">车台SD卡</string>
    <string name="product_test_nd_comm_check_sd_hint">车台SD卡检测</string>
    <string name="product_test_nd_comm_check_udisk">车台U盘</string>
    <string name="product_test_nd_comm_check_udisk_hint">车台U盘检测</string>
    <string name="product_test_nd_comm_check_hdisk">车台硬盘</string>
    <string name="product_test_nd_comm_check_hdisk_hint">车台硬盘检测</string>
    <string name="product_test_nd_comm_check_tts">车台TTS检测</string>
    <string name="product_test_nd_comm_check_tts_hint">车台TTS检测</string>
    <string name="product_test_nd_comm_check_com">车台串口检测</string>
    <string name="product_test_nd_comm_check_com_hint">检测车台的所有可用串口</string>
    <string name="product_test_nd_comm_check_phone">拨号检测</string>
    <string name="product_test_nd_comm_check_phone_hint">检测拨打电话功能</string>
    <string name="product_test_nd_comm_check_system">车台系统自检</string>
    <string name="product_test_nd_comm_check_system_hint">车台系统自检</string>
    <string name="product_test_nd_comm_check_gps">实时定位检测</string>
    <string name="product_test_nd_comm_check_gps_hint">实时检测北斗、gps定位功能是否正常</string>
    <string name="product_test_nd_comm_reset_param">恢复出厂设置</string>
    <string name="product_test_nd_comm_reset_param_hint">车台恢复出厂设置</string>
    <string name="product_test_nd_comm_check_reset">复位记录</string>
    <string name="product_test_nd_comm_check_reset_hint">查询车台复位记录</string>
    <string name="product_test_nd_comm_check_signal">检定输入/输出信号模式选择</string>
    <string name="product_test_nd_comm_check_signal_hint">检测里程脉冲误差、车速传感器、实时时钟</string>
    <string name="nd_comm_sdcard_detail">SD卡详细信息</string>
    <string name="nd_comm_hdisk_detail">硬盘详细信息</string>
    <string name="nd_comm_udisk_detail">U盘详细信息</string>
    <string name="product_test_nd_comm_enable">已打开</string>
    <string name="product_test_nd_comm_disable">已关闭</string>
    <string name="product_test_nd_comm_start">开  始</string>
    <string name="product_test_nd_comm_stop">停  止</string>
    <string name="product_test_nd_comm_check_mileage_error">里程误差测量</string>
    <string name="product_test_nd_comm_check_mileage_error_hint">测量里程误差</string>
    <string name="product_test_nd_comm_check_speed_sensor">测试车速传感器信号</string>
    <string name="product_test_nd_comm_check_speed_sensor_hint">测试车速传感器信号</string>
    <string name="product_test_nd_comm_check_real_time">测试实时时钟</string>
    <string name="product_test_nd_comm_check_real_time_hint">测试实时时钟</string>
    <string name="nd_comm_host_selfcheck">车台系统自检</string>
    <string name="nd_comm_host_selfcheck_gsmstat">网络状态：</string>
    <string name="nd_comm_host_selfcheck_gsmstat_hint">车台手机网络当前状态</string>
    <string name="nd_comm_host_selfcheck_gsm_signal">信号强度：</string>
    <string name="nd_comm_host_selfcheck_gsm_signal_hint">车台手机信号强度</string>
    <string name="nd_comm_host_selfcheck_tcp_stat">TCP：</string>
    <string name="nd_comm_host_selfcheck_tcp_stat_hint">车台tcp登录状态</string>
    <string name="nd_comm_host_selfcheck_udp_stat">UDP：</string>
    <string name="nd_comm_host_selfcheck_udp_stat_hint">车台udp登录状态</string>
    <string name="nd_comm_host_selfcheck_position_stat">定位模块状态：</string>
    <string name="nd_comm_host_selfcheck_position_stat_hint">车台定位模块状态</string>
    <string name="nd_comm_host_selfcheck_position_antenna_stat">定位模块天线：</string>
    <string name="nd_comm_host_selfcheck_position_antenna_hint">车台定位模块天线状态</string>
    <string name="nd_comm_chk_key_menu">菜单</string>
    <string name="nd_comm_chk_key_enter">确认</string>
    <string name="nd_comm_chk_key_cancel">取消</string>
    <string name="nd_comm_chk_key_up">向上</string>
    <string name="nd_comm_chk_key_down">向下</string>
    <string name="nd_comm_chk_test_screen">测试屏幕</string>
    <string name="nd_comm_chk_quit">退出测试</string>
    <string name="hello_world">Hello world!</string>
    <string name="menu_settings">Settings</string>
    <string name="title_activity_video_playback_export">VideoPlaybackExportActivity</string>
    <string name="title_activity_video_playback_detail">VideoPlaybackDetailActivity</string>
    <string name="title_activity_video_search">VideoSearchActivity</string>
    <string name="title_activity_video_search_detail">VideoSearchDetailActivity</string>
    <string name="face_register_close">拍照注册(已关闭)</string>
    <string name="face_register_open">拍照注册(已开启)</string>
    <string name="date_picker_title">选择年、月、通道号：</string>
    <string name="include_date_day">日</string>
    <string name="include_date_one">一</string>
    <string name="include_date_two">二</string>
    <string name="include_date_three">三</string>
    <string name="include_date_four">四</string>
    <string name="include_date_five">五</string>
    <string name="include_date_six">六</string>
    <string name="item_date_gridview">2</string>

    <string name="video_preview_radiobutton_first">视频预览测试1</string>
    <string name="video_preview_radiobutton_second">视频预览测试2</string>

    <string name="dialog_common_ok">确认</string>
    <string name="dialog_common_cancel">取消</string>
    <string name="base_titlebar_e_cert">电子上岗证</string>
    <string name="base_titlebar_hotspot">热点区域关注</string>
    <string name="base_titlebar_voip">IP对讲系统</string>
    <string name="base_titlebar_scheduling">调度信息</string>
    <string name="base_titlebar_business">营运查询</string>
    <string name="base_titlebar_setup">设置</string>
    <string name="base_titlebar_grab_order">抢单</string>
    <string name="base_titlebar_media">多媒体</string>

    <string name="base_titlebar_grab_order_online">抢单(在线地图)</string>
    <string name="base_titlebar_grab_order_offline">抢单</string>
    <string name="base_titlebar_grab_order_online_suc">抢单成功(在线地图)</string>
    <string name="base_titlebar_grab_order_offline_suc">抢单成功(离线地图)</string>
    <string name="base_titlebar_grab_order_failed">抢单失败</string>
    <string name="base_titlebar_grab_order_cancel_by_driver">取消订单</string>
    <string name="base_titlebar_grab_order_cancel_by_passenger">乘客已取消订单</string>
    <string name="base_titlebar_grab_order_evalution">服务评价</string>
    <string name="base_titlebar_grab_order_evalution_cdt">服务评价(%1$dS)</string>

    <string name="base_main_menu_text_map">导 航</string>
    <string name="base_main_menu_text_install">APP超市</string>
    <string name="base_main_menu_text_dianzhao">电 召</string>
    <string name="base_main_menu_text_voip">IP对讲</string>
    <string name="base_main_menu_text_business">营运查询</string>
    <string name="base_main_menu_text_scheduling">调度信息</string>
    <string name="base_main_menu_text_phonecall">语音通话</string>
    <string name="base_main_menu_text_setup">设 置</string>
    <string name="base_main_menu_text_sign_out">签 退</string>
    <string name="base_main_menu_text_player">播放器</string>
    <string name="base_main_menu_text_local_video">本地视频</string>
    <string name="base_main_menu_text_video_playbace">监控视频</string>
    <string name="base_main_menu_text_camera">摄像头预览</string>
    <string name="base_main_menu_text_media">多媒体</string>
    <string name="base_main_menu_text_mp3">音 乐</string>
    <string name="base_main_menu_text_mp4">视 频</string>
    <string name="base_main_menu_text_fm">收音机</string>

    <string name="base_main_weather_des_unknow">加载中</string>

    <string name="base_main_driver_welcome">欢迎您的使用</string>
    <string name="base_main_driver_login">请登录</string>
    <string name="base_main_driver_job_no">工　号: %1$s</string>
    <string name="base_main_driver_name">姓　名: %1$s</string>
    <string name="base_main_driver_car_no">车牌号: %1$s</string>
    <string name="base_main_driver_company">公　司: %1$s</string>
    <string name="base_main_driver_supervisor">监　督: %1$s</string>

    <string name="base_e_cert_driver_job_no">工 \t\t\t号: </string>
    <string name="base_dz_driver_company">公 \t\t\t司: </string>
    <string name="base_dz_driver_name">司 \t\t\t机: </string>
    <string name="base_e_cert_driver_name">姓 \t\t\t名: </string>
    <string name="base_e_cert_driver_car_no">车 \t牌 \t号: </string>
    <string name="base_e_cert_driver_company">公司: </string>
    <string name="base_e_cert_driver_supervisor">监督电话: %1$s</string>
    <string name="base_e_cert_driver_star_level">服 务 星 级: </string>
    <string name="base_e_cert_driver_service_count">服务次数: %1$s</string>
    <string name="base_e_cert_driver_rank">司机排名: %1$s</string>
    <string name="base_e_cert_company_complaint">公司投诉:%1$s</string>
    <string name="base_e_cert_supervision_phone">监 督 电 话: </string>
    <string name="base_e_cert_service_phone">服 务 电 话: </string>
    <string name="base_e_cert_traffic_complaint">交通投诉:%1$s</string>
    <string name="base_e_cert_price_complaint">物价投诉:%1$s</string>
    <string name="base_e_cert_issuance">%1$s核发</string>
    <string name="base_e_cert_sign_in_time">本 次 签 到: </string>
    <string name="base_e_cert_exp_date">有效期至: </string>
    <string name="base_e_cert_exp_date2">有效期: </string>
    <string name="base_e_cert_account_id">请输入工号</string>
    <string name="base_e_cert_account_pwd">请输入密码</string>
    <string name="base_e_cert_account_remid">记住密码</string>
    <string name="base_e_cert_account_login">登录</string>
    <string name="base_e_cert_account_logout">退出\n账号</string>
    <string name="base_e_cert_account_login_loading">loading</string>
    <string name="base_qr_code_preview_pay_wechat">微信支付</string>
    <string name="base_qr_code_preview_pay_alipay">支付宝支付</string>

    <string name="base_tab_dianzhao_my_order">我的订单</string>
    <string name="base_tab_dianzhao_current">当前订单</string>
    <string name="base_tab_dianzhao_reserve_hall">预约大厅</string>
    <string name="base_tab_dianzhao_reserve">预约订单</string>
    <string name="base_tab_dianzhao_history">历史订单</string>

    <string name="base_friend_tip_dianzhao_my_order">暂无待执行订单，您可以前往有人用车查询更多订单</string>
    <string name="base_friend_tip_dianzhao_reserve_hall">下拉可以查询最新的待接订单</string>

    <string name="base_dianzhao_history_scope_today">当天</string>
    <string name="base_dianzhao_history_scope_week">这周</string>
    <string name="base_dianzhao_history_scope_custom">自定义</string>
    <string name="base_dianzhao_history_scope_start">起始时间</string>
    <string name="base_dianzhao_history_scope_end">截止时间</string>
    <string name="base_dianzhao_history_scope_query">查询</string>

    <string name="base_grab_order_op_call_passenger">呼叫乘客</string>
    <string name="base_grab_order_op_three_way_call">三方通话</string>
    <string name="base_grab_order_op_navi">路径导航</string>
    <string name="base_grab_order_op_cancel">取消订单</string>

    <string name="base_grab_order_action_grab">接单\n%1$dS</string>
    <string name="base_grab_order_action_confirm">确认\n%1$dS</string>
    <string name="base_grab_order_action_cancel_order">取消\n订单</string>
    <string name="base_grab_order_action_cancel_order_confirm">确认\n取消</string>
    <string name="base_grab_order_action_order_executing">订单\n执行中</string>
    <string name="base_grab_order_action_back_to_main_menu">返回\n主界面</string>
    <string name="base_grab_order_action_pick_up_passenger">乘客\n上车</string>
    <string name="base_grab_order_action_arrive_destination">送达\n目的地</string>
    <string name="base_grab_order_detail">详情</string>
    <string name="base_grab_order_nav">导航</string>

    <string name="base_grab_order_feed_back_reason_failed">订单已派给其他司机</string>
    <string name="base_grab_order_feed_back_reason_cancel">订单取消原因</string>

    <string name="base_grab_order_feed_back_choice_reason">请选择原因帮助改善派单系统</string>
    <string name="base_grab_order_feed_back_reason1">乘客距离我太远</string>
    <string name="base_grab_order_feed_back_reason2">路线太堵</string>
    <string name="base_grab_order_feed_back_reason3">终点偏远或不熟悉</string>

    <string name="base_grab_order_driver_name">司 机: %1$s</string>
    <string name="base_grab_order_driver_car_no">车 牌 号: %1$s</string>
    <string name="base_grab_order_driver_supervisor">监督电话：物价12358</string>
    <string name="base_grab_order_driver_company">服务单位: %1$s</string>
    <string name="base_grab_order_driver_mileage">行驶里程: %1$.2f Km</string>
    <string name="base_grab_order_driver_amount">金 额: %1$.2f 元</string>
    <string name="base_grab_order_driver_mileage_navi">导航里程: %1$.2f Km</string>
    <string name="base_grab_order_driver_detour_tip">注意路程差距超过%1$d %%！</string>
    <string name="base_grab_order_driver_evalution_tip">请您对本次服务做出评价</string>
    <string name="base_grab_order_driver_pay_wechat">微信二维码支付</string>
    <string name="base_grab_order_driver_pay_alipay">支付宝二维码支付</string>

    <string name="base_voip_tab_mychannel">我的频道</string>
    <string name="base_voip_channel_menu">功能菜单</string>
    <string name="base_voip_current_channel">当前频道：%1$s</string>
    <string name="base_voip_invite_code">邀请码：%1$s</string>

    <string name="base_voip_switch_channel">进入</string>

    <string name="base_voip_invite_info">消息：%1$s，邀请您加入频道%2$s,邀请码%3$s,是否接受?</string>
    <string name="base_voip_invite_accept">接受</string>

    <string name="base_voip_menu_creat_channel">创建频道</string>
    <string name="base_voip_menu_creat_channel_for_bt">创建\n频道</string>
    <string name="base_voip_menu_invite_member">邀请成员</string>
    <string name="base_voip_menu_add_channel">添加频道</string>
    <string name="base_voip_menu_join_channel">加入频道</string>
    <string name="base_voip_menu_join_channel_for_bt">加入\n频道</string>
    <string name="base_voip_menu_call_center_for_bt">呼叫\n中心</string>
    <string name="base_voip_menu_exit_channel">永久退出</string>
    <string name="base_voip_menu_delete_channel">删除频道</string>
    <string name="base_voip_menu_call_center">呼叫中心</string>
    <string name="base_voip_menu_mute">静音</string>
    <string name="base_voip_menu_invite_no_message">暂无邀请信息</string>

    <string name="base_voip_hit_creat_channel">输入频道名称</string>
    <string name="base_voip_hit_invite_member">输入车牌号</string>
    <string name="base_voip_hit_add_channel_id">输入频道ID</string>
    <string name="base_voip_hit_add_channel_invitecode">输入邀请码</string>

    <string name="base_business_bill_today">日账单</string>
    <string name="base_business_bill_week">周账单</string>
    <string name="base_business_bill_month">月账单</string>
    <string name="base_business_bill_query">查询</string>

    <string name="base_tab_phone_call">语音通话</string>
    <string name="base_tab_phone_record">通话记录</string>

    <string name="base_phonecall_dial">呼叫</string>

    <string name="base_setup_hostparam">主机参数</string>
    <string name="base_setup_factory">工程菜单</string>
    <string name="base_setup_version">版本信息</string>
    <string name="base_setup_system">系统状态</string>
    <string name="base_setup_hardware">硬件设置</string>
    <string name="base_setup_light">智能顶灯配置</string>
    <string name="base_setup_price">计价器配置</string>
    <string name="base_setup_brightness">亮度调节</string>
    <string name="base_setup_volume">音量调节</string>
    <string name="base_setup_hecheng">合乘</string>
    <string name="base_setup_bluetooth">蓝牙控制</string>

    <string name="base_processdlg_title">处理中，请稍后...</string>

    <string name="base_voip_float_view_open">IP对讲系统</string>

    <!-- tts -->
    <string name="app_id">568f829a</string>
    <string name="preference_default_tts_role">xiaoyan</string>
    <string name="preference_key_tts_speed">tts_speed</string>
    <string name="preference_key_tts_volume">tts_volume</string>
    <string name="preference_key_tts_pitch">tts_pitch</string>

    <string name="base_titlebar_e_cert_mode">服务监督卡主模式</string>
    <string name="base_titlebar_e_cert_mode_hint">选择是否自动显示电子上岗证</string>
    <string name="main_hardware_screen_brightness_day">屏幕亮度（7:00~19:00）</string>
    <string name="main_hardware_screen_brightness_night">屏幕亮度（19:00~7:00）</string>
    <string name="main_hardware_screen_off">ACC OFF熄屏设置</string>
    <string name="main_hardware_screen_off_hour">时</string>
    <string name="main_hardware_screen_off_minute">分</string>
    <string name="main_hardware_screen_off_second">秒</string>
    <string name="main_hardware_screen_off_time_set">设置时间</string>

    <string name="project_menu_rk_phonecall">语音通话</string>
    <string name="project_menu_rk_phonecall_hint_turnon">已开启</string>
    <string name="project_menu_rk_phonecall_hint_turnoff">已关闭</string>

    <string name="third_party_installed_apps">已安装应用</string>
    <string name="third_party_apps_list">上架清单</string>
    <string name="third_party_add_app">上 架</string>
    <string name="third_party_app_uninstall">卸 载</string>
    <string name="third_party_app_install_title">点击文件安装</string>

    <!-- 新增 -->
    <string name="main_map_heat_area_list_item_queue_count">排\n队</string>

    <string name="main_menu_ipintercom">IP对讲</string>
    <string name="main_menu_usecar">有人用车</string>
    <string name="main_menu_waiting_takecar">等待上车</string>
    <string name="main_menu_navi">导航</string>
    <string name="main_menu_income">我的收入</string>
    <string name="main_menu_history_order">历史订单</string>
    <string name="main_menu_toast">通知</string>
    <string name="main_menu_multi_media">多媒体</string>
    <string name="main_menu_setting">设置</string>
    <string name="main_menu_app_supermarket">APP超市</string>
    <string name="main_menu_face_verify">人脸识别</string>
    <string name="main_menu_other_functions">其他</string>
    <string name="main_menu_status_stop">停运</string>
    <string name="main_menu_status_stop_cancel">取消停运</string>
    <string name="main_menu_dsm">DSM预览</string>
    <string name="main_menu_adas">ADAS预览</string>
    <string name="main_menu_dsm_setting">算法设置</string>
    <string name="main_driver_status_nonwork">停运</string>
    <string name="main_driver_status_empty">空车</string>
    <string name="main_driver_status_heavy">重车</string>
    <string name="main_driver_status_dz">电召</string>
    <string name="main_driver_status_unlogin_welcome">欢迎使用</string>
    <string name="main_driver_status_unlogin">请登录</string>

    <string name="electcert_login_welcom">欢迎使用，请登录</string>
    <string name="electcert_login_input_hint_account">请输入帐号</string>
    <string name="electcert_login_input_hint_password">请输入密码</string>

    <string name="dz_order_grab">接单</string>
    <string name="dz_order_execute">执行</string>
    <string name="dz_order_pay">支付</string>

    <string name="dz_order_type_immediate">即时</string>
    <string name="dz_order_type_appoint">预约</string>
    <string name="dz_order_type_immediate_now">现在</string>
    <string name="dz_order_passenger_distance">距离乘客</string>
    <string name="dz_order_distance_km">km</string>
    <string name="dz_order_prediction">预估</string>
    <string name="dz_order_money_yuan">元</string>

    <string name="dz_order_takecar">上车</string>
    <string name="dz_order_arrive">送达</string>

    <string name="dz_order_number_0">0</string>
    <string name="dz_order_mileage">行驶里程</string>
    <string name="dz_order_mileage_km">公里</string>
    <string name="dz_order_time_waiting">服务时间</string>
    <string name="dz_order_time_minute">分钟</string>

    <string name="dz_order_call_dial">正在呼叫...</string>
    <string name="dz_order_call_prepare_to_finish">通话即将结束...</string>
    <string name="dz_order_call_finish">通话结束</string>

    <string name="business_done">完成</string>
    <string name="business_done_pay">支付</string>
    <string name="business_done_pay_cash">费用</string>
    <string name="business_done_pay_scan_qr">扫码</string>
    <string name="business_done_pay_trade_money">计费金额（元）</string>
    <string name="business_done_pay_trade_mileage_drive">行驶里程</string>
    <string name="business_done_pay_trade_mileage_navi">导航里程</string>

    <string name="dz_order_item_unreceiverd">待接单</string>
    <string name="dz_order_item_finished">已完成</string>

    <string name="driver_income">我的收入</string>
    <string name="driver_income_day">日账单</string>
    <string name="driver_income_week">周账单</string>
    <string name="driver_income_month">月账单</string>
    <string name="driver_income_category">类别</string>
    <string name="driver_income_money">金额(元)</string>
    <string name="driver_income_order_num">订单数</string>
    <string name="driver_income_total_income">总收入</string>
    <string name="driver_income_cash">现金</string>
    <string name="driver_income_union_pay">转账</string>

    <string name="dz_order_center_cancel_current">当前订单已取消，请重新接单</string>
    <string name="dz_order_center_cancel_not_current">有订单已取消，请注意查看</string>
    <string name="dz_order_center_cancel_reason">取消原因：</string>

    <string name="driver_today_bill">当班日账单</string>
    <string name="driver_today_mileage">行驶里程</string>
    <string name="driver_today_business_money">营运金额</string>
    <string name="driver_today_oil_fee">油费</string>
    <string name="driver_today_other_fee">其他</string>
    <string name="driver_today_income">当班收入</string>
    <string name="driver_today_week_income">净收入账单</string>
    <string name="driver_today_loading">正在加载中</string>
    <string name="driver_today_table_money">金额(元)：</string>
    <string name="driver_today_table_mileage">里程(公里)：</string>

    <string name="dz_cancel_reason">取消订单</string>
    <string name="dz_cancel_reason_spec">请选择原因帮助改善派单系统</string>
    <string name="dz_cancel_reason_1">乘客距离我太远</string>
    <string name="dz_cancel_reason_2">路太堵</string>
    <string name="dz_cancel_reason_3">终点偏远或不熟悉</string>
    <string name="dz_cancel_reason_confirm">确定</string>
    <string name="dz_cancel_reason_cancel">取消</string>

    <string name="multi_media_phone_call">语音通话</string>
    <string name="multi_media_video_preview">视频预览</string>
    <string name="multi_media_video_playback">录像回放</string>
    <string name="multi_media_mp3">音乐播放器</string>
    <string name="multi_media_mp4">视频播放器</string>
    <string name="multi_media_fm">收音机</string>
    <string name="multi_media_recorder_playback">记录仪回放</string>
    <string name="multi_media_video_latch">视频锁存</string>
    <string name="multi_media_bluetooth">蓝牙控制</string>
    <string name="main_navi_searthpath_input_add">请输入地址</string>
    <string name="main_navi_searthpath_search">搜索</string>
    <string name="main_navi_searthpath_navi">导航</string>

    <string name="main_record_password">记住密码</string>

    <!-- 指纹采集 -->
    <string name="cancel_btn">取消</string>
    <string name="delete_btn">删除</string>
    <string name="yx_driver_id">卡号</string>
    <string name="yx_edittext_hint">请输入证件号</string>
    <string name="yx_cardnum_length_error">卡号长度过长，请重置</string>
    <string name="collecting_fingerprint">请采集指纹</string>
    <string name="yx_commit_btn">提交</string>
    <string name="collect_fingerprint_success_partone">指纹采集成功，</string>
    <string name="collect_fingerprint_success_parttwo">5</string>
    <string name="collect_fingerprint_success_partthree">秒后返回</string>
    <string name="collect_fingerprint_failure">指纹采集失败，请重新采集</string>
    <string name="edittext_tips">证件号为空，请重新输入证件号！</string>
    <string name="yx_delete_dialog_title">取消采集</string>
    <string name="yx_add_fingerprint">添加指纹</string>
    <string name="yx_load_fingerprint">下载指纹</string>
    <string name="total_count_out_error">可显示采集指纹信息已满</string>
    <string name="base_main_menu_text_net">网络设置</string>
    <string name="base_main_menu_text_fingerprint">指纹采集</string>
    <string name="input_id_dialog_hint">请输入您的卡号</string>
    <string name="collecting_fingerprint_tip">请在指纹仪上采集指纹</string>
    <string name="recollect_fingerprint">重新采集</string>

    <string name="hostparam_ipc_param_setting">IPC参数设置</string>
    <string name="hostparam_ipc_power_save_mode">省电模式设置</string>
    <string name="hostparam_face_register">人脸拍照注册</string>
    <string name="channel1">通道1</string>
    <string name="channel2">通道2</string>
    <string name="channel3">通道3</string>
    <string name="channel4">通道4</string>

    <string name="ipc_title_selcet">选择</string>
    <string name="ipc_title_state">网络</string>
    <string name="ipc_title_ip">IPC设备</string>
    <string name="ipc_title_ip_online">在线IPC设备</string>
    <string name="ipc_title_work_state">状态</string>
    <string name="ipc_title_channel">通道选择</string>
    <string name="ipc_title_edit">更多设置</string>

    <string name="ipc_reflash">刷新</string>
    <string name="ipc_apply">应用</string>
    <string name="ipc_edit">编辑</string>

    <string name="ipc_preview">预览</string>
    <string name="ipc_net_info">网络参数</string>
    <string name="ipc_auth_setting">登录认证</string>
    <string name="ipc_auth_test">测试</string>

    <string name="ipc_edit_ip">IP地址：</string>
    <string name="ipc_edit_user">用户名：</string>
    <string name="ipc_edit_password">密　码：</string>
    <string name="ipc_edit_channel">通道号：</string>

    <string name="default_ip_address">*************</string>
    <string name="ping_ip">Ping Ip</string>
    <string name="ip_address">请输入IP地址：</string>

    <string name="base_main_menu_led_setting">智能顶灯配置</string>
    <string name="base_main_menu_taximeter_setting">计价器配置</string>
    <!-- 顶灯和计价器配置界面 -->
    <string name="led_type_bohai">渤海LED</string>
    <string name="led_type_sc_guanghong_lq">四川广弘LED（龙泉）</string>
    <string name="led_type_huahai">华翰LED</string>
    <string name="led_type_guangrui_jucai">光锐巨彩LED（广东湛江）</string>
    <string name="led_type_sc_guanghong_jx">四川广弘LED（郊县）</string>
    <string name="led_type_sc_guanghong_ms">四川广弘LED（眉山）</string>
    <string name="led_type_sc_cl">四川川力LED</string>
    <string name="led_type_zled_008c">ZLED-008C红绿双色顶灯（嘉兴出租车）</string>

    <string name="taximeter_type_nj_common">南京通用计价器</string>
    <string name="taximeter_type_sh_dazong">上海大众科技计价器</string>
    <string name="taximeter_type_js_liyang_ha">江苏溧阳计价器（淮安）</string>
    <string name="taximeter_type_sc_guanghong">四川广宏计价器</string>
    <string name="taximeter_type_js_liyang_lyg">江苏溧阳计价器（连云港）</string>
    <string name="taximeter_type_905">部标905计价器</string>
    <string name="taximeter_type_bj_juli">北京聚利计价器</string>
    <string name="taximeter_type_bj_qh">北京奇华计价器</string>
    <string name="taximeter_type_gz_bt">广州八通电子计价器</string>
    <string name="taximeter_type_gz_hg">广州华港电子计价器</string>
    <string name="taximeter_type_sh_liangbiao">上海良标计价器</string>
    <string name="taximeter_type_sz_difei">深圳迪飞计价器</string>
    <string name="taximeter_type_js_aboluo">江苏阿波罗计价器</string>
    <string name="taximeter_type_cq_cz">重庆出租车计价器</string>
    <string name="taximeter_type_qd_hx">青岛恒星计价器</string>
    <string name="taximeter_type_eeds">鄂尔多斯出租车计价器</string>

    <string name="face_verify_pass">验证成功</string>
    <string name="face_verify_fail">验证失败</string>

    <string name="qrcode_scan">二维码扫描</string>
    <string name="scan_tips">\"将二维码图片对准扫描框即可自动扫描\"</string>

    <string name="hostparam_third_server">第三服务器参数设置</string>
    <string name="hostparam_third_server_Main">第三服务器主参数设置</string>
    <string name="hostparam_third_server_Vice">第三服务器副参数设置</string>
    <string name="hostparam_third_server_activate_sus">第三服务器激活成功</string>
    <string name="hostparam_third_server_activate_fail">第三服务器激活失败</string>

    <string name="main_map_passenger_heat_map">乘客热点分布</string>
    <string name="main_map_order_money_map">接单金额分布</string>

    <string name="bad_drive_behavior_analysis">不良驾驶行为分析</string>
    <string name="bad_drive_behavior_tip">不良驾驶行为: 急加速、急转弯、急刹车、超速行驶、怠速行驶</string>
    <string name="comparing_oil_comsuption_per_100_kilometers">百公里油耗对比</string>
    <string name="comparing_bad_drive_behavior">不良驾驶行为对比</string>
    <string name="data_collection_champion">冠军</string>
    <string name="data_collection_oil_saving_elites">省油精英</string>
    <string name="data_collection_my">我</string>
    <string name="data_collection">今日统计</string>
    <string name="data_collection_month">本月统计</string>
    <string name="data_collection_unit_money">元</string>
    <string name="data_collection_unit_count">次</string>
    <string name="data_collection_congratulations_month">恭喜您，获得本月</string>
    <string name="data_collection_congratulations">恭喜您，获得今日</string>
    <string name="data_collection_congratulations_normal">表现不错，驾驶很规范！</string>
    <string name="data_collection_congratulations_veteran_driver">表现一般，要努力节油！</string>
    <string name="data_collection_oil_not_input">请输入油费，获取今日油耗统计</string>
    <string name="data_collection_me">我</string>
    <string name="data_collection_rank">(排名</string>
    <string name="data_collection_comma">，</string>
    <string name="data_collection_stop">、</string>
    <string name="data_collection_beat">打败</string>
    <string name="data_collection_competitor">个对手)</string>
    <string name="data_collection_rapid_acceleration">急加</string>
    <string name="data_collection_rapid_deceleration">急减</string>
    <string name="data_collection_sharp_turn">急转</string>
    <string name="data_collection_over_speed">超速</string>
    <string name="data_collection_idle_speed">怠速</string>
    <string name="data_collection_oil_pk">油耗PK</string>

    <string name="elec_cert_title_work">从业资格证</string>
    <string name="elec_cert_title_elec">电子上岗证</string>
    <string name="elec_cert_title_qrcode">二维码</string>
    <string name="elec_cert_driver_sign">司机签到</string>
    <string name="order_history_order_no">订单号: %1$s</string>
    <string name="friend_tip_sign_out">当前未签到，请签到后查看</string>

    <string name="power_save_switch">省电开关</string>
    <string name="power_save_mode">省电模式</string>
    <string name="power_save_delay">省电延时</string>
    <string name="power_save_delay_title">省电延时时间(单位:分钟):</string>
    <string name="power_save_switch_title">是否开启省电模式:</string>
    <string name="power_save_mode_title">省电模式选择:</string>
    <string name="power_save_mode_turnoff_net">关闭网络</string>
    <string name="power_save_mode_gsm">GSM在线模式</string>
    <string name="power_save_mode_gprs">GPRS在线模式</string>

    <!--arcface-->
    <string name="active_failed">引擎激活失败，错误码为 %d</string>
    <string name="prepare_taking_photo">请保持姿势准备拍照</string>
    <string name="face_unfound">没有检测到人脸</string>
    <string name="face_detect_success">人脸识别成功</string>
    <string name="face_detect_success_timer">定时任务，人脸识别成功</string>
    <string name="face_detect_fail">人脸识别失败</string>
    <string name="face_detect_fail_timer">定时任务，人脸识别失败</string>
    <string name="face_detect_not_alive">非活体，请本人正常进行识别</string>
    <string name="face_registering">正在注册人脸信息</string>
    <string name="face_register_success">人脸注册成功</string>
    <string name="face_detect_unfound">未检测到人脸,请保持好姿势</string>

    <string name="face_test">北京第三区交通委提醒您：道路千万条，安全第一条。行车不规范，亲人两行泪</string>
    <string name="face_success_test">当前天气： %1$s %2$d到%3$d摄氏度</string>
    <string name="img_login_fail">请选择正确的头像登录</string>
    <string name="login_face_detect">人脸识别登录</string>
    <string name="login_password">账号密码登录</string>

    <string name="video_retrieve_alarm_flag">报警：</string>
    <string name="video_retrieve_alarm_flag_title">报警标志</string>
    <string name="video_retrieve_lock_video">锁存</string>
    <string name="video_retrieve_unlock_video">解锁</string>
    <string name="video_retrieve_car_no">车牌号</string>
    <string name="video_retrieve_channel_no">通道号</string>
    <string name="video_retrieve_over_time">结束：</string>
    <string name="test_face_register">人脸注册</string>
    <string name="test_face_register_hint">拍照注册人脸信息</string>
    <string name="test_face_detect">人脸识别</string>
    <string name="test_face_detect_hint">人脸识别功能检测</string>
    <string name="test_face_count">人数统计</string>
    <string name="test_face_count_hint">人脸统计功能检测</string>

    <string name="project_menu_asr">语音识别</string>
    <string name="video_preview">视频预览</string>
    <string name="video_preview_ipc">IPC预览</string>
    <string name="video_preview_ahd">AHD预览</string>
    <string name="video_preview_ahd_5">AHD(5~8)预览</string>

    <string name="hide_navigator">隐藏底部导航条</string>
    <string name="eth_con_type">IP设置方式</string>
    <string name="eth_con_type_dhcp">DHCP（动态IP）</string>
    <string name="eth_con_type_manual">静态</string>
    <string name="eth_ipaddr">IP地址</string>
    <string name="eth_network_prefix_length">网络前缀长度</string>
    <string name="eth_dns">DNS</string>
    <string name="eth_gw">网关</string>
    <string name="ethernet_settings">以太网</string>
    <string name="eth_conf_perf_title">以太网</string>
    <string name="eth_conf_summary">以太网设置</string>
    <string name="eth_config_title">以太网设置</string>
    <string name="eth_settings_empty">无有线网</string>
    <string name="eth_settings_error">参数不完整</string>
    <string name="eth_none">无</string>
    <string name="eth_network_mask">"子网掩码"</string>
    <string name="eth_network_set_error">"错误码:"<xliff:g id="error_code">%1$s</xliff:g></string>
    <string name="eth_no_connect">当前无网络连接</string>
    <string name="eth_connected">已连接，当前IP地址：<xliff:g id="eth_ip">%1$s</xliff:g></string>
    <string name="eth_cancel">"取消"</string>
    <string name="menu_save">"保存"</string>
    <string name="main_hardware_ethernet_card">以太网</string>
    <string name="main_hardware_ethernet_card_hint">以太网配置</string>
    <string name="eth_setting_on">开启</string>
    <string name="eth_setting_off">关闭</string>
    <string name="eth_settting_title">以太网状态切换</string>
    <string name="project_menu_export_picture">照片导出</string>
    <string name="project_menu_config_voip">VOIP配置界面</string>

    <!-- TODO: Remove or change this placeholder text -->
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="dz_order_no">单号: %1$s</string>

    <string name="xm_main_car_qrcode_tip">扫一扫，查看车辆信息</string>
    <string name="xm_main_driver_sign_in_tip">请点击签到</string>
    <string name="xm_main_driver_sign_out_tip">请点击签退</string>
    <string name="xm_main_driver_name_tag">姓&#12288;&#12288;名:</string>
    <string name="xm_main_cert_no_tag">上岗证号:</string>
    <string name="xm_main_car_no_tag">车&#160;&#160;牌&#160;&#160;号:</string>
    <string name="xm_main_company_tag">所属企业:</string>
    <string name="xm_main_supervisor_tag">监督电话:</string>
    <string name="xm_main_sum_tag">金&#12288;&#12288;额:</string>
    <string name="xm_main_car_state_tag">状态:</string>
    <string name="xm_main_service_level_tag">考核等级:</string>
    <string name="xm_main_car_info_tip">扫码查看车辆信息</string>
    <string name="xm_main_driver_info_tip">扫码查看司机信息</string>
    <string name="xm_main_route_info_tip">扫码查看行程信息</string>
    <string name="xm_main_default_info">-&#160;-&#160;-&#160;-</string>
    <string name="xm_main_dialog_countdown_pay_info">支付倒计时(%1$ss)</string>
    <string name="xm_main_dialog_countdown_car_info">车辆信息倒计时(%1$ss)</string>
    <string name="xm_main_dialog_countdown_driver_info">司机信息倒计时(%1$ss)</string>
    <string name="xm_main_dialog_countdown_route_info">行程信息倒计时(%1$ss)</string>
    <string name="xm_main_dialog_countdown_route">行程信息倒计时(%1$ss)</string>
    <string name="xm_main_dialog_countdown">倒计时(%1$ss)</string>
    <string name="xm_message_notify">通知消息</string>
    <string name="xm_message_inquire">询问消息</string>
    <string name="xm_message_friend_tip">空空如也，点击图片可刷新哦</string>
    <string name="xm_rv_footer_end_tip">哎呀，已经见底啦</string>
    <string name="xm_rv_footer_loading_tip">加载中...</string>
    <string name="xm_message_inquire_answer_title">当前反馈</string>
    <string name="xm_taxi_status_stop">停</string>
    <string name="xm_pay_before">请打开</string>
    <string name="xm_pay_after">扫一扫</string>
    <string name="xm_pay_alipay">支付宝</string>
    <string name="xm_pay_wechat_pay">微信</string>
    <string name="xm_pay_union_pay">银联</string>
    <string name="dianzhao_grabbing_order_success">抢单成功</string>
    <string name="start_exe">开始执行</string>
    <string name="dz_all">全部</string>
    <string name="dz_complete">已完成</string>
    <string name="dz_cancel">已取消</string>
    <string name="dz_execute">未完成</string>
    <string name="xm_export_data_start_time">开始时间</string>
    <string name="xm_export_data_end_time">结束时间</string>
    <string name="xm_export_data_event_type">事件类型</string>
    <string name="xm_export_date_channel">通道号</string>
    <string name="xm_h5_reload">重新加载</string>
    <string name="dz_passenger_default_name">""</string>
    <string name="dz_btn_pickup_passenger">接到乘客</string>
    <string name="dz_title_to_destination">前往目的地</string>
    <string name="dz_btn_navi_to_destination">目的地导航</string>
    <string name="dz_finish">结束行程</string>
    <string name="dz_time">召车时间：</string>
    <string name="dz_position">召车地点：</string>
    <string name="dz_destination">目  的  地：</string>
    <string name="dz_abandon">放弃(%1$ds)</string>
    <string name="dz_path_calculate">距离计算中...</string>
    <string name="dz_pick_up_passenger">接驾中</string>
    <string name="dz_call_passenger">联系乘客</string>
    <string name="dz_order_number">订单号</string>
    <string name="dz_default_passenger_name">匿名</string>
    <string name="hecheng_unit_price">单价:</string>
    <string name="hecheng_mileage">里程:</string>
    <string name="hecheng_wait_time">等待:</string>
    <string name="hecheng_discount">折扣:</string>
    <string name="hecheng_accrued_expenses">应付:</string>
    <string name="hecheng_out_of_package_expenses">实付:</string>
    <string name="hecheng_add_passenger">添  加  乘  客</string>

    <string name="sign_type_account">工号登录</string>
    <string name="sign_type_face">人脸登录</string>
    <string name="sign_type_qrcode">扫码登录</string>
    <string name="sign_type_temporary_driver">临时司机</string>
    <string name="sign_type_engineer">工程人员</string>
    <string name="face_detecting">人脸识别中...</string>
    <string name="sign_qr_code_tip">请扫描二维码进行登录</string>
    <string name="main_meter_speed">低速</string>
    <string name="main_meter_night">夜间</string>
    <string name="main_meter_pause">暂停</string>
    <string name="home_hot_area_title">排队车辆数</string>
    <string name="home_operation_data_title">当班营收金额</string>
    <string name="home_operation_data_rank">排名</string>
    <string name="home_operation_data_count">次数</string>
    <string name="home_role_visitor">游客</string>
    <string name="home_role_super_admin">超级用户</string>
    <string name="home_role_operating_personnel">运维</string>
    <string name="media_last_song_tip">上一曲  :  无歌曲</string>
    <string name="media_next_song_tip">下一曲  :  无歌曲</string>
    <string name="media_no_song">无歌曲</string>
    <string name="media_no_fm">未知频道</string>
    <string name="media_menu_fm">FM</string>
    <string name="media_menu_mp3">MP3</string>
    <string name="home_light_adjust">亮度调节</string>
    <string name="home_defaunt_0_0">0.0</string>
    <string name="home_defaunt_00">00</string>
    <string name="home_defaunt_000">000</string>
    <string name="home_defaunt_0000">0000</string>
    <string name="media_fm_unit">MHZ</string>
    <string name="main_logout_text">退出本次登录</string>
    <string name="home_page_first">第一页</string>
    <string name="home_page_second">第二页</string>
    <string name="title_function_menu">功能菜单</string>
    <string name="home_driver_name">司　　机:&#160;&#160;&#160;%1$s</string>
    <string name="home_driver_no">资格证号:&#160;&#160;&#160;%1$s</string>
    <string name="home_supervise">监督电话:&#160;&#160;&#160;%1$s</string>
    <string name="message_delete_all">删除所有</string>
    <string name="message_if_delete_all">您要删除所有通知?</string>
    <string name="permit_title">出租车汽车驾驶员服务资格证</string>
    <string name="payment_unit_price">单价(元/公里):</string>
    <string name="payment_mileage">里程(公里):</string>
    <string name="payment_date">   日期:</string>
    <string name="payment_up_time">上车时间:</string>
    <string name="payment_down_time">下车时间:</string>
    <string name="payment_wait_time">等候时间:</string>
    <string name="payment_time">计时(分秒):</string>
    <string name="payment_print_invoice">打印发票</string>
    <string name="payment_sum_title">金额(元)</string>
    <string name="home_message_count">99+</string>
    <string name="home_operation_not_allow">功能未开通</string>
    <string name="no_picture">暂无照片</string>
    <string name="menu_light_ajust_day">白天亮度调节</string>
    <string name="menu_light_ajust_night">夜间亮度调节</string>
    <string name="music_service_unbind">音乐后台服务已断开</string>
    <string name="media_auto_search">自动搜台</string>
    <string name="media_manual_search">手动搜台</string>
    <string name="media_clear">清空</string>
    <string name="home_out_of_999">999+</string>
    <string name="hot_info_unknow_address">未知区域</string>
    <string name="revenue_rank">营收排名</string>
    <string name="revenue_pricing_mileage">营运里程</string>
    <string name="revenue_mileage">总里程</string>
    <string name="revenue_count">次数</string>
    <string name="revenue_history_revenue">历史营收</string>
    <string name="revenue_history_amount">金额</string>
    <string name="revenue_history_pricing_mileage">营运里程</string>
    <string name="revenue_history_mileage">里程</string>
    <string name="revenue_history_count">次数</string>
    <string name="revenue_title_id">序号</string>
    <string name="revenue_title_type">类型</string>
    <string name="revenue_get_on_off_time">上下客</string>
    <string name="menu_camera">预览/回放</string>
    <string name="menu_production_testing">生产检测</string>
    <string name="menu_algorithm_setting">算法设置</string>

    <string name="cotake_driver_name">姓　名：</string>
    <string name="cotake_driver_car_no">车牌号：</string>
    <string name="cotake_driver_supervisor">监　督：</string>
    <string name="base_main_menu_text_sharing">合  乘</string>
    <string name="cotake_takeup">上车</string>
    <string name="cotake_takeoff">下车</string>
    <string name="cotake_print">下车并打票</string>
    <string name="cotake_passenger">乘客</string>
    <string name="cotake_mileage">里程: </string>
    <string name="cotake_time">时间: </string>
    <string name="cotake_cope_with">金额: </string>
    <string name="cotake_actual_pay">实付金额: </string>
    <string name="cotake_unit_price">单价: </string>
    <string name="cotake_discount_rate">(折扣率: </string>
    <string name="cotake_discount_rate2">)</string>
    <string name="cotake_money_unit">元/公里</string>
    <string name="cotake_mileage_unit">公里</string>
    <string name="cotake_aboard_time">上车:</string>

    <string name="sun">日</string>
    <string name="mon">一</string>
    <string name="tue">二</string>
    <string name="wed">三</string>
    <string name="thu">四</string>
    <string name="fri">五</string>
    <string name="sat">六</string>

    <string name="default_unknow_price">--</string>
</resources>

    <!--
    1個漢字 = 4個&#160; = 4個&#8197; = 1個&#12288; = 2個&#8194;-->
