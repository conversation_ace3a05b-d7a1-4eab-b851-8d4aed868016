/*
 * 文 件 名:  FilesTransInfo.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2012-11-19
 * 文件描述:  定义待传输的文件信息封装类，该类对象可以保存到sharedprefers文件中。
 *****************************修改记录********************************
 *
 * 修 改 者:
 * 修改单号： [可选]
 * 修改描述:
 *
 *********************************************************************
 */
package com.yaxon.telematics.service.protocol.monitor;

/**
 * 封装待传输的文件信息，包括文件名，当前已经保存的文件包数，文件总包数
 *
 * <AUTHOR>
 * @version V1.0，2012-11-19
 * @see
 * @since V1.0.1
 */
public class FilesTransInfo {
    /**
     * 文件名
     */
    public String mFileNameString = null;
    /**
     * 当前接收的包数
     */
    public int mCurrentPackage = -1;
    /**
     * 文件总包数
     */
    public int mTotalPackages = 0;
    /**
     * 文件数据总长度
     */
    public int mFileDataLen = 0;
    /**
     * 保存接收到的包序号信息，如果对应位置的值为1表示接收到，为0表示未接收到
     */
    public byte[] mPackageNums = null;

    public FilesTransInfo() {
        mFileNameString = null;
        mCurrentPackage = -1;
        mTotalPackages = 0;
        mFileDataLen = 0;
        mPackageNums = null;
    }

    public FilesTransInfo(String fileName, int currentPackage, int totalPackage) {
        mFileNameString = fileName;
        mCurrentPackage = currentPackage;
        mTotalPackages = totalPackage;
    }

    public boolean isFilesTransEnd() {
        if (null == mPackageNums) {
            return false;
        }

        for (int i = 0; i < mPackageNums.length; i++) {
            if (0 == mPackageNums[i]) {
                return false;
            }
        }

        return true;
    }
}