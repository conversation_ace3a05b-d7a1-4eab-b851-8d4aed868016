/*
 * 文 件 名:  ResourceDescription.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2013-5-28
 * 文件描述:  定义系统音频资源描述类。
 *****************************修改记录********************************
 * 
 * 修 改 者:  
 * 修改单号： [可选]
 * 修改描述:
 *
 *********************************************************************  
 */
package com.yaxon.voiceswitcher.aidl;

/**
 * 描述系统所使用的音频资源，例如麦克、扬声器；
 * 在这里，将这些音频资源抽象为各种输入通道；这些通道用不同的位来描述，
 * 在申请时，不同资源可以同时申请（通过或操作）
 * <br>
 * 如果有新的资源，后续会更新
 *
 * <AUTHOR>
 * @version V1.0，2013-5-28
 * @see
 */
public class ResourceDescription {
    /**
     * MIC通道
     * <br>
     * MIC切换到GSM
     */
    public static final int MIC_CHANNEL_GSM = 0x100;
    /**
     * MIC通道
     * <br>
     * MIC切换到系统
     */
    public static final int MIC_CHANNEL_SYSTEM = 0x200;
    /**
     * MIC通道
     * <br>
     * MIC切换到蓝牙
     */
    public static final int MIC_CHANNEL_BLUETOOTH = 0x300;
    /**
     * MIC通道
     * <br>
     * MIC切换到其他外部设备
     */
    public static final int MIC_CHANNEL_OTHER = 0x400;

    /**
     * 小喇叭通道
     * <br>
     * 小喇叭切换到其它外接设备（例如110R）
     */
    public static final int MINOR_SPEAKER_CHANNEL_OTHER = 0x001;
    /**
     * 小喇叭通道
     * <br>
     * 小喇叭切换到系统
     */
    public static final int MINOR_SPEAKER_CHANNEL_SYSTEM = 0x002;

    /**
     * 大喇叭通道
     * <br>
     * 大喇叭的输入通道切换到系统
     */
    public static final int MAIN_SPEAKER_CHANNEL_SYSTEM = 0x010;
    /**
     * 大喇叭通道
     * <br>
     * 大喇叭的输入通道切换到蓝牙
     */
    public static final int MAIN_SPEAKER_CHANNEL_BLUETOOTH = 0x020;
    /**
     * 大喇叭通道
     * <br>
     * 大喇叭的输入通道切换到AUX
     */
    public static final int MAIN_SPEAKER_CHANNEL_AUX = 0x030;
    /**
     * 大喇叭通道
     * <br>
     * 大喇叭的输入通道切换到DVR
     */
    public static final int MAIN_SPEAKER_CHANNEL_DVR = 0x040;
    /**
     * 大喇叭通道
     * <br>
     * 大喇叭的输入通道切换到GSM
     */
    public static final int MAIN_SPEAKER_CHANNEL_GSM = 0x050;
    /**
     * 大喇叭通道
     * <br>
     * 大喇叭的输入通道切换到FM
     */
    public static final int MAIN_SPEAKER_CHANNEL_FM = 0x060;
    /**
     * 大喇叭通道
     * <br>
     * 大喇叭的输入通道切换到Mute
     */
    public static final int MAIN_SPEAKER_CHANNEL_MUTE = 0x070;

    /**
     * 优先级定义
     * <br>
     * 高优先级
     */
    public static final int PRIORITY_HIGH_BASE = 200;
    /**
     * 优先级定义
     * <br>
     * 中优先级
     */
    public static final int PRIORITY_MIDDLE_BASE = 100;
    /**
     * 优先级定义
     * <br>
     * 中优先级
     */
    public static final int PRIORITY_LOW_BASE = 1;


    /**
     * 优先级定义
     * <br>
     * 校车TTS优先级
     */
    public static final int PRIORITY_TTS_SCHOOLBUS = PRIORITY_HIGH_BASE + 100;// 校车TTS优先级
    /**
     * 优先级定义
     * <br>
     * 喊话器优先级
     */
    public static final int PRIORITY_SPEAKER = PRIORITY_HIGH_BASE + 90;// 喊话器优先级

    /**
     * 优先级定义
     * <br>
     * 电话（普通电话，蓝牙）优先级
     */
    public static final int PRIORITY_PHONE = PRIORITY_MIDDLE_BASE + 100;// 电话（普通电话，蓝牙）优先级
    /**
     * 优先级定义
     * <br>
     * 录音优先级
     */
    public static final int PRIORITY_RECORD = PRIORITY_MIDDLE_BASE + 90;//
    /**
     * 优先级定义
     * <br>
     * 调度TTS优先级
     */
    public static final int PRIORITY_TTS_EMERGENCY = PRIORITY_MIDDLE_BASE + 50; // 调度TTS优先级
    /**
     * 优先级定义
     * <br>
     * 普通TTS优先级
     */
    public static final int PRIORITY_TTS_COMMON = PRIORITY_MIDDLE_BASE + 20; // 普通TTS优先级
    /**
     * 优先级定义
     * <br>
     * 导航优先级
     */
    public static final int PRIORITY_SHORN_NAVIGATE = PRIORITY_MIDDLE_BASE + 10; // 小喇叭导航优先级

    /**
     * 优先级定义
     * <br>
     * 媒体优先级（音视频播放、FM、录音测试）
     */
    public static final int PRIORITY_MEDIA = PRIORITY_LOW_BASE;

    /**
     * 音频切换程序发送到各个应用程序的广播（控制应用程序停止或者暂停）Action
     */
    public static final String COMMAND_ACTION = "com.yaxon.voiceswitcher";
    /**
     * 广播Intent中携带数据的关键字的定义
     * <br>
     * 处理广播的应用程序标志
     */
    public static final String COMMAND_TARGET = "RECEIVER";
    /**
     * 广播Intent中携带数据的关键字的定义
     * <br>
     * 命令字
     */
    public static final String COMMAND_VALUE = "VALUE";
    /**
     * 命令字
     * <br>
     * 暂停
     */
    public static final int COMMAND_VALUE_PAUSE = 0;
    /**
     * 命令字
     * <br>
     * 重新运行
     */
    public static final int COMMAND_VALUE_RESUME = 1;
    /**
     * 命令字
     * <br>
     * 停止
     */
    public static final int COMMAND_VALUE_STOP = 2;
    /**
     * 绑定音频切换服务的Action
     */
    public static final String ACTION_BIND_VOICE_SWITCHER_SERVICE =
            "com.yaxon.voiceswitcher.IVoiceSwitchService";
    /**
     * 音频切换服务启动之后发送的广播Action
     */
    public static final String ACTION_VOICE_SWITCHER_SERVICE_START =
            "com.yaxon.voiceswitcher.start";
}
