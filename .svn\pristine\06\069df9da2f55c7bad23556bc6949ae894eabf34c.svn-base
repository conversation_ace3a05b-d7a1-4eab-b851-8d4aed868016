package com.yaxon.telematics.service.util;

import com.yaxon.telematics.jni.YxSystemUtil;

public class GpioControl {
    //返回值:0成功，-1失败
    public static boolean setGpioHigh(int io) {
        int ret = 0;

        byte[] cmd = new byte[]{(byte) io, 0, 0, 0, 1};

        ret = YxSystemUtil.commandControl(1, cmd, cmd.length);

        if (ret == 0) {
            return true;
        } else {
            return false;
        }
    }

    //返回值:0成功，-1失败
    public static boolean setGpioLow(int io) {
        int ret = 0;

        byte[] cmd = new byte[]{(byte) io, 0, 0, 0, 0};

        ret = YxSystemUtil.commandControl(1, cmd, cmd.length);

        if (ret == 0) {
            return true;
        } else {
            return false;
        }
    }

    //返回值:1为高电平，0为低电平，-1表示失败
    public static int getGpioState(int io) {

        byte[] cmd = new byte[]{(byte) io, 0, 0, 0};

        int ret = 0;
        ret = YxSystemUtil.commandControl(2, cmd, cmd.length);

        return ret;
    }
}
