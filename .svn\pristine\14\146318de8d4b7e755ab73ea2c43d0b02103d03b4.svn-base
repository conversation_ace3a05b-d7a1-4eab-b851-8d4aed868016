<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
  xmlns:android="http://schemas.android.com/apk/res/android"
  android:orientation="vertical"
  android:layout_width="fill_parent"
  android:layout_height="fill_parent"
   android:background="@drawable/main_background">
     <LinearLayout
    	android:id="@+id/title_bar"
	    android:orientation="horizontal"
	    android:layout_width="fill_parent"
	    android:layout_height="wrap_content"
	    android:layout_alignParentTop="true">
	    
	    	<include layout="@layout/main_title_update"></include>
    </LinearLayout>
     <LinearLayout 
	    android:orientation="horizontal"
	    android:layout_width="fill_parent"
	    android:layout_height="fill_parent"
	    android:background="@drawable/check_layout_bg"
	    android:padding="0dip"
	    android:layout_margin="10dip"
	    android:gravity="center_vertical"
		android:layout_gravity="center"
  	>
	    	 	<ListView 
	  		android:id="@+id/lv_apk_item_container"
		    android:orientation="vertical"
		    android:layout_width="fill_parent"
		    android:layout_height="fill_parent"	   
		    android:layout_gravity="center_vertical"		   
		    android:layout_margin="5dip"		    
	  	></ListView>
  		</LinearLayout>
</LinearLayout>
