<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="75px">

    <ImageView
        android:id="@+id/project_menu_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="35px"
        android:layout_centerVertical="true"
        android:background="@drawable/system_tts_selector" />

    <LinearLayout
        android:id="@+id/layoutMenu"
        android:layout_width="match_parent"
        android:layout_height="75px"
        android:layout_toRightOf="@+id/project_menu_image"
        android:orientation="vertical"
        android:gravity="center_vertical"
        android:layout_marginLeft="20px">

        <TextView
            android:id="@+id/project_menu_data"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            style="@style/TitleText" />

        <TextView
            android:id="@+id/project_menu_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            style="@style/SystemCheckText" />
    </LinearLayout>

    <TextView
        android:id="@+id/txtResult"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginRight="20px"
        android:layout_centerVertical="true"
        android:text="@string/product_test_chengdu_taxi_2d_mileag_pulse_unknown"
        style="@style/TitleText" />

    <LinearLayout
        android:visibility="invisible"
        android:id="@+id/layoutSwitchBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginRight="20px"
        android:layout_centerVertical="true">

        <Button
            android:id="@+id/btnStart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:background="@drawable/kaitian2d_test_btn_x"
            style="@style/TitleText"
            android:text="@string/product_test_chengdu_taxi_2d_start"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:clickable="false" />

        <Button
            android:id="@+id/btnStop"
            android:visibility="invisible"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:background="@drawable/kaitian2d_test_btn_x"
            style="@style/TitleText"
            android:text="@string/product_test_chengdu_taxi_2d_stop"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:clickable="false" />

    </LinearLayout>

</RelativeLayout>
