package com.yaxon.business.revenue.activity;

import android.graphics.Typeface;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.jeremyliao.liveeventbus.LiveEventBus;
import com.yaxon.adapter.RevenueAdapter;
import com.yaxon.base.R;
import com.yaxon.base.TitleBarActivity;
import com.yaxon.base.databinding.ActivityRevenueBinding;
import com.yaxon.business.home.activity.HomeActivity;
import com.yaxon.business.login.LogType;
import com.yaxon.business.presenter.DriverPresenter;
import com.yaxon.business.revenue.viewmodel.RevenueViewModel;
import com.yaxon.common.LiveEventBusKey;
import com.yaxon.telematics.service.taxi.aidl.YxDriverInfo;
import com.yaxon.telematics.service.taxi.aidl.YxRevenueDetailInfo;
import com.yaxon.telematics.service.taxi.aidl.YxRevenueInfo;
import com.yaxon.telematics.service.taxi.aidl.YxRevenueReqInfo;
import com.yaxon.utils.Util;
import com.yaxon.view.calendarview.Calendar;
import com.yaxon.view.calendarview.CalendarView;
import com.yaxon.view.calendarview.utils.CalendarUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.yaxon.utils.StringUtil.div;

public class RevenueActivity extends TitleBarActivity<ActivityRevenueBinding, RevenueViewModel> {
    private int[] cDate = CalendarUtil.getCurrentDate();
    private Typeface mTypeface;
    //当前所在页面
    private int curPager = 0;

    private RevenueAdapter adapter;

    private List<YxRevenueDetailInfo> dataList = new ArrayList<>();

    @Override
    protected void setObjectVariable() {

    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_revenue;
    }

    @Override
    protected boolean enableShowWithMenuLayout() {
        return false;
    }

    @Override
    public void initViews(@Nullable Bundle savedInstanceState) {
        super.initViews(savedInstanceState);

        showTitleName("当班详情");
        setHomeImageResource(R.drawable.icon_btn_back);
        getDataBinding().iRevenue.btHistoryRevenue.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                curPager = 1;
                getDataBinding().vfRevenue.showNext();
            }
        });
        initTypeFace();

        adapter = new RevenueAdapter(this, dataList);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this,
                LinearLayoutManager.VERTICAL, false);
        getDataBinding().rvDetailRevenue.setLayoutManager(layoutManager);
        getDataBinding().rvDetailRevenue.setAdapter(adapter);

        initCalendar();
    }

    /**
     * 初始化字体
     */
    private void initTypeFace(){
        mTypeface = Typeface.createFromAsset(getAssets(), "digifaw.ttf");
        getDataBinding().iRevenue.tvAmount.setTypeface(mTypeface);
        getDataBinding().iRevenue.tvRank.setTypeface(mTypeface);
        getDataBinding().iRevenue.tvPricingMileage.setTypeface(mTypeface);
        getDataBinding().iRevenue.tvMileage.setTypeface(mTypeface);
        getDataBinding().iRevenue.tvCount.setTypeface(mTypeface);
    }

    /**
     * 初始化日历
     */
    private void initCalendar(){
        getDataBinding().setBeSelectedDate(cDate[0] + "年" + cDate[1] + "月");
        getDataBinding().iHistoryRevenue.cvCalendar.setOnCalendarSelectListener(new CalendarView.OnCalendarSelectListener() {
            @Override
            public void onCalendarOutOfRange(Calendar calendar) {

            }

            @Override
            public void onCalendarSelect(Calendar calendar, boolean isClick) {
                //点击选中日期，处理相关业务请求
                if (isClick){

                    if(Util.isFastClick()) {
                        String strDate = new SimpleDateFormat("yyMMdd").format(calendar.getTimeInMillis());
                        getRevenueInfo(strDate, DriverPresenter.getInstance().getCurYxDriverInfo().mDriverNum, false);
						
						getDataBinding().setAmount("0.0");
	                    //计价里程
	                    getDataBinding().setPricingMileage("0.0");
	                    //总里程
	                    getDataBinding().setMileage("0");
	                    //次数
	                    getDataBinding().setCount(0);
	                    dataList.clear();
	                    adapter.notifyDataSetChanged();
                    }
                }
            }
        });

        getDataBinding().iHistoryRevenue.cvCalendar.setOnMonthChangeListener(new CalendarView.OnMonthChangeListener() {
            @Override
            public void onMonthChange(int year, int month) {
                getDataBinding().setBeSelectedDate(year + "年" + month + "月");
            }
        });

        getDataBinding().iHistoryRevenue.ibArrowLeft.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDataBinding().iHistoryRevenue.cvCalendar.scrollToPre();
            }
        });

        getDataBinding().iHistoryRevenue.ibArrowRight.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDataBinding().iHistoryRevenue.cvCalendar.scrollToNext();
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK){
            if (curPager == 1){
                curPager = 0;
                getDataBinding().vfRevenue.showPrevious();
                //initData();
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected Class<?> getBackToClass() {
        return HomeActivity.class;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.ibHome:
                if (curPager == 1){
                    curPager = 0;
                    getDataBinding().vfRevenue.showPrevious();
                    //initData();
                    return;
                }

                break;
            default:
                break;
        }
        super.onClick(v);
    }

    @Override
    protected void initData() {
        //营收数据
        getViewModel().revenueInfo.observe(this, new Observer<YxRevenueInfo>() {
            @Override
            public void onChanged(YxRevenueInfo yxRevenueInfo) {
                //当班营收金额
                getDataBinding().setAmount(div(yxRevenueInfo.mSum, 100, 1) + "");
                //营运里程
                getDataBinding().setPricingMileage(div(yxRevenueInfo.mPricingMileage, 1000, 1) + "");
                //总里程
                getDataBinding().setMileage(div(yxRevenueInfo.mMileage, 1000, 1) + "");
                //次数
                getDataBinding().setCount(yxRevenueInfo.items.size());

                dataList.clear();
                Collections.reverse(yxRevenueInfo.items);
                dataList.addAll(yxRevenueInfo.items);
                adapter.notifyDataSetChanged();
            }
        });

        //TODO 请求当天数据
        String strDate = new SimpleDateFormat("yyMMdd").format(new Date());
        getRevenueInfo(strDate, DriverPresenter.getInstance().getCurYxDriverInfo().mDriverNum, true);

        LiveEventBus.get(LiveEventBusKey.KEY_DRIVER_RANK, YxDriverInfo.class).observeSticky(this, new Observer<YxDriverInfo>() {
            @Override
            public void onChanged(YxDriverInfo info) {
                //司机排名
                getDataBinding().setRank(info.mDriverRank);
            }
        });
    }

    private void getRevenueInfo(String strDate, String strDriverNo, boolean bRank){

        //获取运营数据
        YxRevenueReqInfo inData = new YxRevenueReqInfo();
        inData.mGetOnTime = strDate + "000000";
        inData.mGetOffTime = strDate + "235959";
        inData.mDriverNo = strDriverNo;
        inData.mWithDetailInfo = 1;
        if(DriverPresenter.getInstance().getCurYxDriverInfo().mLogType == LogType.ENGINEER.getType()) {
            getViewModel().getRevenueInfo(inData, 0);
        }
        else if(DriverPresenter.getInstance().getCurYxDriverInfo().mLogType == LogType.TEMP.getType()) {
            getViewModel().getRevenueInfo(inData, 1);
        }
        else {
            getViewModel().getRevenueInfo(inData, 2);
        }

        //获取司机排名
        if(bRank) {
            getViewModel().getDriverRank(DriverPresenter.getInstance().getCurYxDriverInfo().mDriverNum);
        }
    }
}
