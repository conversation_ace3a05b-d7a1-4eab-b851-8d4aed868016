package com.yaxon.business.home.viewmodel;


//Modify by zxb on 2021.11.3 px3
import androidx.annotation.NonNull;
//import android.annotation.NonNull;


import android.app.Application;
import android.util.Log;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.yaxon.base.common.BaseViewModel;
import com.yaxon.base.maintain.recorder.DrivingDataAdapter;
import com.yaxon.base.rxjava.task.RxAsyncTask;
import com.yaxon.base.rxjava.utils.RxJavaUtils;
import com.yaxon.business.home.player.PlayerProxy;
import com.yaxon.business.home.player.PlayerType;
import com.yaxon.business.presenter.CommonPresenter;
import com.yaxon.business.state.StatusConstants;
import com.yaxon.business.state.StatusManager;
import com.yaxon.common.CommDef;
import com.yaxon.common.LogTag;
import com.yaxon.datasource.api.ControlApi;
import com.yaxon.datasource.database.Appdatabase;
import com.yaxon.datasource.sharepreference.SharedPrefsManager;
import com.yaxon.datasource.sharepreference.SharedPrefsTag;
import com.yaxon.musicplayer.model.MusicInfo;
import com.yaxon.telematics.service.aidl.main.DispatchInfo;
import com.yaxon.telematics.service.taxi.aidl.YxHotAreaInfo;
import com.yaxon.telematics.service.taxi.aidl.YxRevenueInfo;
import com.yaxon.telematics.service.taxi.aidl.YxRevenueReqInfo;
import com.yaxon.utils.LoggerUtil;
import com.yaxon.utils.MusicUtil;
import com.yaxon.utils.StringUtil;
import com.yaxon.utils.ToastUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Observer;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.ObservableSource;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;

public class HomeViewModel extends BaseViewModel implements StatusManager.DeviceStateChangeListener {
    private static final String TAG = HomeViewModel.class.getSimpleName();
    private Disposable disposableTimer;
    private Disposable disposableHotAreaTimer;
    private Disposable disposableRevenueTimer;

    private static final int INTERVAL_TIME = 1;
    /** MP3播放器定时更新进度条 */
    public MutableLiveData<Long> timer = new MutableLiveData<>();
    /** 页面切换 */
    public MutableLiveData<Integer> pagerSwitcher = new MutableLiveData<>(0);
    /** 多媒体类型变化 */
    public MutableLiveData<PlayerType> mediaType = new MutableLiveData<>();
    /** 消息通知条数 */
    public ObservableField<String> messageCount = new ObservableField<>();
    /** 歌曲列表 */
    public MutableLiveData<List<MusicInfo>> songLists = new MutableLiveData<>();
    /** 音乐播放 */
    public MutableLiveData<Boolean> isMusicPlaying = new MutableLiveData<>();
    /** 热点区域 */
    public MutableLiveData<List<YxHotAreaInfo>> hotAreaInfos = new MutableLiveData<>();
    /** 营收数据*/
    public MutableLiveData<YxRevenueInfo> revenueInfo = new MutableLiveData<>();

    public HomeViewModel(@NonNull Application application) {
        super(application);
        mediaType.setValue(PlayerType.valueOf(SharedPrefsManager.getInt(SharedPrefsTag.KEY_MEDIA_TYPE)));
    }

    /**
     * 查询未读信息的条数
     */
    public void queryUnreadMessageCount() {
        addSubscribe(Appdatabase.getInstance().getMessageDao()
                .queryUnreadMessageCount(0)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<List<DispatchInfo>>() {
                    @Override
                    public void accept(List<DispatchInfo> dispatchInfos) throws Exception {
                        String displayCount = "";
                        if (dispatchInfos != null && dispatchInfos.size() > 0){
                            if (dispatchInfos.size() > 99){
                                displayCount = "99+";
                            } else {
                                displayCount = dispatchInfos.size() + "";
                            }
                        }
                        messageCount.set(displayCount);
                    }
                }));
    }


    /**
     * 启动定时器
     */
    public void startTimer() {
        if (disposableTimer != null && !disposableTimer.isDisposed()){
            return;
        }

        disposableTimer =
                Observable.interval(0, INTERVAL_TIME, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(new Consumer<Long>() {
                            @Override
                            public void accept(Long aLong) throws Exception {
                                timer.setValue(aLong);
                            }
                        });
    }

    /**
     * 销毁定时器
     */
    public void stopTimer(){
        if (disposableTimer != null && !disposableTimer.isDisposed()){
            disposableTimer.dispose();
            disposableTimer = null;
        }
    }

    /**
     * 刷新歌曲列表
     */
    public void updateSongList(){
        addSubscribe(RxJavaUtils.executeAsyncTask(new RxAsyncTask<String, List<MusicInfo>>("") {
            @Override
            public void doInUIThread(List<MusicInfo> musicInfos) {
                songLists.setValue(musicInfos);
            }

            @Override
            public List<MusicInfo> doInIOThread(String s) {
                PlayerProxy.getInstance().refreshPlayList();
                List<MusicInfo> localMusicInfos = PlayerProxy.getInstance().getSongList();
                if (localMusicInfos == null || localMusicInfos.size() <= 0){
                    return localMusicInfos;
                }

                for (MusicInfo info : localMusicInfos){
                    MusicUtil.getFullInfoWithPath(info);
                }

                return localMusicInfos;
            }
        }));
    }

    /**
     * 获取营收数据
     */
    public void getRevenueInfo(YxRevenueReqInfo yxRevenueReqInfo, int type){
        //LoggerUtil.w(TAG, "请求运营数据，参数："+yxRevenueReqInfo);
        if (!StatusManager.getInstance().isLinked()){
            LoggerUtil.w(TAG, "warning, the Link is not connected now");
            return;
        }
        addSubscribe(RxJavaUtils.executeAsyncTask(
                new RxAsyncTask<YxRevenueReqInfo, YxRevenueInfo>(yxRevenueReqInfo) {
                    @Override
                    public void doInUIThread(YxRevenueInfo yxRevenueInfo) {
                        if(yxRevenueInfo != null){
                            //Log.e(TAG, yxRevenueInfo.toString());
                            revenueInfo.setValue(yxRevenueInfo);
                        }
                    }
                    @Override
                    public YxRevenueInfo doInIOThread(YxRevenueReqInfo yxRevenueReqInfo) {
                        return ControlApi.getInstance().reqRevenueInfo(yxRevenueReqInfo, type);
                    }
                }));
    }

    /**
     * 请求司机排名
     */
    public void getDriverRank(String driverNo){
        //Log.e(TAG, "请求司机排名，参数："+driverNo);
        if (!StatusManager.getInstance().isConnectedToCenter()){
            LoggerUtil.w(TAG, "warning, because the center is not connected");
            return;
        }
        addSubscribe(RxJavaUtils.executeAsyncTask(new RxAsyncTask<String, Integer>("") {
            @Override
            public void doInUIThread(Integer rank) {

                //rankInfo.setValue(rank);
            }

            @Override
            public Integer doInIOThread(String s) {
                return ControlApi.getInstance().reqDriverRank(driverNo);
            }
        }));

    }

    /**
     * 启动热点区域查询定时器
     */
    public void startHotAreaInfoTimer(){
        if (!StatusManager.getInstance().isConnectedToCenter()){
            LoggerUtil.w(TAG, "the hot area timer will be stopped, because the center is not connected");
            return;
        }

        if (disposableHotAreaTimer != null && !disposableHotAreaTimer.isDisposed()){
            LoggerUtil.w(TAG, "the hotarea timer is running");
            return;
        }

        LoggerUtil.printLog(LoggerUtil.PRINT_DBG, LogTag.TAG_HOT_AREA, "startHotAreaInfoTimer");

        int intervalTime = SharedPrefsManager.getInt(SharedPrefsTag.KEY_REFRESH_INTERVAL_TIME_WITH_HOT_AREA);
        if (intervalTime <= 0){
            intervalTime = 10;
        }
        disposableHotAreaTimer = Observable.interval(0, intervalTime, TimeUnit.MINUTES)
                .flatMap(new Function<Long, ObservableSource<List<YxHotAreaInfo>>>() {
            @Override
            public ObservableSource<List<YxHotAreaInfo>> apply(@io.reactivex.annotations.NonNull Long aLong) throws Exception {
                return CommonPresenter.getInstance().queryHotAreaInfo();
            }
        }, true).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<List<YxHotAreaInfo>>() {
            @Override
            public void accept(List<YxHotAreaInfo> yxHotAreaInfos) throws Exception {
                if (yxHotAreaInfos == null) {
                    LoggerUtil.printLog(LoggerUtil.PRINT_DBG, LogTag.TAG_HOT_AREA,
                            "yxHotAreaInfos == null");
                } else {
                    LoggerUtil.printLog(LoggerUtil.PRINT_DBG, LogTag.TAG_HOT_AREA, "#####the " +
                            "size of yxHotAreaInfos :" + yxHotAreaInfos.size());
                }

                hotAreaInfos.setValue(yxHotAreaInfos);
            }
        });

        addSubscribe(disposableHotAreaTimer);
    }

    /**
     * 停止热点区域更新的定时器
     */
    public void stopHotAreaTimer(){
        if (disposableHotAreaTimer != null && !disposableHotAreaTimer.isDisposed()){
            disposableHotAreaTimer.dispose();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        startHotAreaInfoTimer();
        hotAreaInfos.setValue(CommonPresenter.getInstance().hotAreaInfos);
        StatusManager.getInstance().addListener(this);
    }

    @Override
    public void onStop() {
        super.onStop();
        stopHotAreaTimer();
        stopTimer();
        StatusManager.getInstance().removeListener(this);
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        stopHotAreaTimer();
    }

    @Override
    public void onCenterState(int center) {
        if (center == StatusConstants.CENTER_STATE_CONNECTED){
            if (disposableHotAreaTimer == null || disposableHotAreaTimer.isDisposed()){
                startHotAreaInfoTimer();
            }
        }
    }

    @Override
    public void onLinkState(int link) {

        if(link == StatusConstants.LINK_STATE_CONNECTED){

        }
    }

}
