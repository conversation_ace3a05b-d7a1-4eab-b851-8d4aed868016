<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent" 
	android:layout_height="match_parent"
	android:orientation="vertical" 
	android:background="@drawable/main_background">
	<!-- 标题栏 -->
    <LinearLayout
    	android:id="@+id/explorer_title_bar"
	    android:orientation="horizontal"
	    android:layout_width="fill_parent"
	    android:layout_height="wrap_content"
	    android:layout_alignParentTop="true">
        <include layout="@layout/main_title_simple"></include>
    </LinearLayout>
	<LinearLayout 
		android:layout_width="match_parent"
		android:layout_height="match_parent" 
		android:layout_marginLeft="5px"
		android:layout_marginRight="5px" 
		android:layout_marginTop="5px"
		android:layout_marginBottom="5px" 
		android:background="@drawable/main_listview_background">
		<ListView 
			android:layout_width="match_parent"
			android:layout_height="match_parent" 
			android:layout_marginTop="10px"
			android:layout_marginLeft="3px" 
			android:layout_marginRight="3px"
			android:cacheColorHint="@android:color/transparent" 
			android:divider="@color/listview_seperator"
			android:dividerHeight="1px" 
			android:id="@+id/hardware_listview" />
	</LinearLayout>
</LinearLayout>