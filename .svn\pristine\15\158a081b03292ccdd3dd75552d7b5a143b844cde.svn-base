<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/dialog_bg">

    <RelativeLayout
        android:id="@+id/layoutTitle"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40px"
        android:layout_marginTop="17px">

        <ImageView
            android:id="@+id/ivTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/main_listview_btn_down_normal" />

        <TextView
            android:id="@+id/txtTitle"
            style="@style/TitleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20px"
            android:layout_toRightOf="@+id/ivTitle"
            android:text="@string/video_playback_settings" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/layoutRadio"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/layoutTitle"
        android:layout_centerVertical="true"
        android:layout_marginTop="25px"
        android:gravity="center">

        <LinearLayout
            android:id="@+id/commonWheelView"
            android:layout_width="match_parent"
            android:layout_height="210px"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <com.yaxon.yx_control.function.WheelViewVideoPlayback
                android:id="@+id/year"
                android:layout_width="125px"
                android:layout_height="wrap_content" />

            <com.yaxon.yx_control.function.WheelViewVideoPlayback
                android:id="@+id/month"
                android:layout_width="125px"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4px" />

            <com.yaxon.yx_control.function.WheelViewVideoPlayback
                android:id="@+id/channel"
                android:layout_width="125px"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4px" />
        </LinearLayout>
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="295px"
        android:paddingBottom="20px">

        <Button
            android:id="@+id/btn_ok"
            android:layout_width="100px"
            android:layout_height="40px"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="30px"
            android:background="@drawable/btn_cc_selector"
            android:text="@string/video_playback_setting_yes"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="100px"
            android:layout_height="40px"
            android:layout_alignParentRight="true"
            android:layout_marginRight="30px"
            android:background="@drawable/btn_cc_selector"
            android:text="@string/video_playback_setting_no"
            android:textColor="@color/white"
            android:textSize="24sp" />
    </RelativeLayout>

</RelativeLayout>