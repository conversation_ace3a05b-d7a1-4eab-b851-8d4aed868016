package com.yaxon.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.TextView;

import androidx.annotation.Nullable;

public class FocuseTextView extends TextView {
    public FocuseTextView(Context context) {
        super(context);
    }

    public FocuseTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public FocuseTextView(Context context, @Nullable AttributeSet attrs,
                          int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean isFocused() {
        return true;
    }
}
