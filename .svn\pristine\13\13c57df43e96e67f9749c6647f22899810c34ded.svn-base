<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="380dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/dialog_wifi_background"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:gravity="center"
            android:textSize="15sp"
            android:textColor="#333333"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp"
            android:text="设置 WLAN 热点" />


        <LinearLayout
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:orientation="horizontal"
            android:background="@drawable/wifi_shape_background_white"
            android:layout_width="match_parent"
            android:layout_height="42dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="match_parent"
                android:textColor="#333333"
                android:gravity="center"
                android:text="网络名称"/>

            <View
                android:background="#f0f0f0"
                android:layout_width="1dp"
                android:layout_height="match_parent"/>

            <EditText
                android:id="@+id/dialog_et_name"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:background="#00000000"
                android:layout_weight="1"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:gravity="center|start"
                android:textSize="15sp"
                android:ems="20"
                android:inputType="text" />



        </LinearLayout>



        <TextView
            android:text="安全性"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="12dp"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center|left"/>


        <RadioGroup
            android:id="@+id/rg_encryption_type"
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:background="@drawable/wifi_shape_background_white"
            android:layout_height="42dp" >

            <RadioButton
                android:id="@+id/rb_non"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                android:layout_weight="1"
                android:textColor="@color/black"
                android:text="无" />

            <RadioButton
                android:id="@+id/rb_wpa"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                android:textColor="@color/black"
                android:layout_weight="1"
                android:text="WPA2 PSK" />
        </RadioGroup>

        <LinearLayout
            android:id="@+id/ll_pwd"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="12dp"
            android:orientation="horizontal"
            android:background="@drawable/wifi_shape_background_white"
            android:layout_width="match_parent"
            android:layout_height="42dp">

            <TextView
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="密码"/>

            <View
                android:background="#f0f0f0"
                android:layout_width="1dp"
                android:layout_height="match_parent"/>

            <EditText
                android:id="@+id/dialog_et_pwd"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:background="#00000000"
                android:layout_weight="1"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:gravity="center|start"
                android:textSize="15sp"
                android:ems="20"
                android:inputType="textPassword" />

            <View
                android:background="#f0f0f0"
                android:layout_width="1dp"
                android:layout_height="match_parent"/>

            <CheckBox
                android:id="@+id/radioButton"
                android:layout_marginStart="12dp"
                android:background="@drawable/wifi_radio_selector"
                android:layout_marginEnd="12dp"
                android:button="@null"
                android:layout_width="36dp"
                android:layout_height="36dp"/>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:textSize="15sp"
                android:textColor="#333333"
                android:background="@drawable/wifi_ripple_btn_bg"
                android:text="取消" />
            <View
                android:background="#f0f0f0"
                android:layout_width="1dp"
                android:layout_height="match_parent"/>
            <TextView
                android:id="@+id/tv_ok"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:textColor="@color/blue"
                android:background="@drawable/wifi_ripple_btn_bg"
                android:textSize="15sp"
                android:text="保存" />
        </LinearLayout>


    </LinearLayout>
</FrameLayout>
