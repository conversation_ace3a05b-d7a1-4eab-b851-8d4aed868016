<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
  xmlns:android="http://schemas.android.com/apk/res/android"
  android:orientation="vertical"
  android:layout_width="fill_parent"
  android:layout_height="fill_parent"
  android:background="@drawable/main_background">
    <LinearLayout
    	android:id="@+id/title_bar"
	    android:orientation="horizontal"
	    android:layout_width="fill_parent"
	    android:layout_height="wrap_content"
	    android:layout_alignParentTop="true">
	    
	    	<include layout="@layout/main_title_simple"></include>
    </LinearLayout>
    
    <LinearLayout 
   	  android:layout_width="fill_parent"
	  android:layout_height="fill_parent"
	  android:orientation="horizontal"
	  android:layout_weight="1"
    >
     <ListView
	    android:id="@+id/list_gprs"
	    android:layout_width="fill_parent"
	    android:layout_height="wrap_content"
	    android:layout_toLeftOf="@+id/button"
	    android:padding="2px"
	    android:background="@drawable/list_bg"
	    android:cacheColorHint="#00000000"
	    android:saveEnabled="false"
		android:scrollbars="none"
		android:layout_margin="10px"
		android:divider="@color/listview_seperator"
		android:dividerHeight="1px"
	  />
	  </LinearLayout>
	  
	    <LinearLayout
		  android:layout_width="fill_parent"
		  android:layout_height="wrap_content"
		  android:layout_gravity="bottom"
		  android:orientation="horizontal"
		  android:background="@drawable/gprs_set_btn_bg"
		  android:gravity="center_vertical">
		  <Button 
		  android:id="@+id/btn_save"
		  android:layout_width="wrap_content"
		  android:layout_height="wrap_content"
		  android:textColor="@android:color/white"
		  android:textSize="20sp"
		  android:text="@string/save"
		  android:layout_marginLeft="20px"
		  android:background="@drawable/btn_selector"
		  android:onClick="onClick"
		  />
		  <LinearLayout
		  android:layout_width="fill_parent"
		  android:layout_height="wrap_content"
		  android:orientation="vertical">
			  <Button
			  android:id="@+id/btn_cancel"
			  android:layout_width="wrap_content"
			  android:layout_height="wrap_content"
			  android:textColor="@android:color/white"
			  android:textSize="20sp"
			  android:text="@string/main_cancel"
			  android:layout_marginRight="20px"
			  android:background="@drawable/btn_selector"
			  android:onClick="onClick"
			  android:layout_gravity="right"
			  />
		  </LinearLayout>
	  </LinearLayout>
</LinearLayout>
