package com.yaxon.telematics.service.taxi.aidl;

import java.util.ArrayList;
import java.util.List;

import com.yaxon.telematics.service.annotation.ByteArrayOrm;
import com.yaxon.telematics.service.annotation.FieldType;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 调度屏查询预约大厅订单信息请求体
 * 字段类型	字段长度	备注
 * 纬度	4	大端模式，以度为单位的纬度值乘以 10 的 6 次方，精确到百万分之一度
 * 经度	4	大端模式，以度为单位的纬度值乘以 10 的 6 次方，精确到百万分之一度
 * 订单项数	1	当前时间之后，已经抢单成功的预约电召订单数，平台做过滤条件使用
 * 订单项	N	当前时间之后，已经抢单成功的预约电召订单项，见“订单项定义”
 * 
 * 订单项定义
 * 字段类型	字段长度	备
 * 订单号	4	当前时间之后，已经抢单成功的预约电召订单项
 * 订单所在时间段	6	北京时间，固定6字节
 *                     年：取值范围00H~FFH
 *                     月：取值范围01H~0CH
 *                     日：取值范围01H~1FH
 *                     时：取值范围00H~3BH
 *                     分：取值范围00H~3BH
 *                     秒：取值范围00H~3BH
 *                     年份是以当前年份减去2000年
 * <AUTHOR>
 */
public class YxDzHallQueryInfo implements Parcelable {
	
	/** 当前位置经度 Longitude */
	@ByteArrayOrm(id = 0, primaryLength = 4, type = FieldType.INT)
	public int carLng = 0;

	/** 当前位置纬度 Latitude */
	@ByteArrayOrm(id = 1, primaryLength = 4, type = FieldType.INT)
	public int carLat = 0;
	
	public List<YxDzHallQueryItem> items = new ArrayList<YxDzHallQueryItem>();
	
	@Override
	public int describeContents() {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public void writeToParcel(Parcel dest, int flags) {
		// TODO Auto-generated method stub
		dest.writeInt(carLng);
		dest.writeInt(carLat);
		dest.writeTypedList(items);
		//dest.writeParcelableArray(items, 0);
	}
	
	public static final Parcelable.Creator<YxDzHallQueryInfo> CREATOR = new Parcelable.Creator<YxDzHallQueryInfo>() {

		@Override
		public YxDzHallQueryInfo createFromParcel(Parcel source) {
			// TODO Auto-generated method stub
			YxDzHallQueryInfo info = new YxDzHallQueryInfo();

			info.carLng = source.readInt();
			info.carLat = source.readInt();
			source.readTypedList(info.items, YxDzHallQueryItem.CREATOR);
			//info.items = (YxDzHallQueryItem[]) source.readParcelableArray(YxDzHallQueryItem.class.getClassLoader());
			return info;
		}

		@Override
		public YxDzHallQueryInfo[] newArray(int size) {
			// TODO Auto-generated method stub
			return new YxDzHallQueryInfo[size];
		}
	};

	@Override
	public String toString() {
		// TODO Auto-generated method stub
		StringBuilder sb = new StringBuilder();
		sb.append(getClass().getSimpleName()).append('@')
				.append(Integer.toHexString(hashCode()))
				.append("[ carLng : ")
				.append(carLng)
				.append(", carLat : ")
				.append(carLat)
				.append(", items : ")
				.append(items);
		
//		if (items == null) {
//			sb.append(", items : null");
//		} else {
//			int i = 0;
//			for (long l : items) {
//				sb.append(", items[").append(i++).append("] : [ order : ")
//						.append(getItemOrderID(i)).append(", time : ").append(getItemOrderTime(i)).append(']');
//			}
//		}
		
		sb.append(" ]");
		return sb.toString();
//		return getClass().getSimpleName() + '@' + Integer.toHexString(hashCode()) + 
//				"[ carLng : " + Integer.toHexString(carLng) + ", carLat : " + Integer.toHexString(carLat) + 
//				", items : " + items + 
//				" ]";
	}
	
//	public void setItemOrderID(int i, int orderID) {
//		if(items == null || items.length <= i)
//			return;
//		long t = ((long) orderID) << 48;
//		long l = items[i] & 0x00000000FFFFFFFFl;
//		l |= t;
//		items[i] = l;
//	}
//	
//	public int getItemOrderID(int i) {
//		if(items == null || items.length <= i)
//			return -1;
//		long l = items[i];
//		return (int) (l >>> 32 & 0x00000000FFFFFFFFl); //(l & 0xFFFFFFFF00000000l) >>> 32;
//	}
//
//	public void setItemOrderv(int i, int orderTime) {
//		if(items == null || items.length <= i)
//			return;
//		long t = ((long) orderTime) & 0x00000000FFFFFFFFl;
//		long l = items[i] & 0xFFFFFFFF00000000l;
//		l |= t;
//		items[i] = l;
//	}
//	
//	public int getItemOrderTime(int i) {
//		if(items == null || items.length <= i)
//			return -1;
//		long l = items[i];
//		return (int) (l & 0x00000000FFFFFFFFl);
//	}
	
}
