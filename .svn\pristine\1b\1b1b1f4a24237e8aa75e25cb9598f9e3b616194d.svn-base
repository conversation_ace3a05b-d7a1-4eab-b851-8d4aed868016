package com.yaxon.base.parameter;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.util.SparseArray;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemSelectedListener;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.Spinner;
import android.widget.TextView;

import com.yaxon.adapter.CommonAdapter;
import com.yaxon.base.R;
import com.yaxon.base.YXActivity;
import com.yaxon.common.ProgramLoader;
import com.yaxon.common.YXDefine;
import com.yaxon.datasource.api.ControlApi;
import com.yaxon.telematics.service.aidl.main.IPCChannelInfo;
import com.yaxon.utils.PlatformUtil;
import com.yaxon.utils.YXAsyncTask;
import com.yaxon.view.ViewHolder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class IpcParamSettingActivity extends YXActivity implements OnItemSelectedListener {

    private static final String TAG = IpcParamSettingActivity.class.getSimpleName();

    //private static final int DUMMY_CHANNEL_BIND_START = 0xFE;
    //private static final int DUMMY_CHANNEL_UNBIND_START = 0xFC;

    private static final List<IPCChannelInfo> NULL_LIST = new ArrayList<IPCChannelInfo>();

    private ListView lvIPC;
    private Button btIpcAuthSetting;
    private Button btIpcNetInfoEditor;

    private TextView btTitle;
    private ImageButton btExit;
    private Button btTitleReflash;

    private CommonAdapter<IPCChannelInfo> adapterIpc;
    private List<IPCChannelInfo> mList = NULL_LIST;

    private Dialog conflictDlg;
    private TextView tvWarning;
    private Button btConfirm;
    private Button btCancel;

    private TextView tvDlgTitle;
    private EditText etIpcIp;
    private EditText etIpcUser;
    private EditText etIpcPsw;
    //private Spinner spIpcChannel;

    private Dialog ipcEditDlg;
    private Button btPositive;
    private Button btNegative;
    private IPCChannelInfo editChannelInfo;

    private Dialog ipcAuthDlg;
    private Button btPositiveAuth;
    private Button btNegativeAuth;
    private IPCChannelInfo authInfo;

    private ListView lvIpcAuth;

    private CommonAdapter<IPCChannelInfo> adapterAuth;
    private List<IPCChannelInfo> ipcAuthData = NULL_LIST;

    private Button btIpcAuthTest;

    private final IPCChannelInfo ipcBindStart = new IPCChannelInfo();
    private final IPCChannelInfo ipcUnBindStart = new IPCChannelInfo();

    private Button btIpcReflash;
    private Button btIpcPreview;
    private boolean isProductTest = false;//是否正在生产检测
    private boolean isManual = false;//是否手动检测


    @SuppressLint("MissingSuperCall")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState, R.layout.ipc_param_set);
        isProductTest = getIntent().getBooleanExtra(YXDefine.KEY_IS_PRODUCT_TEST, false);
        isManual = getIntent().getBooleanExtra(YXDefine.KEY_IS_MANUAL_TEST,false);
        lvIPC = (ListView) findViewById(R.id.lvIPC);
        btIpcAuthSetting = (Button) findViewById(R.id.btIpcAuthSetting);
        btIpcNetInfoEditor = (Button) findViewById(R.id.btIpcNetInfoEditor);
        btIpcReflash = (Button) findViewById(R.id.btIpcReflash);
        btIpcPreview = (Button) findViewById(R.id.btIpcPreview);
        btIpcAuthSetting.setOnClickListener(this);
        btIpcNetInfoEditor.setOnClickListener(this);
        btIpcReflash.setOnClickListener(this);
        btIpcPreview.setOnClickListener(this);

        mList = NULL_LIST;
        adapterIpc = new CommonAdapter<IPCChannelInfo>(this, R.layout.ipc_param_item, mList) {

            @Override
            public void convert(ViewHolder holder, IPCChannelInfo t) {
                if (t == ipcBindStart) {
                    holder.setVisibility(R.id.llIpcItemContent, View.GONE);
                    holder.setVisibility(R.id.tvIpcItemDiver, View.VISIBLE);
                    holder.setText(R.id.tvIpcItemDiver, "通道配置信息");
                    //holder.setTextColor(R.id.tvIpcItemDiver, 0xff6ea500);
                } else if (t == ipcUnBindStart) {
                    holder.setVisibility(R.id.llIpcItemContent, View.GONE);
                    holder.setVisibility(R.id.tvIpcItemDiver, View.VISIBLE);
                    holder.setText(R.id.tvIpcItemDiver, "空闲配置信息");
                    //holder.setTextColor(R.id.tvIpcItemDiver, 0xffc4130c);
                } else {
                    holder.setVisibility(R.id.llIpcItemContent, View.VISIBLE);
                    holder.setVisibility(R.id.tvIpcItemDiver, View.GONE);

                    holder.setText(R.id.tvIpcIP, t.mIpDst);

                    ImageView btIpcEditSelected = holder.getView(R.id.btIpcEditSelected);
                    if (t == editChannelInfo) {
                        btIpcEditSelected.setImageResource(R.drawable.ipc_check_on_normal);
                        btIpcEditSelected.setTag(null);
                        btIpcEditSelected.setOnClickListener(IpcParamSettingActivity.this);
                    } else {
                        btIpcEditSelected.setImageResource(R.drawable.ipc_check_off_normal);
                        btIpcEditSelected.setTag(t);
                        btIpcEditSelected.setOnClickListener(IpcParamSettingActivity.this);
                    }

                    if (t.mState == IPCChannelInfo.STATE_ONLINE) {
                        holder.setImageResource(R.id.ivIpcState, R.drawable.ipc_state_online);
                        holder.setText(R.id.tvIpcWorkState, getWorkState(t.mWorkState));
                    } else {
                        holder.setImageResource(R.id.ivIpcState, R.drawable.ipc_state_offline);
                        holder.setText(R.id.tvIpcWorkState, "-");
                    }

                    Spinner sp = holder.getView(R.id.spIpcChannel);
                    if (t.mIpDst.length() > 0) {
                        sp.setEnabled(true);
                    } else {
                        sp.setEnabled(false);
                    }
                    if (t.mState == IPCChannelInfo.STATE_ONLINE && t.mWorkState != IPCChannelInfo.WORK_STATE_NORMAL) {
                        holder.setTextColor(R.id.tvIpcWorkState, 0xFFFF0000);
                    } else {
                        holder.setTextColor(R.id.tvIpcWorkState, 0xFFFFFFFF);
                    }

                    int selection = channel2selection(t.mChannelDst);
                    sp.setSelection(selection);
                    //sp.setTag(holder);
                    sp.setTag(t);
                    sp.setOnItemSelectedListener(IpcParamSettingActivity.this);

                }
            }

        };
        lvIPC.setAdapter(adapterIpc);

        btTitle = (TextView) findViewById(R.id.title_text_name);
        btTitle.setText("IPC参数设置");
        btExit = (ImageButton) findViewById(R.id.title_btn_exit);
        btExit.setOnClickListener(this);
        btTitleReflash = (Button) findViewById(R.id.title_btn_reflash);
        btTitleReflash.setOnClickListener(this);

        adapterAuth = new CommonAdapter<IPCChannelInfo>(this, R.layout.ipc_auth_item, ipcAuthData) {

            @Override
            public void convert(ViewHolder holder, IPCChannelInfo t) {
                holder.setText(R.id.tvIpcIP, t.mIpDst);
                if (t.mResult == 0x01) {
                    holder.setText(R.id.tvIpcWorkState, "认证通过");
                    holder.setTextColor(R.id.tvIpcWorkState, 0xFFFFFFFF);

                } else {
                    holder.setText(R.id.tvIpcWorkState, "认证失败");
                    holder.setTextColor(R.id.tvIpcWorkState, 0xFFFF0000);
                }

            }
        };
    }

    private String getWorkState(int workState) {
        String ret = null;
        switch (workState) {
            case IPCChannelInfo.WORK_STATE_NORMAL:
                ret = "正常";
                break;

            case IPCChannelInfo.WORK_STATE_AUTH_FAILED:
                ret = "认证失败";
                break;

            case IPCChannelInfo.WORK_STATE_UNKNOW_ERR:
                ret = "其他异常";
                break;

            default:
                ret = "未知错误(-" + workState + ")";
                break;
        }
        return ret;
    }

    BroadcastReceiver receiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            debug("onReceive, action : " + action);
            if (action == null) {
                err("err, action null");
                return;
            }
            List<IPCChannelInfo> list = intent.getParcelableArrayListExtra("KEY_IPC_CHANNEL_LIST");
            if (list == null) {
                err("err, list null");
                return;
            }
            if (ipcSettingTask != null && ipcSettingTask.getStatus() == AsyncTask.Status.RUNNING) {
                ipcSettingTask.setList(list);
            } else {
                updataListView(list);
            }
        }

    };

    @Override
    protected void onStart() {
        // TODO Auto-generated method stub
        super.onStart();
        IntentFilter filter = new IntentFilter();
        filter.addAction("com.yaxon.telematics.ACTION_IPC_CHANNEL_LIST");
        registerReceiver(receiver, filter);
    }

    @Override
    protected void onStop() {
        // TODO Auto-generated method stub
        super.onStop();
        unregisterReceiver(receiver);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mList == NULL_LIST || mList.size() == 0) {
            startIpcSettingTask(lvIPC, "正在搜索IPC设备...", TYPE_IPC_GET_CHANNEL_INFO);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        stopIpcSettingTask();
    }

    private int channel2selection(int channel) {
        if (channel == IPCChannelInfo.CHANNEL_INVALID
                || channel < IPCChannelInfo.CHANNEL_MIN
                || channel > IPCChannelInfo.CHANNEL_MAX) {
            return 0;
        }
        return channel - (IPCChannelInfo.CHANNEL_MIN - 1);
    }

    private int selection2channel(int selection) {
        if (selection == 0 || selection > (IPCChannelInfo.CHANNEL_MAX - IPCChannelInfo.CHANNEL_MIN + 1)) {
            return 0xff;
        }
        return selection + (IPCChannelInfo.CHANNEL_MIN - 1);
    }

    @Override
    public void onClick(View v) {
        Object obj = null;
        switch (v.getId()) {
            case R.id.btCancel:
                hideConflictDlg();
                adapterIpc.notifyDataSetChanged();
                break;

//        case R.id.btIpcEdit:
//            obj = v.getTag();
//            if(obj instanceof IPCChannelInfo){
//                showIpcEditDlg((IPCChannelInfo) obj);
//            }
//            break;

            case R.id.btIpcEditSelected:
                obj = v.getTag();
                debug("onClick, btIpcEditSelected, obj : " + obj);
                if (obj instanceof IPCChannelInfo) {
                    IPCChannelInfo info = (IPCChannelInfo) obj;
                    editChannelInfo = info;
                } else {
                    editChannelInfo = null;
                }
                adapterIpc.notifyDataSetChanged();
                break;

            case R.id.btPositive:
                if (editChannelInfo == null || etIpcIp == null
                    /* || etIpcUser == null || etIpcPsw == null*/) {
                    err("err, onClick btPositive, null");
                    return;
                }
                String ip = etIpcIp.getText().toString().trim();
                //String user = etIpcUser.getText().toString().trim();
                //String psw = etIpcPsw.getText().toString().trim();
                if (!isIPAddress(ip)) {
                    showToast("输入的IP地址不符合格式，请检查");
                    return;
                }
                IPCChannelInfo check = mapIp.get(ip);
                if (check != null && check != editChannelInfo) {
                    showToast("输入的IP地址：" + ip + "已存在，请检查");
                    return;
                }

                //if(ip != null && !ip.equals(editChannelInfo.mIpSrc)){
                //    editChannelInfo.mModifiedMask |= IPCChannelInfo.MODIFIED_IP;
                //}
                //if((user != null && !user.equals(editChannelInfo.mUserSrc)) ||
                //        (psw != null && !psw.equals(editChannelInfo.mPasswordSrc))){
                //    editChannelInfo.mModifiedMask |= IPCChannelInfo.MODIFIED_USER;
                //}
                //if(editChannelInfo.mModifiedMask == 0){
                //    showToast("IPC参数信息未修改");
                //    return;
                //}

                if (ip != null && ip.equals(editChannelInfo.mIpSrc)) {
                    showToast("IPC参数信息未修改");
                    return;
                }

                editChannelInfo.mIpDst = ip;
                //editChannelInfo.mUserDst = user;
                //editChannelInfo.mPasswordDst = psw;
                startIpcSettingTask(lvIPC, "修改IPC参数信息...", TYPE_IPC_SET_CHANNEL_INFO, editChannelInfo);
                hideIpcEditDlg();
                break;

            case R.id.btNegative:
                hideIpcEditDlg();
                break;

            case R.id.btIpcAuthSetting:
                // 登录认证
                if (authInfo == null) {
                    showToast("当前暂无登录认证信息");
                    return;
                }
                showIpcAuthDlg(authInfo);
                break;

            case R.id.btIpcAuthTest:
                if (authInfo == null || etIpcUser == null || etIpcPsw == null) {
                    err("err, onClick btPositive, null");
                    return;
                }
                //String ip = etIpcIp.getText().toString().trim();
                String user = etIpcUser.getText().toString().trim();
                String psw = etIpcPsw.getText().toString().trim();

                if (user == null || psw == null) {
                    showToast("IPC登录认证信息错误，请重新填写");
                    return;
                }

                authInfo.mUserDst = user;
                authInfo.mPasswordDst = psw;
                startIpcSettingTask(lvIPC, "测试在线IPC认证信息...", TYPE_IPC_AUTH_TEST, authInfo);
                adapterAuth.setData(NULL_LIST);
                break;

            case R.id.btPositiveAuth:
                if (authInfo == null || etIpcUser == null || etIpcPsw == null) {
                    err("err, onClick btPositive, null");
                    return;
                }
                //String ip = etIpcIp.getText().toString().trim();
                user = etIpcUser.getText().toString().trim();
                psw = etIpcPsw.getText().toString().trim();

                if (user == null || psw == null) {
                    showToast("IPC登录认证信息错误，请重新填写");
                    return;
                }
                if (user.equals(authInfo.mUserSrc) &&
                        psw.equals(authInfo.mPasswordSrc)) {
                    //showToast("IPC登录认证信息未修改");
                    hideIpcAuthDlg();
                    return;
                }

                authInfo.mUserDst = user;
                authInfo.mPasswordDst = psw;
                startIpcSettingTask(lvIPC, "保存IPC登录认证信息...", TYPE_IPC_AUTH_SET, authInfo);
                break;

            case R.id.btNegativeAuth:
                hideIpcAuthDlg();
                break;

            case R.id.btIpcNetInfoEditor:
                // 网络参数
                warning("onClick, editChannelInfo : " + editChannelInfo);
                if (editChannelInfo == null) {
                    showToast("请选择要编辑的IPC设备");
                    return;
                }
                if (editChannelInfo.mState != IPCChannelInfo.STATE_ONLINE) {
                    showToast("请确保设备在线");
                    return;
                }
                if (editChannelInfo.mWorkState != IPCChannelInfo.WORK_STATE_NORMAL) {
                    showToast("请确保设备状态“正常”");
                    return;
                }
                showIpcEditDlg(editChannelInfo);
                break;

            case R.id.title_btn_exit:
                if (isProductTest) {
                    if (isManual) {
                        ProgramLoader.meigeProductTest(this);
                    } else {
                        ProgramLoader.productTest();
                    }
                }
                this.finish();
                break;

            case R.id.title_btn_reflash:
            case R.id.btIpcPreview:
                ProgramLoader.openVideoPreview();
                break;
            case R.id.btIpcReflash:
                startIpcSettingTask(lvIPC, "重新搜索IPC设备...", TYPE_IPC_GET_CHANNEL_INFO);
                adapterIpc.setData(NULL_LIST);
                break;

            default:
                break;
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (isProductTest) {
                if (PlatformUtil.isMeiGPlatform()) {
                    ProgramLoader.meigeProductTest(this);
                } else {
                    ProgramLoader.productTest();
                }
            }
            this.finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    /* Java 验证Ip是否合法 */
    public static boolean isIPAddress(String ipaddr) {
        boolean flag = false;
        Pattern pattern = Pattern
                .compile("\\b((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\b");
        Matcher m = pattern.matcher(ipaddr);
        flag = m.matches();
        return flag;
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        debug(">>>>>>>> onItemSelected : " + position);
        Object obj = parent.getTag();
        if (obj == null || !(obj instanceof IPCChannelInfo)) {
            err("err, obj : " + obj);
            return;
        }

//        ViewHolder holder = (ViewHolder) obj;
//        int index = holder.getPosition();
//        debug("  holder index : " + index);
//        if(index >= mList.size()){
//            err("err, index : " + index);
//            return;
//        }
//        IPCChannelInfo selectInfo = mList.get(index);

        IPCChannelInfo selectInfo = (IPCChannelInfo) obj;
        int channelDst = selection2channel(position);
        debug("  selectInfo : ip " + selectInfo.mIpSrc + ", channel " + selectInfo.mChannelSrc);
        debug("  channelDst : " + channelDst);
        int channelSrc = selectInfo.mChannelSrc;
        if (selectInfo.mChannelDst == channelDst || (channelSrc < IPCChannelInfo.CHANNEL_MIN ||
                (channelSrc > IPCChannelInfo.CHANNEL_MAX && channelSrc != IPCChannelInfo.CHANNEL_INVALID))) {
            //warning("channelDst is same with selectInfo.mChannelDst, or channelSrc invalid...");
            return;
        }

        if (channelDst == IPCChannelInfo.CHANNEL_INVALID) {
            debug("  channelDst == IPCChannelInfo.CHANNEL_INVALID");
            startIpcSettingTask(lvIPC, "释放IPC通道[" + selectInfo.mChannelDst + "]...", TYPE_IPC_BIND_CHANNEL, channelDst, selectInfo);
            //mapChannel.put(selectInfo.mChannelDst, null);
            //selectInfo.mChannelDst = IPCChannelInfo.CHANNEL_INVALID;
            //adapterIpc.notifyDataSetChanged();
        } else {
            IPCChannelInfo old = mapChannel.get(channelDst);
            if (old != null && old.mChannelDst == channelDst) {
                debug("  conflict old : ip " + old.mIpSrc + ", channel " + old.mChannelSrc);
                showConflictDlg(selectInfo, old);
            } else {
                startIpcSettingTask(lvIPC, "绑定IPC通道[" + channelDst + "]...", TYPE_IPC_BIND_CHANNEL, channelDst, selectInfo);
                //selectInfo.mChannelDst = channelDst;
                //mapChannel.put(channelDst, selectInfo);
                //adapterIpc.notifyDataSetChanged();
            }
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }

    @SuppressLint("InflateParams")
    private void showConflictDlg(final IPCChannelInfo newInfo, final IPCChannelInfo oldInfo) {
        debug("  showConflictDlg, newInfo : " + newInfo);
        debug("  showConflictDlg, oldInfo : " + oldInfo);
        if (conflictDlg == null) {
            conflictDlg = new Dialog(this, R.style.Theme_dialog);
            View content = LayoutInflater.from(this).inflate(R.layout.ipc_conflict_dlg, null);
            tvDlgTitle = (TextView) content.findViewById(R.id.tvDialogTitle);
            tvWarning = (TextView) content.findViewById(R.id.tvWarning);
            btConfirm = (Button) content.findViewById(R.id.btConfirm);
            btCancel = (Button) content.findViewById(R.id.btCancel);
            conflictDlg.setContentView(content);
        }
        tvDlgTitle.setText("IPC配置冲突");
        //tvWarning.setText("通道：" + oldInfo.mChannelDst + "\n已绑定\n\t设备：" + oldInfo.mIpDst + "\n您确定要使用\n\t设备：" + newInfo.mIpDst + "\n覆盖原通道配置？");
        tvWarning.setText("通道" + oldInfo.mChannelDst + "已被绑定，是否覆盖？");
        btConfirm.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                hideConflictDlg();
                startIpcSettingTask(lvIPC, "重新绑定IPC通道[" + oldInfo.mChannelDst + "]...", TYPE_IPC_BIND_CHANNEL, oldInfo.mChannelDst, newInfo, oldInfo);

                //newInfo.mChannelDst = oldInfo.mChannelDst;
                //oldInfo.mChannelDst = IPCChannelInfo.CHANNEL_INVALID;
                //mapChannel.put(newInfo.mChannelDst, newInfo);
                //hideConflictDlg();
                //if(adapterIpc != null) {
                //    adapterIpc.notifyDataSetChanged();
                //}

            }
        });
        btCancel.setOnClickListener(this);
        conflictDlg.show();
    }

    private void hideConflictDlg() {
        if (conflictDlg == null || !conflictDlg.isShowing())
            return;

        conflictDlg.dismiss();
    }

    @SuppressLint("InflateParams")
    private void showIpcEditDlg(IPCChannelInfo info) {
        if (ipcEditDlg != null) {
            ipcEditDlg = null;
        }
        if (ipcEditDlg == null) {
            ipcEditDlg = new Dialog(this, R.style.Theme_dialog);
            View content = LayoutInflater.from(this).inflate(R.layout.ipc_edit_dlg, null);
            TextView tvDialogTitle = (TextView) content.findViewById(R.id.tvDialogTitle);
            tvDialogTitle.setText("编辑网络参数信息");
            etIpcIp = (EditText) content.findViewById(R.id.etIpcIp);
            //etIpcUser = (EditText) content.findViewById(R.id.etIpcUser);
            //etIpcPsw = (EditText) content.findViewById(R.id.etIpcPsw);
            //spIpcChannel = (Spinner) content.findViewById(R.id.spIpcChannel);
            //spIpcChannel.setOnItemSelectedListener(this);
            btPositive = (Button) content.findViewById(R.id.btPositive);
            btPositive.setOnClickListener(this);
            btNegative = (Button) content.findViewById(R.id.btNegative);
            btNegative.setOnClickListener(this);
            ipcEditDlg.setContentView(content);
        }
        editChannelInfo = info;
        etIpcIp.setText(info.mIpDst);
        //etIpcUser.setText(info.mUserDst);
        //etIpcPsw.setText(info.mPasswordDst);
        //spIpcChannel.setTag(info);
        //spIpcChannel.setSelection(channel2selection(info.mChannelDst));
        ipcEditDlg.show();
    }

    private void hideIpcEditDlg() {
        if (ipcEditDlg == null || !ipcEditDlg.isShowing()) {
            return;
        }
        ipcEditDlg.dismiss();
        ipcEditDlg = null;
        editChannelInfo = null;
        adapterIpc.notifyDataSetChanged();
    }

    @SuppressLint("InflateParams")
    private void showIpcAuthDlg(IPCChannelInfo auth) {
        if (ipcAuthDlg != null) {
            ipcAuthDlg = null;
        }
        if (ipcAuthDlg == null) {
            ipcAuthDlg = new Dialog(this, R.style.Theme_dialog);
            View content = LayoutInflater.from(this).inflate(R.layout.ipc_auth_dlg, null);
            TextView tvDialogTitle = (TextView) content.findViewById(R.id.tvDialogTitle);
            tvDialogTitle.setText("IPC登录认证设置");
            etIpcUser = (EditText) content.findViewById(R.id.etIpcUser);
            etIpcUser.clearFocus();
            etIpcPsw = (EditText) content.findViewById(R.id.etIpcPsw);
            lvIpcAuth = (ListView) content.findViewById(R.id.lvIpcAuth);
            lvIpcAuth.setAdapter(adapterAuth);
            btIpcAuthTest = (Button) content.findViewById(R.id.btIpcAuthTest);
            btIpcAuthTest.setOnClickListener(this);
            btPositiveAuth = (Button) content.findViewById(R.id.btPositiveAuth);
            btPositiveAuth.setOnClickListener(this);
            btNegativeAuth = (Button) content.findViewById(R.id.btNegativeAuth);
            btNegativeAuth.setOnClickListener(this);
            ipcAuthDlg.setContentView(content);
        }
        authInfo = auth;
        if (authInfo == null) {
            err("err, showIpcAuthDlg, authInfo null");
            showToast("暂无IPC登录认证信息");
            return;
        }
        etIpcUser.setText(authInfo.mUserSrc);
        etIpcPsw.setText(authInfo.mPasswordSrc);
        adapterAuth.setData(NULL_LIST);
        ipcAuthDlg.show();
    }

    private void hideIpcAuthDlg() {
        if (ipcAuthDlg == null || !ipcAuthDlg.isShowing()) {
            return;
        }
        ipcAuthDlg.dismiss();
        ipcAuthDlg = null;
    }

    public void updataListView(List<IPCChannelInfo> list) {
        if (list != null && lvIPC != null && adapterIpc != null) {
            updataChannelSet(list);

            Collections.sort(list, new Comparator<IPCChannelInfo>() {

                public int compare(IPCChannelInfo lhs, IPCChannelInfo rhs) {
                    int lChannel = lhs.mChannelSrc;
                    int rChannle = rhs.mChannelSrc;
                    int compareChannle = lChannel - rChannle;
                    int compareState = lhs.mState - rhs.mState;
                    int compareWorkState = lhs.mWorkState - rhs.mWorkState;
                    if (lChannel == IPCChannelInfo.CHANNEL_INVALID ||
                            rChannle == IPCChannelInfo.CHANNEL_INVALID) {
                        if (compareChannle == 0) {
                            if (compareState == 0) {
                                if (compareWorkState == 0) {
                                    return 0;
                                } else {
                                    return lhs.mIpSrc.compareTo(rhs.mIpSrc);
                                }
                            } else {
                                return -compareState;
                            }
                        } else {
                            return compareChannle;
                        }
                    } else {
                        return lChannel - rChannle;
                    }
                }
            });
            //int bindcnt = 0;
            //int listsize = list.size();
            //for (int i = 0; i < listsize; i++) {
            //    IPCChannelInfo info = list.get(i);
            //    if(info.mChannelSrc <= IPCChannelInfo.CHANNEL_MAX){
            //        bindcnt++;
            //    }
            //}
            //if(bindcnt > 0 && bindcnt < listsize){
            //ipcBindStart.mChannelSrc = ipcBindStart.mChannelDst = DUMMY_CHANNEL_BIND_START;
            //ipcUnBindStart.mChannelSrc = ipcUnBindStart.mChannelDst = DUMMY_CHANNEL_UNBIND_START;
            //list.add(bindcnt, ipcUnBindStart);
            //list.add(0, ipcBindStart);
            //}
            mList = list;
            adapterIpc.setData(list);
        }
    }

    SparseArray<IPCChannelInfo> mapChannel = new SparseArray<IPCChannelInfo>();
    Map<String, IPCChannelInfo> mapIp = new HashMap<String, IPCChannelInfo>();

    private void updataChannelSet(List<IPCChannelInfo> list) {
        if (list != null) {
            mapChannel.clear();
            mapIp.clear();
            for (IPCChannelInfo ipc : list) {
                if (ipc == null || ipc.mIpDst == null || ipc.mIpDst.trim().length() == 0) continue;

                mapChannel.put(ipc.mChannelDst, ipc);
                mapIp.put(ipc.mIpDst, ipc);
            }
        }
    }

    public void updataAuthListView(List<IPCChannelInfo> listAuth) {
        if (listAuth != null && adapterAuth != null) {
            ipcAuthData = listAuth;
            adapterAuth.setData(ipcAuthData);
        }
    }

    private IpcSettingTask ipcSettingTask;

    private void startIpcSettingTask(View view, String content, int type, Object... params) {
        if (ipcSettingTask != null && ipcSettingTask.getStatus() == AsyncTask.Status.RUNNING) {
            warning("startIpcSettingTask, task no finish, cancel it...");
            ipcSettingTask.cancel(true);
        }
        debug("startIpcSettingTask, new task...");
        ipcSettingTask = new IpcSettingTask(this, view, true, content);
        ipcSettingTask.execute(type, params);
    }

    private void stopIpcSettingTask() {
        if (ipcSettingTask != null && ipcSettingTask.getStatus() == AsyncTask.Status.RUNNING) {
            ipcSettingTask.cancel(true);
        }
    }

    private static final int TYPE_IPC_GET_CHANNEL_INFO = 0;
    private static final int TYPE_IPC_SET_CHANNEL_INFO = 1;
    private static final int TYPE_IPC_BIND_CHANNEL = 2;
    private static final int TYPE_IPC_AUTH_TEST = 3;
    private static final int TYPE_IPC_AUTH_SET = 4;
//    // For test
//    private static final String[] testIp = new String[] {
//        "*************", "************", "************", "*************", "************", 
//        "*************", "************", "************"
//    };
//    
//    private static final int[] testChannel = new int[] {
//        9, 12, 10, 0xff, 0xff,
//        0xff, 0xff, 11,
//    };
//
//    private static final int[] testState = new int[] {
//        1, 1, 0, 0, 1,
//        0, 0, 1,
//    };
//    
//    private static final int[] testWorkState = new int[] {
//        0, 1, 0, 0xff, 0xff, 
//        0, 1, 0, 
//    };
//
//    private static final int[] testResult = new int[] {
//        0, 1, 0, 0xff, 0xff, 
//        0, 1, 0, 
//    };

    class IpcSettingTask extends YXAsyncTask<Object, Void, Integer> {

        private int type;
        private volatile List<IPCChannelInfo> list;

        private IPCChannelInfo auth;
        private List<IPCChannelInfo> listAuth;

        int ret = -255;
        private String tip = "";
        private Integer channelDst;
        private IPCChannelInfo selectInfo;
        private IPCChannelInfo newInfo;
        private IPCChannelInfo oldInfo;
        private IPCChannelInfo editInfo;

        int reflashRet = -255;
        private String reflashTip = "";

        public IpcSettingTask(Context context, View view, boolean isShowPrompt, String content) {
            super(context, view, isShowPrompt, content);
        }

        public synchronized void setList(List<IPCChannelInfo> l) {
            list = l;
        }

        private boolean waitTimeout(long timeout) throws InterruptedException {
            timeout += System.currentTimeMillis();
            while (System.currentTimeMillis() < timeout) {
                if (list != null) {
                    break;
                } else {
                    Thread.sleep(100);
                }
            }
            return (list != null);
        }

        @Override
        protected void onProgressUpdate(Void... values) {
            // TODO Auto-generated method stub
            super.onProgressUpdate(values);
            updataListView(NULL_LIST);
        }

        @Override
        protected Integer doInBackground(Object... params) {
            if (params == null || params.length < 1 || !(params[0] instanceof Integer)) {
                return -2;
            }
            type = (Integer) params[0];
            Object[] objs;
            switch (type) {
                case TYPE_IPC_GET_CHANNEL_INFO:
                    publishProgress();
                    //list = ControlApi.getInstance().ipcGetChannelInfo();
                    ControlApi.getInstance().ipcGetChannelInfo();
                    try {
                        waitTimeout(10 * 1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                        if (isCancelled())
                            return -1;
                    }
                    if (list == null) {
                        tip = "搜索IPC设备失败";
                        return -1;
                    }
                    if (authInfo == null ||
                            authInfo.mUserSrc == null ||
                            authInfo.mUserDst == null ||
                            authInfo.mPasswordSrc == null ||
                            authInfo.mPasswordDst == null) {
                        auth = ControlApi.getInstance().ipcGetAuthInfo();
                        if (auth == null) {
                            tip = "查询IPC登录认证信息失败";
                            return -1;
                        }
                    } else {
                        auth = authInfo;
                    }

                    //TODO remove for test
//                try {
//                    Thread.sleep(3000);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//                list = new ArrayList<IPCChannelInfo>();
//                for (int i = 0; i < testIp.length; i++) {
//                    IPCChannelInfo info = new IPCChannelInfo();
//                    info.mState = testState[i];
//                    info.mWorkState = testWorkState[i];
//                    info.mIpSrc = info.mIpDst = testIp[i];
//                    info.mChannelSrc = info.mChannelDst = testChannel[i];
//                    list.add(info);
//                }
//                auth = new IPCChannelInfo();
//                auth.mUserDst = auth.mUserSrc = "admin";
//                auth.mPasswordDst = auth.mPasswordSrc = "adminpsw";

                    ret = 0;
                    break;

//            case TYPE_IPC_SET_CHANNEL_INFO:
//                debug("IpcSettingTask, TYPE_IPC_SET_CHANNEL_INFO, params.length : " + params.length );
//                for (int i = 0; i < params.length; i++) {
//                    debug("IpcSettingTask, TYPE_IPC_SET_CHANNEL_INFO, params : " + params[i]);
//                }
//                if(params.length < 2 || !(params[1] instanceof Object[])){
//                    tip = "未知错误";
//                    return -6;
//                }
//                objs = (Object[]) params[1];
//                if(!(objs[0] instanceof IPCChannelInfo)){
//                    tip = "未知错误";
//                    return -5;
//                }
//                editInfo = (IPCChannelInfo) objs[0];
//                if(editInfo.mIpDst != null && !editInfo.mIpDst.equals(editInfo.mIpSrc)){
//                    editInfo.mModifiedMask |= IPCChannelInfo.MODIFIED_IP;
//                }
//                if((editInfo.mUserDst != null && !editInfo.mUserDst.equals(editInfo.mUserSrc)) || 
//                        (editInfo.mPasswordDst != null && !editInfo.mPasswordDst.equals(editInfo.mPasswordSrc))){
//                    editInfo.mModifiedMask |= IPCChannelInfo.MODIFIED_USER;
//                }
//                ret = ControlApi.getInstance().ipcSetChannelInfo(editInfo);
//                if(ret == -1) {
//                    tip = "请求发送失败";
//                } else if(ret == -2) {
//                    tip = "参数错误";
//                } else if(ret == 0x02) {
//                    tip = "编辑失败";
//                } else if(ret == 0x01) {
//                    tip = "编辑成功";
//                    ret = 0; 
//                }
//                break;

                case TYPE_IPC_SET_CHANNEL_INFO:
                    publishProgress();
                    if (params.length < 2 || !(params[1] instanceof Object[])) {
                        tip = "未知错误";
                        return -6;
                    }
                    objs = (Object[]) params[1];
                    if (!(objs[0] instanceof IPCChannelInfo)) {
                        tip = "未知错误";
                        return -5;
                    }
                    editInfo = (IPCChannelInfo) objs[0];
                    if (editInfo.mIpDst == null || editInfo.mIpDst.equals(editInfo.mIpSrc)) {
                        tip = "IPC网络参数未修改";
                        return -2;
                    }
                    ret = ControlApi.getInstance().ipcEditChannelNetInfo(editInfo);
                    if (ret == -1) {
                        tip = "请求发送失败";
                    } else if (ret == -2) {
                        tip = "参数错误";
                    } else if ((ret & 0xff) == 0x02) {
                        tip = "修改IPC网络参数失败";
                    } else if ((ret & 0xff) == 0x01) {
                        // 修改成功
                        int reboot = (ret >> 8) * 0xff;
                        if (reboot == 0x00) {
                            tip = "修改成功";
                        } else {
                            tip = "修改成功，设备重启中，请稍后刷新";
                        }
                        // 重新绑定
                        if (editInfo.mChannelDst >= IPCChannelInfo.CHANNEL_MIN &&
                                editInfo.mChannelDst <= IPCChannelInfo.CHANNEL_MAX) {
                            List<IPCChannelInfo> bind = new ArrayList<IPCChannelInfo>();
                            IPCChannelInfo copy = channelInfoCopy(editInfo);
                            copy.mIpSrc = copy.mIpDst;
                            copy.mModifiedMask = IPCChannelInfo.MODIFIED_BIND;
                            bind.add(copy);
                            ret = ControlApi.getInstance().ipcBindChannel(bind);
                            if (ret == -1) {
                                ret = -3;
                                tip = "请求发送失败";
                            } else if (ret == -2) {
                                ret = -4;
                                tip = "参数错误";
                            } else if (ret == 0 && bind != null) {
                                if (bind.size() == 1) {
                                    IPCChannelInfo retInfo = bind.get(0);
                                    if (retInfo == null) {
                                        ret = -5;
                                        tip = "通信异常错误";
                                        break;
                                    }
                                    if (retInfo.mResult == IPCChannelInfo.RESULT_SUCC) {
                                        ret = 0;
                                        //tip = "操作成功";
                                    } else {
                                        ret = -6;
                                        tip = "操作失败";
                                    }
                                } else {
                                    ret = -7;
                                    tip = "未知错误";
                                }
                            } else {
                                ret = -8;
                                tip = "未知错误";
                            }
                        }
                    } else {
                        ret = -9;
                        tip = "未知错误";
                    }
                    // 延迟3秒 防止车台未及时更新原IP地址
                    try {
                        Thread.sleep(3000);
                    } catch (InterruptedException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                    // 重新获取IPC设备列表
                    //list = ControlApi.getInstance().ipcGetChannelInfo();
                    ControlApi.getInstance().ipcGetChannelInfo();
                    try {
                        waitTimeout(10 * 1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                        if (isCancelled())
                            return -1;
                    }
                    if (list == null) {
                        reflashTip = "，重新搜索IPC设备列表失败";
                    } else {
                        reflashRet = 0;
                    }
                    break;

                case TYPE_IPC_AUTH_TEST:
                    if (params.length < 2 || !(params[1] instanceof Object[])) {
                        tip = "未知错误";
                        return -6;
                    }
                    objs = (Object[]) params[1];
                    if (!(objs[0] instanceof IPCChannelInfo)) {
                        tip = "未知错误";
                        return -5;
                    }
                    auth = (IPCChannelInfo) objs[0];

                    if (auth.mUserDst == null || auth.mPasswordDst == null) {
                        tip = "IPC认证参数错误";
                        return -2;
                    }
                    listAuth = new ArrayList<IPCChannelInfo>();
                    ret = ControlApi.getInstance().ipcTestAuthInfo(auth.mUserDst, auth.mPasswordDst, listAuth);
                    if (ret == -1) {
                        tip = "请求发送失败";
                    } else if (ret == -2) {
                        tip = "参数错误";
                    } else if (ret == 0x00) {
                        tip = "测试成功";
                        ret = 0;
                    }
                    break;

                case TYPE_IPC_AUTH_SET:
                    publishProgress();
                    if (params.length < 2 || !(params[1] instanceof Object[])) {
                        tip = "未知错误";
                        return -6;
                    }
                    objs = (Object[]) params[1];
                    if (!(objs[0] instanceof IPCChannelInfo)) {
                        tip = "未知错误";
                        return -5;
                    }
                    auth = (IPCChannelInfo) objs[0];
                    if ((auth.mUserDst != null && !auth.mUserDst.equals(auth.mUserSrc)) ||
                            (auth.mPasswordDst != null && !auth.mPasswordDst.equals(auth.mPasswordSrc))) {
                        auth.mModifiedMask |= IPCChannelInfo.MODIFIED_USER;
                    }
                    ret = ControlApi.getInstance().ipcSetAuthInfo(auth.mUserDst, auth.mPasswordDst);
                    if (ret == -1) {
                        tip = "请求发送失败";
                    } else if (ret == -2) {
                        tip = "参数错误";
                    } else if (ret == 0x02) {
                        tip = "认证信息保存失败";
                    } else if (ret == 0x01) {
                        tip = "认证信息保存成功";
                        ret = 0;
                    }
                    // 重新获取IPC设备列表
                    //list = ControlApi.getInstance().ipcGetChannelInfo();
                    ControlApi.getInstance().ipcGetChannelInfo();
                    try {
                        waitTimeout(10 * 1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                        if (isCancelled())
                            return -1;
                    }
                    if (list == null) {
                        reflashTip = "，重新搜索IPC设备列表失败";
                    } else {
                        reflashRet = 0;
                    }
                    break;

                case TYPE_IPC_BIND_CHANNEL:
                    publishProgress();
                    debug("IpcSettingTask, TYPE_IPC_BIND_CHANNEL, params.length : " + params.length);
                    for (int i = 0; i < params.length; i++) {
                        debug("IpcSettingTask, TYPE_IPC_BIND_CHANNEL, params : " + params[i]);
                    }
                    if (params.length < 2 || !(params[1] instanceof Object[])) {
                        tip = "未知错误";
                        return -6;
                    }
                    objs = (Object[]) params[1];
                    if (objs.length == 2) {
                        if (!(objs[0] instanceof Integer) || !(objs[1] instanceof IPCChannelInfo)) {
                            tip = "未知错误";
                            return -5;
                        }
                        channelDst = (Integer) objs[0];
                        selectInfo = (IPCChannelInfo) objs[1];
                        //selectInfo.mChannelDst = channelDst;
                        //selectInfo.mModifiedMask =
                        //        channelDst == IPCChannelInfo.CHANNEL_INVALID ? IPCChannelInfo.MODIFIED_UNBIND : IPCChannelInfo.MODIFIED_BIND;
                        List<IPCChannelInfo> bind = new ArrayList<IPCChannelInfo>();
                        if (selectInfo.mChannelSrc == channelDst) {
                            err("err, channelDst equals mChannelSrc : " + channelDst);
                        }
                        //先解绑
                        if (selectInfo.mChannelSrc != IPCChannelInfo.CHANNEL_INVALID) {
                            IPCChannelInfo orign = channelInfoCopy(selectInfo);
                            orign.mModifiedMask = IPCChannelInfo.MODIFIED_UNBIND;
                            bind.add(orign);
                        }

                        if (channelDst != IPCChannelInfo.CHANNEL_INVALID) {
                            selectInfo.mChannelDst = channelDst;
                            selectInfo.mModifiedMask = IPCChannelInfo.MODIFIED_BIND;
                            bind.add(selectInfo);
                        }
                        ret = ControlApi.getInstance().ipcBindChannel(bind);
                        if (ret == -1) {
                            tip = "请求发送失败";
                            selectInfo.mChannelDst = selectInfo.mChannelSrc;
                        } else if (ret == -2) {
                            tip = "参数错误";
                            selectInfo.mChannelDst = selectInfo.mChannelSrc;
                        } else if (ret == 0 && bind != null) {
                            if (bind.size() == 1) {
                                IPCChannelInfo retInfo = bind.get(0);
                                if (retInfo == null) {
                                    ret = -4;
                                    tip = "通信异常错误";
                                    selectInfo.mChannelDst = selectInfo.mChannelSrc;
                                    break;
                                }
                                tip = (channelDst == IPCChannelInfo.CHANNEL_INVALID) ?
                                        ("设备[" + selectInfo.mIpDst + "]解绑通道[" + selectInfo.mChannelDst + "]") :
                                        ("设备[" + selectInfo.mIpDst + "]绑定通道[" + channelDst + "]");
                                if (retInfo.mResult == IPCChannelInfo.RESULT_SUCC) {
                                    selectInfo.mChannelSrc = selectInfo.mChannelDst = channelDst;
                                    tip += "成功";
                                } else {
                                    ret = -3;
                                    tip += "失败";
                                    selectInfo.mChannelDst = selectInfo.mChannelSrc;
                                }
                            } else if (bind.size() == 2) {
                                IPCChannelInfo retInfo0 = bind.get(0);
                                IPCChannelInfo retInfo1 = bind.get(1);
                                if (retInfo0 == null || retInfo1 == null) {
                                    ret = -4;
                                    tip = "通信异常错误";
                                    selectInfo.mChannelDst = selectInfo.mChannelSrc;
                                    break;
                                }
                                tip = (channelDst == IPCChannelInfo.CHANNEL_INVALID) ?
                                        ("设备[" + selectInfo.mIpDst + "]解绑通道[" + selectInfo.mChannelDst + "]") :
                                        ("设备[" + selectInfo.mIpDst + "]绑定通道[" + channelDst + "]");
                                if (retInfo0.mResult == IPCChannelInfo.RESULT_SUCC && retInfo1.mResult == IPCChannelInfo.RESULT_SUCC) {
                                    selectInfo.mChannelSrc = selectInfo.mChannelDst = channelDst;
                                    tip += "成功";
                                } else {
                                    ret = -3;
                                    tip += "失败";
                                    selectInfo.mChannelDst = selectInfo.mChannelSrc;
                                }
                            } else {
                                ret = -4;
                                tip = "数据返回异常错误";
                                selectInfo.mChannelDst = selectInfo.mChannelSrc;
                            }
                        } else {
                            tip = "未知错误";
                        }
                    } else if (objs.length == 3) {
                        if (!(objs[0] instanceof Integer)
                                || !(objs[1] instanceof IPCChannelInfo)
                                || !(objs[2] instanceof IPCChannelInfo)) {
                            tip = "未知错误";
                            return -5;
                        }
                        channelDst = (Integer) objs[0];
                        newInfo = selectInfo = (IPCChannelInfo) objs[1];
                        List<IPCChannelInfo> bind = new ArrayList<IPCChannelInfo>();
                        if (channelDst == IPCChannelInfo.CHANNEL_INVALID) {
                            err("err, conflict but channelDst is invalied");
                        }
                        if (newInfo.mChannelSrc == channelDst) {
                            err("err, channelDst equals mChannelSrc : " + channelDst);
                        }
                        //先解绑
                        if (newInfo.mChannelSrc != IPCChannelInfo.CHANNEL_INVALID) {
                            IPCChannelInfo orign = channelInfoCopy(newInfo);
                            orign.mModifiedMask = IPCChannelInfo.MODIFIED_UNBIND;
                            bind.add(orign);
                        }
                        newInfo.mChannelDst = channelDst;
                        newInfo.mModifiedMask = IPCChannelInfo.MODIFIED_BIND;
                        oldInfo = (IPCChannelInfo) objs[2];
                        oldInfo.mChannelDst = oldInfo.mChannelSrc;//IPCChannelInfo.CHANNEL_INVALID;
                        oldInfo.mModifiedMask = IPCChannelInfo.MODIFIED_UNBIND;
                        bind.add(oldInfo);
                        bind.add(newInfo);
                        debug("IpcSettingTask, TYPE_IPC_BIND_CHANNEL, before bind : " + bind.get(0) + ", " + bind.get(1));
                        ret = ControlApi.getInstance().ipcBindChannel(bind);
                        debug("IpcSettingTask, TYPE_IPC_BIND_CHANNEL, after  bind : " + bind.get(0) + ", " + bind.get(1));
                        if (ret == -1) {
                            tip = "请求发送失败";
                            newInfo.mChannelDst = newInfo.mChannelSrc;
                            oldInfo.mChannelDst = oldInfo.mChannelSrc;
                        } else if (ret == -2) {
                            tip = "参数错误";
                            newInfo.mChannelDst = newInfo.mChannelSrc;
                            oldInfo.mChannelDst = oldInfo.mChannelSrc;
                        } else if (ret == 0 && bind != null) {
                            if (bind.size() == 2) {
                                IPCChannelInfo retOldInfo = bind.get(0);
                                IPCChannelInfo retNewInfo = bind.get(1);
                                if (retOldInfo == null || retNewInfo == null) {
                                    ret = -4;
                                    tip = "通信异常错误";
                                    newInfo.mChannelDst = newInfo.mChannelSrc;
                                    oldInfo.mChannelDst = oldInfo.mChannelSrc;
                                    break;
                                }
                                tip = "";
                                if (retOldInfo.mResult == IPCChannelInfo.RESULT_SUCC) {
                                    tip = "设备[" + oldInfo.mIpDst + "]解除绑定通道[" + oldInfo.mChannelSrc + "]成功\n";
                                } else {
                                    tip = "设备[" + oldInfo.mIpDst + "]解除绑定通道[" + oldInfo.mChannelSrc + "]失败\n";
                                    ret = -3;
                                    oldInfo.mChannelDst = oldInfo.mChannelSrc;
                                }
                                if (retNewInfo.mResult == IPCChannelInfo.RESULT_SUCC) {
                                    tip += "设备[" + newInfo.mIpDst + "]绑定通道[" + newInfo.mChannelSrc + "]成功";
                                } else {
                                    tip += "设备[" + newInfo.mIpDst + "]绑定通道[" + newInfo.mChannelSrc + "]失败";
                                    ret = -3;
                                    newInfo.mChannelDst = newInfo.mChannelSrc;
                                }
                            } else if (bind.size() == 3) {
                                IPCChannelInfo retOriInfo = bind.get(0);
                                IPCChannelInfo retOldInfo = bind.get(1);
                                IPCChannelInfo retNewInfo = bind.get(2);
                                if (retOriInfo == null || retOldInfo == null || retNewInfo == null) {
                                    ret = -4;
                                    tip = "通信异常错误";
                                    newInfo.mChannelDst = newInfo.mChannelSrc;
                                    oldInfo.mChannelDst = oldInfo.mChannelSrc;
                                    break;
                                }
                                tip = "";
                                if (retOldInfo.mResult == IPCChannelInfo.RESULT_SUCC) {
                                    tip = "设备[" + oldInfo.mIpDst + "]解除绑定通道[" + oldInfo.mChannelSrc + "]成功\n";
                                } else {
                                    tip = "设备[" + oldInfo.mIpDst + "]解除绑定通道[" + oldInfo.mChannelSrc + "]失败\n";
                                    ret = -3;
                                    oldInfo.mChannelDst = oldInfo.mChannelSrc;
                                }
                                if (retNewInfo.mResult == IPCChannelInfo.RESULT_SUCC) {
                                    tip += "设备[" + newInfo.mIpDst + "]绑定通道[" + newInfo.mChannelSrc + "]成功";
                                } else {
                                    tip += "设备[" + newInfo.mIpDst + "]绑定通道[" + newInfo.mChannelSrc + "]失败";
                                    ret = -3;
                                    newInfo.mChannelDst = newInfo.mChannelSrc;
                                }
                            } else {
                                ret = -4;
                                tip = "数据返回异常错误";
                                newInfo.mChannelDst = newInfo.mChannelSrc;
                            }
                        } else {
                            tip = "未知错误";
                        }
                    }

                    // 重新获取IPC列表
                    //list = ControlApi.getInstance().ipcGetChannelInfo();
                    ControlApi.getInstance().ipcGetChannelInfo();
                    try {
                        waitTimeout(10 * 1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                        if (isCancelled())
                            return -1;
                    }
                    if (list == null) {
                        reflashTip = "，重新搜索IPC设备列表失败";
                    } else {
                        reflashRet = 0;
                    }
                    break;

                default:
                    break;
            }
            debug(">>>>>>>>>>> IpcSettingTask ret : " + ret);
            return ret;
        }

        @Override
        protected void onPostExecute(Integer result) {
            super.onPostExecute(result);
            switch (type) {
                case TYPE_IPC_GET_CHANNEL_INFO:
                    if (result < 0) {
                        showToast(tip);
                    }
                    if (list != null) {
                        updataListView(list);
                    }
                    authInfo = auth;
                    if (listAuth != null) {
                        updataAuthListView(listAuth);
                    }
                    break;

                case TYPE_IPC_SET_CHANNEL_INFO:
                    if (result == 0) {
                        if (reflashRet == 0) {
                            showToast(tip);
                        } else {
                            showToast("更新IPC设备列表失败");
                        }
                        if (editInfo != null) {
                            mapIp.put(editInfo.mIpSrc, null);
                            editInfo.mIpSrc = editInfo.mIpDst;
                            //editInfo.mUserSrc = editInfo.mUserDst;
                            //editInfo.mPasswordSrc = editInfo.mPasswordDst;
                            editInfo.mModifiedMask = 0x00;
                            mapIp.put(editInfo.mIpSrc, editInfo);
                        }
                    } else {
                        if (editInfo != null) {
                            editInfo.mIpDst = editInfo.mIpSrc;
                            //editInfo.mUserDst = editInfo.mUserSrc;
                            //editInfo.mPasswordDst = editInfo.mPasswordSrc;
                            editInfo.mModifiedMask = 0x00;
                        }
                        tip += "(" + result.intValue() + ")";
                        tip += reflashTip;
                        err(tip);
                        showToast(tip);
                    }
                    if (list != null) {
                        updataListView(list);
                    } else if (adapterIpc != null) {
                        adapterIpc.notifyDataSetChanged();
                    }
                    break;

                case TYPE_IPC_AUTH_TEST:
                    if (ret != 0) {
                        showToast(tip);
                    }
                    if (listAuth != null) {
                        updataAuthListView(listAuth);
                    }
                    break;

                case TYPE_IPC_AUTH_SET:
                    if (ret == 0 && authInfo != null) {
                        if (reflashRet == 0) {
                            //showToast(tip);
                        } else {
                            showToast("更新IPC设备列表失败");
                        }
                        authInfo.mUserDst = authInfo.mUserSrc = auth.mUserDst;
                        authInfo.mPasswordDst = authInfo.mPasswordSrc = auth.mPasswordDst;
                        hideIpcAuthDlg();
                    } else {
                        tip += "(" + result.intValue() + ")";
                        tip += reflashTip;
                        err(tip);
                        showToast(tip);
                    }
                    if (list != null) {
                        updataListView(list);
                    } else if (adapterIpc != null) {
                        adapterIpc.notifyDataSetChanged();
                    }
                    break;

                case TYPE_IPC_BIND_CHANNEL:
                    if (result == 0) {
                        if (channelDst == IPCChannelInfo.CHANNEL_INVALID) {
                            debug("  channelDst == CHANNEL_INVALID");
                            mapChannel.put(selectInfo.mChannelSrc, null);
                            selectInfo.mChannelSrc = selectInfo.mChannelDst = IPCChannelInfo.CHANNEL_INVALID;
                        } else {
                            if (oldInfo != null && oldInfo.mChannelDst == channelDst) {
                                mapChannel.put(newInfo.mChannelSrc, null);
                                newInfo.mChannelSrc = newInfo.mChannelDst = channelDst;
                                oldInfo.mChannelSrc = oldInfo.mChannelDst = IPCChannelInfo.CHANNEL_INVALID;
                                mapChannel.put(channelDst, newInfo);
                            } else {
                                mapChannel.put(selectInfo.mChannelSrc, null);
                                selectInfo.mChannelSrc = selectInfo.mChannelDst = channelDst;
                                mapChannel.put(channelDst, selectInfo);
                            }
                        }
                        tip += reflashTip;
                        debug(tip);
                        //showToast("操作成功" + reflashTip);

                        if (reflashRet == 0) {
                            //showToast("操作成功");
                        } else {
                            showToast("更新IPC设备列表失败");
                        }
                    } else {
                        tip += "(" + result.intValue() + ")";
                        tip += reflashTip;
                        err(tip);
                        showToast(tip);
                    }
                    if (list != null) {
                        updataListView(list);
                    } else if (adapterIpc != null) {
                        adapterIpc.notifyDataSetChanged();
                    }
                    //if (adapterIpc != null) {
                    //    adapterIpc.notifyDataSetChanged();
                    //}
                    //if (editChannelInfo != null && spIpcChannel != null && ipcEditDlg != null && ipcEditDlg.isShowing()){
                    //    spIpcChannel.setTag(editChannelInfo);
                    //    spIpcChannel.setSelection(channel2selection(editChannelInfo.mChannelDst));
                    //}
                    break;
                default:
                    break;
            }
        }
    }

    public static IPCChannelInfo channelInfoCopy(IPCChannelInfo info) {
        IPCChannelInfo copy = new IPCChannelInfo();
        copy.mChannelSrc = info.mChannelSrc;
        copy.mChannelDst = info.mChannelDst;
        copy.mIpSrc = info.mIpSrc;
        copy.mIpDst = info.mIpDst;
        copy.mUserSrc = info.mUserSrc;
        copy.mUserDst = info.mUserDst;
        copy.mPasswordSrc = info.mPasswordSrc;
        copy.mPasswordDst = info.mPasswordDst;
        copy.mState = info.mState;
        copy.mWorkState = info.mWorkState;
        copy.mResult = info.mResult;
        copy.mModifiedMask = info.mModifiedMask;

        return copy;
    }

    private void debug(String info) {
        Log.i(TAG, info);
    }

    private void warning(String info) {
        Log.w(TAG, info);
    }

    private void err(String info) {
        Log.e(TAG, info);
    }
}
