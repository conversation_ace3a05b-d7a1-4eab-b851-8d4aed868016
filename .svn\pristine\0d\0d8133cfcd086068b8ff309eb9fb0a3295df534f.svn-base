<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/main_background">

    <LinearLayout
        android:id="@+id/title_bar"
        android:orientation="horizontal"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true">

        <include layout="@layout/main_title_positiontype"></include>
    </LinearLayout>

    <TextView
        android:layout_below="@+id/title_bar"
        android:id="@+id/txtStatus"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5px"
        android:layout_marginLeft="80px"
        android:textColor="@color/green"
        android:textSize="26sp"


        />

    <LinearLayout
        android:id="@+id/layoutRadio"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="60px"
        android:orientation="vertical"
        android:layout_marginLeft="80px"
        android:layout_marginTop="50px"
        android:background="@drawable/list_bg"
        android:padding="0px"
        android:gravity="center">

        <TextView
            android:id="@+id/text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/position_type"
            style="@style/TitleText"
            android:padding="5dip"
            android:gravity="center_vertical|center_horizontal" />

        <RadioGroup
            android:id="@+id/position_type_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="5dip">

            <RadioButton
                android:id="@+id/rb_gps"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/gps_type"
                android:gravity="center_vertical|center_horizontal" />

            <RadioButton
                android:id="@+id/rb_beidou"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/beidou_type"
                android:gravity="center_vertical|center_horizontal" />

            <RadioButton
                android:id="@+id/rb_hunhe"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/hunhe_type"
                android:gravity="center_vertical|center_horizontal" />
        </RadioGroup>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layoutButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toRightOf="@+id/layoutRadio"
        android:layout_alignTop="@+id/layoutRadio"
        android:layout_marginLeft="20px"
        android:orientation="vertical">

        <Button
            android:id="@+id/btnSetMode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/Kaitian_taxi_set"
            android:textSize="28sp"

            android:onClick="onClick" />

        <Button

            android:id="@+id/btnCheckGpsSignal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40px"
            android:text="@string/Kaitian_taxi_check_gps_signal"
            android:textSize="28sp"

            android:onClick="onClick" />

    </LinearLayout>


</RelativeLayout>
