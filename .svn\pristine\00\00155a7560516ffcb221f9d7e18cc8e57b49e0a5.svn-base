package com.yaxon.base;

import android.os.Bundle;
import android.view.WindowManager;

import androidx.annotation.LayoutRes;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProviders;

import com.trello.lifecycle2.android.lifecycle.AndroidLifecycle;
import com.trello.rxlifecycle3.LifecycleProvider;
import com.yaxon.base.common.BaseViewModel;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * 此类可用于mvvm所继承
 * @param <VDB>
 * @param <VM>
 */
public abstract class MvvmActivity<VDB extends ViewDataBinding, VM extends BaseViewModel> extends BaseActivity{
    private VM viewModel;
    private VDB dataBinding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        dataBinding = DataBindingUtil.setContentView(this, getLayoutId());
        initViews(savedInstanceState);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        bindingView();
        initData();
    }

    /**
     * 绑定view
     */
    private void bindingView(){
        viewModel = initViewModel();
        if (viewModel == null) {
            Class modelClass;
            Type type = getClass().getGenericSuperclass();
            if (type instanceof ParameterizedType) {
                modelClass = (Class) ((ParameterizedType) type).getActualTypeArguments()[1];
            } else {
                //如果没有指定泛型参数，则默认使用BaseViewModel
                modelClass = BaseViewModel.class;
            }
            viewModel = (VM) createViewModel(this, modelClass);
        }

        setObjectVariable();
        //支持LiveData绑定xml，数据改变，UI自动会更新
        getDataBinding().setLifecycleOwner(this);
        //让ViewModel拥有View的生命周期感应
        getLifecycle().addObserver(viewModel);
        //注入RxLifecycle生命周期
        LifecycleProvider<Lifecycle.Event> lifecycleProvider = AndroidLifecycle.createLifecycleProvider(this);
        viewModel.injectLifecycleProvider(lifecycleProvider);
    }

    @Override
    public void finish()
    {
        super.finish();
    }

    /**
     * 设置需要绑定到databinding的对象
     */
    protected abstract void setObjectVariable();

    /**
     * 放置layout布局ID
     * @return
     */
    protected abstract @LayoutRes int getLayoutId();

    /**
     * 如果viewmodel需要依赖其他model需使用factory创建viewmodel时，调用该方法创建
     * @return 继承BaseViewModel的ViewModel
     */
    public VM initViewModel() {
        return null;
    }

    /**
     * 获取当前viewModel对象
     * @return
     */
    public VM getViewModel(){
        if (viewModel == null){
            throw new NullPointerException("err, the viewModel can not be null");
        }

        return viewModel;
    }

    /**
     * 创建ViewModel
     *
     * @param cls
     * @param <T>
     * @return
     */
    public <T extends ViewModel> T createViewModel(FragmentActivity activity, Class<T> cls) {
        return ViewModelProviders.of(activity).get(cls);
    }

    /**
     * 获取databinding对象
     * @return
     */
    protected VDB getDataBinding(){
        if (dataBinding == null){
            throw new NullPointerException("err, the dataBinding can not be null");
        }
        return dataBinding;
    }


    /**
     * 初始化views相关
     */
    public void initViews(@Nullable Bundle savedInstanceState){

    }

    /**
     * 初始化数据相关
     */
    protected abstract void initData();
}
