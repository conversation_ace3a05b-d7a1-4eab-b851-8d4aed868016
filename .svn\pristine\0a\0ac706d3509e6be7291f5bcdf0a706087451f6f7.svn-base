package com.yaxon.telematics.service;

import android.util.Log;


/**
 * 这个类不实现任何功能，用于在未识别分体机、两锭机前的接口调用
 *
 * <AUTHOR>
 */
public class ImpYxPlatformService_NULL implements IYxPlatformService {

    private final String TAG = ImpYxPlatformService_NULL.class.getName();

    private static ImpYxPlatformService_NULL mInst = null;

    @Override
    public int getPlatformType() {
        // TODO Auto-generated method stub
        return CommServiceDefine.PLATFORM_TYPE_UNKNOWN;
    }

    private ImpYxPlatformService_NULL() {

    }

    public static synchronized ImpYxPlatformService_NULL getInstance() {
        if (mInst == null) {
            mInst = new ImpYxPlatformService_NULL();
        }

        return mInst;
    }

    @Override
    public boolean DvrUsbSet(int flag) {
        // TODO Auto-generated method stub
        Log.i(TAG, "NULL----  DvrUsbSet");
        return false;
    }

    @Override
    public boolean setUsbFunction(int flag) {
        // TODO Auto-generated method stub
        Log.i(TAG, "NULL----  setUsbFunction");
        return false;
    }

    @Override
    public boolean register(String appNameString, String appFlagString, int priority, int requestResource) {
        // TODO Auto-generated method stub
        Log.i(TAG, "NULL----  register");
        return false;
    }

    @Override
    public void unregister(String appName, String appFlagString, int requestResource) {
        // TODO Auto-generated method stub
        Log.i(TAG, "NULL----  unregister");
    }

    @Override
    public int PowerKeySet(int flag) {
        // TODO Auto-generated method stub
        Log.i(TAG, "NULL----  PowerKeySet");
        return 0;
    }

    @Override
    public int Cam0PowEn(int flag) {
        // TODO Auto-generated method stub
        Log.i(TAG, "NULL----  Cam0PowEn");
        return 0;
    }

    @Override
    public int Cam1PowEn(int flag) {
        // TODO Auto-generated method stub
        Log.i(TAG, "NULL----  Cam1PowEn");
        return 0;
    }

    @Override
    public int Cam2PowEn(int flag) {
        // TODO Auto-generated method stub
        Log.i(TAG, "NULL----  Cam2PowEn");
        return 0;
    }

    @Override
    public int SwitchChannel(String VideoChannel) {
        // TODO Auto-generated method stub
        Log.i(TAG, "NULL----  SwitchChannel");
        return 0;
    }

    @Override
    public String SeachCurChannel() {
        // TODO Auto-generated method stub
        Log.i(TAG, "NULL----  SeachCurChannel");
        return null;
    }

    @Override
    public int FmPowEn(int bFlag) {
        // TODO Auto-generated method stub
        Log.e(TAG, "NULL----  Cam0PowEn not implemented");
        return -1;
    }

}
