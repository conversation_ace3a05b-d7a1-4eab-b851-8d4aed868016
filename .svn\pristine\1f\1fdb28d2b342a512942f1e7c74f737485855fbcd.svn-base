 package com.yaxon.base.maintain.recorder;

 import android.annotation.SuppressLint;
 import android.app.Dialog;
 import android.content.Context;
 import android.content.DialogInterface;
 import android.content.DialogInterface.OnKeyListener;
 import android.graphics.Color;
 import android.os.AsyncTask;
 import android.os.Bundle;
 import android.os.Handler;
 import android.os.Message;
 import android.view.KeyEvent;
 import android.view.LayoutInflater;
 import android.view.View;
 import android.view.View.OnClickListener;
 import android.widget.AbsListView;
 import android.widget.AbsListView.OnScrollListener;
 import android.widget.Button;
 import android.widget.LinearLayout;
 import android.widget.ListView;
 import android.widget.RadioButton;
 import android.widget.RadioGroup;
 import android.widget.RadioGroup.OnCheckedChangeListener;
 import android.widget.RelativeLayout;
 import android.widget.TextView;
 import android.widget.Toast;

 import com.yaxon.base.R;
 import com.yaxon.base.YXActivity;
 import com.yaxon.base.maintain.CarSignalDetectionActivity;
 import com.yaxon.common.YXDefine;
 import com.yaxon.datasource.api.ControlApi;
 import com.yaxon.telematics.service.aidl.main.DrivingRecord;
 import com.yaxon.utils.Log;
 import com.yaxon.utils.Tools;
 import com.yaxon.utils.YXAsyncTask;

 import java.util.ArrayList;

 /**
  * 行驶记录仪界面
  * <AUTHOR>
  * @version   版本号，2013-4-27
  * @see       [相关类/方法]
  * @since     [产品/模块版本]
  */
 public class DrivingRecorderActivity extends YXActivity {

     public final static String ACTION = "com.yaxon.DrivingRecorderActivity";

     public static Context context;
     //对话框类型
     private static final int TYPE_DIALOG_EXPORT = 1;//U盘数据导出对话框
     private static final int TYPE_DIALOG_PRINT = 2;//打印信息对话框
     private static final int TYPE_DIALOG_LOAD = 3;//载重设置对话框
     private int dialog_type;//当前对话框类型


     public final static int HANDLER_REFRESH_DIALOG_MESSAGE = 0;//刷新对话框提示信息
     public final static int HANDLER_EXPORT_DATA_UDISK = 1;//U盘导出数据
     public final static int HANDLER_REFRESH_LISTVIEW = 2;//刷新列表
     public static final int HIDE_NEXT_PRE = 3;
     public final static int HANDLER_CLOSE_PROMPT = 0x10;//关闭提示对话框
 //	public final static int  REFRESH_DIALOG_MESSAGE = 2;


     private int export_type = ControlApi.EXPORT_DATA_NOTAT;//导出U盘数据状态类型


     private LinearLayout menu; //菜单
     private ListView list; //列表
     private Dialog dialog;//对话框
     private TextView text_message;//对话框title
     private Button btn_ok;//导出数据对话框确定按钮
     private Button btn_cancel;//导出数据对话框退出按钮
     private DrivingDataAdapter mAdapter; //行驶记录仪数据列表适配器

     private ArrayList<DrivingDataAdapter.Data> data_list;//列表数据

     private ControlApi mControlApi = null;//通讯服务层部分接口分装类

     private int load_type = ControlApi.FAL_LOAD;//当前载重状态
     private String load_type_str; //载重信息
     private int last_load = load_type;//载重状态
     private int message;//是否有打印数据信息

     private TextView text_title_bat;//标题栏信息
     private View btn_exit;//退出按钮
     private View btn_menu;//菜单
     private RelativeLayout mlayout;


     public static Handler mHandler;

     private LoadDataAsyncTask loadTask;//加载行驶记录仪数据异步线程
     //private PcUsbSetAsyncTask usbTask;
     private SetLoadStatus mSetLoadStatus;
     private QueryUsbExportData mQueryUsbExportDataByClick = null;
     private QueryUsbExportData mQueryUsbExportDataByDialog = null;

     public boolean flaga = false;
     public boolean flagb = false;
     public boolean flagaDown = false;
     public boolean flagbDown = false;

     @SuppressLint("MissingSuperCall")
     @Override
     protected void onCreate(Bundle savedInstanceState) {
         // TODO Auto-generated method stub
         super.onCreate(savedInstanceState, R.layout.driving_recorder);

         mControlApi = ControlApi.getInstance();//与通讯服务层的接口封装类

         text_title_bat = (TextView) findViewById(R.id.title_text_name);//标题

         text_title_bat.setText(R.string.app_titel);

         load_type_str = getString(R.string.fal_load);

         data_list = new ArrayList<DrivingDataAdapter.Data>();
         initView();//初始化控件

         mHandler = new Handler()
         {
             @Override
         public void handleMessage(Message msg) {
             switch (msg.what) {
             case HANDLER_REFRESH_DIALOG_MESSAGE:
                 if(dialog_type == TYPE_DIALOG_EXPORT)
                 {
                     if(dialog != null)
                     {
                         if(dialog.isShowing())
                         {
                             if((Boolean)msg.obj)
                             {
 //								int text;
 //
 //								if(mControlApi.hasDataExportToUdisk())
 //								{
 //									btn_ok.setEnabled(true);
 //									btn_ok.setTextColor(Color.WHITE);
 //									text = R.string.with_data_out;
 //								}
 //								else
 //								{
 //									btn_ok.setEnabled(false);
 //									btn_ok.setTextColor(Color.GRAY);
 //									text = R.string.no_data_out;
 //								}
 //								text_message.setText(text);

                                 mQueryUsbExportDataByDialog = new QueryUsbExportData(DrivingRecorderActivity.this,
                                         null, false, null);
                                 mQueryUsbExportDataByDialog.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, QueryUsbExportData.DEAL_TYPE_UPDATE_DIALOG);
                             }
                             else
                             {
                                 btn_ok.setTextColor(Color.GRAY);
                                 btn_ok.setEnabled(false);
                                 text_message.setText(R.string.export_data_message);
                             }
                         }
                     }
                 }
                 break;
             case HANDLER_EXPORT_DATA_UDISK:
                 int flog = msg.arg1;
                 if(flog == ControlApi.EXPORT_DATA_NOTAT)//无法执行导出请求
                 {
                     export_type = ControlApi.EXPORT_DATA_NOTAT;
                     text_message.setText(R.string.export_data_fal);
                 }else if(flog == ControlApi.EXPORT_DATA_SUC)//导出请求执行成功
                 {
                     export_type = ControlApi.EXPORT_DATA_SUC;
                     text_message.setText(R.string.export_data_suc);

                     {//cyh add
                         btn_ok.setTextColor(Color.GRAY);
                         btn_ok.setEnabled(false);
                         btn_cancel.setEnabled(false);
                         btn_cancel.setTextColor(Color.GRAY);
                         Message msg_close_prompt = mHandler.obtainMessage(HANDLER_CLOSE_PROMPT);
                         mHandler.sendMessageDelayed(msg_close_prompt, 1000);
                     }

                 }else if(flog == ControlApi.EXPORT_DATA_FAL)//导出请求执行失败
                 {
                     export_type = ControlApi.EXPORT_DATA_FAL;
                     text_message.setText(R.string.export_data_fal);
                 }else//设备正忙稍候操作
                 {
                     export_type = ControlApi.EXPORT_DATA_BUSY;
                     text_message.setText(R.string.device_busy);
                 }
                 break;
             case HANDLER_REFRESH_LISTVIEW://刷新列表
                 System.out.println("-------------llll-------------------"+msg.what+"-------"+data_list.size());
                 mAdapter.notifyDataSetChanged(data_list);
                 break;
             case HIDE_NEXT_PRE:
                  mlayout.setVisibility(View.GONE);
                 break;
             case HANDLER_CLOSE_PROMPT:
                 if(dialog!=null)
                 {
                     dialog.dismiss();
                     dialog=null;
                 }
                 else
                 {
                     System.out.println("------HANDLER_CLOSE_PROMPT hide prompt--------");
                 }
             default:
                 break;
             }
             super.handleMessage(msg);
         }};

         if(ControlApi.isUse)//加载数据
         {
             loadTask = new LoadDataAsyncTask();
             loadTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, "loadData");
         }

         mHandler.sendEmptyMessageDelayed(HIDE_NEXT_PRE, YXDefine.HIDE_TIME);
     }

     @Override
     protected void onResume() {
         context = this;
         super.onResume();
     }

     @Override
     protected void onPause() {
         context = null;
         super.onPause();
     }

     @Override
     protected void onDestroy()
     {
         super.onDestroy();

 //		if(YXApplication.mGpio != null)
 //        {
 //            try
 //            {
 //                YXApplication.mGpio.PcUsbSet(0);
 //            }
 //            catch (RemoteException e)
 //            {
 //                // TODO Auto-generated catch block
 //                e.printStackTrace();
 //            }
 //        }

         mHandler = null;
         if(loadTask != null)
         {
             Log.println("Task",loadTask.cancel(true)+"");//取消加载
         }
 //        if(usbTask != null)
 //        {
 //            usbTask.cancel(true);
 //        }

         if (mQueryUsbExportDataByClick != null)
         {
             mQueryUsbExportDataByClick.cancel(true);
         }

         if (null != mQueryUsbExportDataByDialog)
         {
             mQueryUsbExportDataByDialog.cancel(true);
         }
     }

     @Override
     public boolean dispatchKeyEvent(KeyEvent event) {


         switch (event.getKeyCode()) {
         case KeyEvent.KEYCODE_DPAD_UP:
             if(event.isLongPress())
             {
                 System.out.println("-----------long---up-----------");
                 flaga= true;
                 flagaDown = true;
                 break;
             }else{
                 if(event.getAction() == KeyEvent.ACTION_DOWN)
                 {
                     flagaDown = true;
                 }else
                 {
                     if (!flagaDown || !flagbDown) {
                         if(!flaga && !flagb)
                         {
                             System.out.println(flagaDown+" "+flagbDown+" "+flaga+" "+flagb);
                             preOnClick();
                         }
                     }
                     flagaDown = false;
                     flaga= false;
                 }

                 return true;
             }
         case KeyEvent.KEYCODE_DPAD_DOWN:
             if(event.isLongPress())
             {
                 System.out.println("-----------long----down----------");
                 flagb = true;
                 flagbDown = true;
                 break;
             }else{
                 if(event.getAction() == KeyEvent.ACTION_DOWN)
                 {
                     flagbDown = true;

                 }else
                 {
                     if (!flagaDown || !flagbDown) {
                         if(!flaga && !flagb)
                         {
                             System.out.println(flagaDown+" "+flagbDown+" "+flaga+" "+flagb);
                             nextOnClick();
                         }
                     }
                     flagb= false;
                     flagbDown = false;
                 }
                 return true;
             }
         default:
             break;
         }
         if ( flagaDown &&  flagbDown) {
             if(flaga || flagb)
             {
                 System.out.println("-----------------------------onclick---------------------------------");

             }
             return true;
         }
         return super.dispatchKeyEvent(event);
     }

     /**
      * 初始化列表数据
      *  备注：
      * <pre>
      * 调用示例代码
      * </pre>
      * @exception   [异常类型] [异常说明]
      * @see          [类、类#方法、类#成员]
      * @since        [从哪个版本开始有该方法]
      */
     private void initListData()
     {
         DrivingDataAdapter.Data data;
         DrivingRecord dr;
         load_type = mControlApi.getVehicleLoadStatus();//加载载重信息
         setLoadStr();//设置载重信息
         last_load = load_type;
         ArrayList<DrivingRecord> list = mControlApi.getTwoDaysDrivingRecord();//加载行驶记录数据
         ArrayList<ControlApi.Data> list_speeds= mControlApi.getFifteenSpeed();//15分平均速度
         data_list = new ArrayList<DrivingDataAdapter.Data>();


         data_list.add(new DrivingDataAdapter.Data(getString(R.string.load_state),load_type_str));
         if(list != null)
         {
             int size = list.size();
             if(size != 0)
             {
                 for (int i = 0; i < size; i++) {
                     int num = i+1;
                     data_list.add(new DrivingDataAdapter.Data("#nodata#", getString(R.string.driving_data)+num));

                     data = new DrivingDataAdapter.Data();
                     dr = list.get(i);
                     data.title = getString(R.string.start_end_time);
                     data.data = Tools.getTime(dr.mStartTime, Tools.PATTERN_DATE2)
                                 + getString(R.string.zhi)
                                 + Tools.getTime(dr.mEndTime, Tools.PATTERN_DATE2);
                     data_list.add(data);

                     data = new DrivingDataAdapter.Data();
                     data.title = getString(R.string.driver_no);
                     data.data = dr.mDriverCardId;
                     data_list.add(data);

                     data = new DrivingDataAdapter.Data();
                     data.title = getString(R.string.mileage_msg);
                     data.data = dr.mileage+getString(R.string.km);
                     data_list.add(data);
                 }
             }else
             {
 //				System.out.println("------77777777777777-----DrivingRecord list size == 0");
                 data_list.add(new DrivingDataAdapter.Data("#nodata#", getString(R.string.no_driving_data)));
             }
         }else
         {
 //			System.out.println("------77777777777777-----DrivingRecord list == null");
             data_list.add(new DrivingDataAdapter.Data("#nodata#", getString(R.string.no_driving_data)));
         }
         data_list.add(new DrivingDataAdapter.Data("#15#", getString(R.string.average_speed)));
         int size = list_speeds.size();
         if(size != 0)
         {
             for (int i = 0; i < size; i++) {
                 data_list.add(new DrivingDataAdapter.Data("",list_speeds.get(i).toString()));
             }
         }else
         {
             data_list.add(new DrivingDataAdapter.Data("",getString(R.string.no_data)));
         }
     }
     /**
      * 设置载重信息
      *  备注：
      * <pre>
      * 调用示例代码
      * </pre>
      * @exception   [异常类型] [异常说明]
      * @see          [类、类#方法、类#成员]
      * @since        [从哪个版本开始有该方法]
      */
     private void setLoadStr()
     {
         switch (load_type) {
         case ControlApi.NULL_LOAD://空载
             load_type_str = getString(R.string.null_load);
             break;
         case ControlApi.HEAVY_LOAD://满载
             load_type_str = getString(R.string.heavy_load);
             break;
         case ControlApi.HALF_LOAD://半载
             load_type_str = getString(R.string.half_load);
             break;
         case ControlApi.FAL_LOAD://获取失败
             load_type_str = getString(R.string.fal_load);
             break;
         default:
             break;
         }
     }

     /**
      * 初始化控件
      *  备注：
      * <pre>
      * 调用示例代码
      * </pre>
      * @exception   [异常类型] [异常说明]
      * @see          [类、类#方法、类#成员]
      * @since        [从哪个版本开始有该方法]
      */
     private void initView()
     {
         mlayout = (RelativeLayout) findViewById(R.id.layout_btn_next_pre);
         btn_exit = findViewById(R.id.title_btn_exit);
         btn_menu = findViewById(R.id.title_btn_menu);
         menu = (LinearLayout) findViewById(R.id.menu);
         list = (ListView) findViewById(R.id.list_data);
         list.setDivider(null);
         mAdapter = new DrivingDataAdapter(this, data_list);
         list.setAdapter(mAdapter);
         list.setOnKeyListener(new View.OnKeyListener() {

             @Override
             public boolean onKey(View v, int keyCode, KeyEvent event) {
                 switch (keyCode) {
                 case KeyEvent.KEYCODE_DPAD_DOWN:

                     return false;
                 case KeyEvent.KEYCODE_DPAD_UP:

                     return false;
                 default:
                     break;
                 }
                 return false;
             }
         });

         list.setOnScrollListener(new OnScrollListener() {

             @Override
             public void onScrollStateChanged(AbsListView arg0, int scrollState) {
                 // TODO Auto-generated method stub
                 if(scrollState == SCROLL_STATE_TOUCH_SCROLL)
                 {
                     showNextPre();
                 }
             }

             @Override
             public void onScroll(AbsListView arg0, int arg1, int arg2, int arg3) {

             }
         });

         btn_exit.setOnClickListener(new OnClickListener() {

             @Override
             public void onClick(View arg0) {
                 DrivingRecorderActivity.this.finish();
             }
         });
         btn_menu.setOnClickListener(new OnClickListener() {

             @Override
             public void onClick(View arg0) {
                 // TODO Auto-generated method stub
                 if(menu.getVisibility() == View.GONE)
                 {
                     menu.setVisibility(View.VISIBLE);
                 }else
                 {
                     menu.setVisibility(View.GONE);
                 }
             }
         });
     }

     /**
      * button 的onclick事件
      *  备注：
      * <pre>
      * 调用示例代码
      * </pre>
      * @param view 点击的控件
      * @exception   [异常类型] [异常说明]
      * @see          [类、类#方法、类#成员]
      * @since        [从哪个版本开始有该方法]
      */
     public void onClick(View view)
     {
         switch (view.getId()) {

         case R.id.menu_export_data://数据导出
             dialog_type = TYPE_DIALOG_EXPORT;
 //			usbTask = new PcUsbSetAsyncTask();
 //			usbTask.execute(1);

             exportDataOnclik(view);

             break;
 //		case R.id.menu_control_media_data://媒体数据控制
 //			Intent intent_control_media_data = new Intent(MediaControlActivity.ACTION);
 //			startActivity(intent_control_media_data);
 //			break;
         case R.id.menu_car_detection://车辆检测
             //Intent intent_detection = new Intent(CarSignalDetectionActivity.ACTION);
             //Intent intent_detection = new Intent(CarDetectionActivity.ACTION);  //
             //startActivity(intent_detection);
             yxStartActivity(CarSignalDetectionActivity.class);
             break;
         case R.id.menu_set_load_state://载重设置
             dialog_type = TYPE_DIALOG_LOAD;
             loadStateOnclik();
             break;
 //		case R.id.menu_driver_analaysis://数据分析
 //			IDemoChart mCharts = new DriverAnalysisChart();
 //			startActivity(mCharts.execute(this));
 //			break;
         case R.id.menu_print_data://打印
             dialog_type = TYPE_DIALOG_PRINT;
             printDataOnclik();
             break;
         case R.id.btn_next://下一页
             showNextPre();
             nextOnClick();
             break;
         case R.id.btn_pre://上一页
             showNextPre();
             preOnClick();
             break;
         default:
             break;
         }
     }

     public void showNextPre()
     {
         mHandler.removeMessages(HIDE_NEXT_PRE);
         mlayout.setVisibility(View.VISIBLE);
         mHandler.sendEmptyMessageDelayed(HIDE_NEXT_PRE, YXDefine.HIDE_TIME);
     }

     private void preOnClick()
     {
 //		System.out.println("-------------------------pre onclick-----------------------------");
         int tmpPosition = 0;
         int currentFirstPosition = list.getFirstVisiblePosition();
         if(currentFirstPosition>=9){
             tmpPosition = currentFirstPosition-9;
         }
         list.setSelection(tmpPosition);
     }

     private void nextOnClick()
     {
 //		System.out.println("-------------------------next onclick-----------------------------");
         list.setSelection(list.getLastVisiblePosition()+1);
     }

     /**
      * 导出数据点击操作
      *  备注：
      * <pre>
      * 调用示例代码
      * </pre>
      * @exception   [异常类型] [异常说明]
      * @see          [类、类#方法、类#成员]
      * @since        [从哪个版本开始有该方法]
      */
     private void exportDataOnclik(View ctrlView)
     {

         message = R.string.export_data_message;

         mQueryUsbExportDataByClick = new QueryUsbExportData(DrivingRecorderActivity.this,
                 ctrlView, true, null);
         mQueryUsbExportDataByClick.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, QueryUsbExportData.DEAL_TYPE_EXPORT_DATA);

 //		if(Tools.getUidskStatu())//U盘是否挂载
 //		{
 //			if(mControlApi.hasDataExportToUdisk())//有无数据可以导出
 //			{
 //				message = R.string.with_data_out;
 //			}
 //			else
 //			{
 //				message = R.string.no_data_out;
 //			}
 //		}
 //
 //		//对话框ok按钮点击事件
 //		OnClickListener ok_export = new OnClickListener()
 //		{
 //
 //			@Override
 //			public void onClick(View v)
 //			{
 //				if(dialog!=null)
 //				{
 //					if(Tools.getUidskStatu())//U盘是否挂载
 //					{
 //						if(!mControlApi.hasDataExportToUdisk())//是否有数据可以导出
 //						{
 //						    //提示无数据可以导出
 //							Toast.makeText(DrivingRecorderActivity.this, R.string.no_data, Toast.LENGTH_SHORT).show();
 //						}
 //						else
 //						{//导出数据
 //							text_message.setText(R.string.exporting);
 //							btn_ok.setEnabled(false);
 //							btn_ok.setTextColor(Color.GRAY);
 //							btn_cancel.setEnabled(false);
 //							btn_cancel.setTextColor(Color.GRAY);
 //							MyAsyncTask mat = new MyAsyncTask();
 //							mat.execute("start");
 //						}
 //					}
 //					else
 //					{//提示插入U盘
 //						Toast.makeText(DrivingRecorderActivity.this, R.string.export_data_message, Toast.LENGTH_SHORT).show();
 //					}
 //				}
 //
 //			}
 //		};
 //
 //		OnClickListener cancel_export = new OnClickListener()
 //		{
 //
 //				@Override
 //				public void onClick(View v) {
 //					if(YXApplication.mGpio != null)
 //					{
 //						try {
 //							YXApplication.mGpio.PcUsbSet(0);
 //						} catch (RemoteException e) {
 //							// TODO Auto-generated catch block
 //							e.printStackTrace();
 //						}
 //					}
 //					if(dialog!=null)
 //					{
 //						dialog.dismiss();
 //						dialog=null;
 //					}
 //					if(usbTask != null)
 //					{
 //						usbTask.cancel(true);
 //					}
 //
 //				}
 //			};
 //
 //
 //		onClickExportOrPrintData(R.layout.export_print_data_dialog, message,
 //				ok_export, cancel_export);
     }
     /**
      * 加载载重状态设置对话框
      *  备注：
      * <pre>
      * 调用示例代码
      * </pre>
      * @exception   [异常类型] [异常说明]
      * @see          [类、类#方法、类#成员]
      * @since        [从哪个版本开始有该方法]
      */
     private void loadStateOnclik()
     {
         //设置载重状态确定按钮
         OnClickListener ok_set_load = new OnClickListener()
         {

             @Override
             public void onClick(View v)
             {
                     if(dialog!=null)
                     {
                         if(last_load != load_type)//当前载重状态与设置的不等进入设置
                         {
                             Toast.makeText(DrivingRecorderActivity.this, load_type+":"+load_type_str, Toast.LENGTH_SHORT).show();

 //							boolean flog = mControlApi.setVehicleLoadStatus(load_type);//设置载重状态
 //							if(flog)//载重设置结果
 //							{//载重设置成功
 //								mAdapter.updataLoadText(load_type_str);//更新列表上的载重信息
 //								last_load = load_type;
 //								dialog.dismiss();
 //								dialog=null;
 //							}
 //							else
 //							{//提示设置失败
 //								System.out.println("提示设置失败------"+last_load);
 //								Toast.makeText(DrivingRecorderActivity.this, R.string.set_fal, Toast.LENGTH_SHORT).show();
 ////								load_type = last_load;
 //							}

                             mSetLoadStatus = new SetLoadStatus(DrivingRecorderActivity.this,
                                     btn_ok, true, null);
                             mSetLoadStatus.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, load_type);
                         }
                         else
                         {
                             System.out.println("---------提示设置失败------");
                             Toast.makeText(DrivingRecorderActivity.this,
                                     R.string.set_fal_last, Toast.LENGTH_SHORT).show();
                         }

                         setLoadStr();
                     }
             }
         };

         OnClickListener cancel_set_load  = new OnClickListener()
         {

                 @Override
                 public void onClick(View v)
                 {
                     if(dialog!=null)
                     {
                         load_type = last_load;
                         dialog.dismiss();
                         dialog=null;
                     }

                 }
             };

         onClickSetCarLoadState(R.layout.set_load_state_dialog, ok_set_load, cancel_set_load);
     }

     /**
      * 打印数据
      *  备注：
      * <pre>
      * 调用示例代码
      * </pre>
      * @exception   [异常类型] [异常说明]
      * @see          [类、类#方法、类#成员]
      * @since        [从哪个版本开始有该方法]
      */
     private void printDataOnclik()
     {
 //		message = R.string.no_print_data_message;
 //		if(mControlApi.hasDataToPrint())//判断是否有数据可以打印
 //		{
             message = R.string.has_print_data_message;
 //		}
         OnClickListener ok_print = new OnClickListener() {

             @Override
             public void onClick(View v) {
                 if(dialog!=null)
                 {
 //					if(mControlApi.hasDataToPrint())
 //					{
 //						if(mControlApi.startPrint() == ControlApi.PRINT_DATA_SUC)
 //						{
 //							dialog.dismiss();
 //							dialog=null;
 //						}else
 //						{
 //							Toast.makeText(DrivingRecorderActivity.this, R.string.print_data_fal, Toast.LENGTH_SHORT).show();
 //						}
                         int strId = R.string.print_data_fal;
                         switch (mControlApi.startPrint()) {
                         case ControlApi.PRINT_DATA_SUC:
                             strId = R.string.print_data_suc;
                             break;
                         case ControlApi.PRINT_DATA_FAL:
                             strId = R.string.print_data_fal;
                             break;
                         case ControlApi.PRINT_DATA_BUSY:
                             strId = R.string.device_busy;
                             break;
                         default:
                             break;
                         }
                         Toast.makeText(DrivingRecorderActivity.this, strId, Toast.LENGTH_SHORT).show();
                         dialog.dismiss();
                         dialog=null;
 //					}
                 }

             }
         };
         OnClickListener cancel_print = new OnClickListener() {

                 @Override
                 public void onClick(View v) {
                     if(dialog!=null)
                     {
                         dialog.dismiss();
                         dialog=null;
                     }

                 }
             };
         onClickExportOrPrintData(R.layout.export_print_data_dialog, message,
                 ok_print, cancel_print);
     }

     /**
      * 显示对话框
      *  备注：
      * <pre>
      * 调用示例代码
      * </pre>
      * @param view
      * @exception   [异常类型] [异常说明]
      * @see          [类、类#方法、类#成员]
      * @since        [从哪个版本开始有该方法]
      */
     private void showDialog(View view)
     {
         dialog=new Dialog(this, R.style.Theme_dialog);
         dialog.setContentView(view);
         dialog.setOnKeyListener(new OnKeyListener()
         {
             @Override
             public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event)
             {
                 if(keyCode== KeyEvent.KEYCODE_BACK&&dialog!=null)
                 {
                     dialog.dismiss();
                     dialog=null;
                     return true;
                 }
                 return false;
             }
         });
         //显示
         dialog.show();
     }

     /**
      * 点击导打印信息方法
      *  备注：
      * <pre>
      * 调用示例代码
      * </pre>
      * @param layout
      * @param message
      * @param ok
      * @param cancel
      * @exception   [异常类型] [异常说明]
      * @see          [类、类#方法、类#成员]
      * @since        [从哪个版本开始有该方法]
      */
     private void onClickExportOrPrintData(int layout, int message, OnClickListener ok, OnClickListener cancel)
     {
         if(dialog != null && dialog.isShowing())
         {
             return;
         }
         View view = LayoutInflater.from(this).inflate(layout, null);
         btn_ok = (Button) view.findViewById(R.id.btn_ok);
         btn_cancel = (Button) view.findViewById(R.id.btn_cancel);
         text_message = (TextView) view.findViewById(R.id.text_message);
         if(dialog_type == TYPE_DIALOG_EXPORT)
         {
             if(Tools.getUidskStatu())//U盘是否挂载
             {
                 if(mControlApi.hasDataExportToUdisk())//有无数据可以导出
                 {
                     btn_ok.setEnabled(true);//有数据，确定按钮可以点击
                     btn_ok.setTextColor(Color.WHITE);
                 }else
                 {
                     btn_ok.setEnabled(false);
                     btn_ok.setTextColor(Color.GRAY);
                 }
             }else
             {
                 btn_ok.setEnabled(false);
                 btn_ok.setTextColor(Color.GRAY);
             }
         }else
         {
 //			if(mControlApi.hasDataToPrint())//判断是否有数据可以打印
 //			{
                 btn_ok.setEnabled(true);//有数据，确定按钮可以点击
                 btn_ok.setTextColor(Color.WHITE);
 //			}else
 //			{
 //				btn_ok.setEnabled(false);
 //				btn_ok.setTextColor(Color.GRAY);
 //			}
         }


         btn_ok.setOnClickListener(ok);
         btn_cancel.setOnClickListener(cancel);

         if(!Tools.getUidskStatu() && dialog_type == TYPE_DIALOG_EXPORT)
         {
             text_message.setText(R.string.export_data_message);
         }
         else
         {
             text_message.setText(message);
         }

         showDialog(view);
     }

     /**
      * 设置载重
      *  备注：
      * <pre>
      * 调用示例代码
      * </pre>
      * @param layout
      * @param ok
      * @param cancel
      * @exception   [异常类型] [异常说明]
      * @see          [类、类#方法、类#成员]
      * @since        [从哪个版本开始有该方法]
      */
     private void onClickSetCarLoadState(int layout, OnClickListener ok, OnClickListener cancel)
     {
         if(dialog != null && dialog.isShowing())
         {
             return;
         }
         View view = LayoutInflater.from(this).inflate(layout, null);
         Button btn_ok = (Button) view.findViewById(R.id.btn_ok);
         Button btn_cancel = (Button) view.findViewById(R.id.btn_cancel);
         RadioGroup mGroup = (RadioGroup) view.findViewById(R.id.radio_group);
         RadioButton Raido_null_load = (RadioButton) view.findViewById(R.id.Raido_null_load);
         RadioButton Raido_heavy_load = (RadioButton) view.findViewById(R.id.Raido_heavy_load);
         RadioButton Raido_half_load = (RadioButton) view.findViewById(R.id.Raido_half_load);
         text_message = (TextView) view.findViewById(R.id.text_load_type);
         text_message.setText(load_type_str);

         switch (load_type) {
         case ControlApi.NULL_LOAD:
             Raido_null_load.setChecked(true);
             break;
         case ControlApi.HEAVY_LOAD:
             Raido_heavy_load.setChecked(true);
             break;
         case ControlApi.HALF_LOAD:
             Raido_half_load.setChecked(true);
             break;
         default:
             break;
         }
         btn_ok.setOnClickListener(ok);
         btn_cancel.setOnClickListener(cancel);

         mGroup.setOnCheckedChangeListener(new OnCheckedChangeListener() {

             @Override
             public void onCheckedChanged(RadioGroup group, int checkedId) {
                 switch (checkedId) {
                 case R.id.Raido_null_load:
                     load_type_str = getString(R.string.null_load);
                     load_type = ControlApi.NULL_LOAD;
                     break;
                 case R.id.Raido_heavy_load:
                     load_type_str = getString(R.string.heavy_load);
                     load_type = ControlApi.HEAVY_LOAD;
                     break;
                 case R.id.Raido_half_load:
                     load_type_str = getString(R.string.half_load);
                     load_type = ControlApi.HALF_LOAD;
                     break;
                 default:
                     break;
                 }
                 Toast.makeText(DrivingRecorderActivity.this, load_type_str, Toast.LENGTH_SHORT).show();
             }
         });
         showDialog(view);

     }

     public static void udiskMessage(boolean isMOUNTED)
     {
         if(mHandler != null && context != null)
         {
             Message msg = new Message();
             msg.what = HANDLER_REFRESH_DIALOG_MESSAGE;
             msg.obj = isMOUNTED;
             if(mHandler != null)
             {
                 mHandler.sendMessage(msg);
             }
         }
     }

     /**
      * 执行导出数据异步线程
      * <AUTHOR>
      * @version   版本号，2013-5-18
      * @see       [相关类/方法]
      * @since     [产品/模块版本]
      */
     private class MyAsyncTask extends AsyncTask<String, String, Boolean> {
         @Override
         protected Boolean doInBackground(String... arg0) {
             // TODO Auto-generated method stub
             System.out.println(arg0[0]);
             int flog = mControlApi.startExportDataToUdisk();
             Message msg = new Message();
             msg.what = HANDLER_EXPORT_DATA_UDISK;
             msg.arg1 = flog;
             if(mHandler != null)
             {
                 mHandler.sendMessage(msg);
             }
             return true;
         }

         @Override
         protected void onPostExecute(Boolean result) {
             btn_ok.setEnabled(true);
             btn_ok.setTextColor(Color.WHITE);
             btn_cancel.setEnabled(true);
             btn_cancel.setTextColor(Color.WHITE);
             super.onPostExecute(result);
         }
     }
     /**
      * 加载行驶记录仪数据
      * <AUTHOR>
      * @version   版本号，2013-5-18
      * @see       [相关类/方法]
      * @since     [产品/模块版本]
      */
     private class LoadDataAsyncTask extends AsyncTask<String, String, Boolean> {
         @Override
         protected Boolean doInBackground(String... arg0) {
             // TODO Auto-generated method stub
             initListData();//初始化列表数据
             return true;
         }

         @Override
         protected void onPostExecute(Boolean result) {
             if(mHandler != null)
             {
                 mHandler.sendEmptyMessage(HANDLER_REFRESH_LISTVIEW);
             }
             super.onPostExecute(result);
         }

     }


 //	private class PcUsbSetAsyncTask extends AsyncTask<Integer, String, Integer>
 //	{
 //
 //		@Override
 //		protected Integer doInBackground(Integer... arg0) {
 //			int flag = -1;
 //			if(YXApplication.mGpio != null)
 //			{
 //				try {
 //					flag = YXApplication.mGpio.PcUsbSet(arg0[0]);
 //				} catch (RemoteException e) {
 //					// TODO Auto-generated catch block
 //					e.printStackTrace();
 //				}
 //			}
 //
 //			if(flag != 0)
 //			{
 //				try {
 //					Thread.sleep(1000);
 //				} catch (InterruptedException e) {
 //					// TODO Auto-generated catch block
 //					e.printStackTrace();
 //				}
 //			}
 //			return flag;
 //		}
 //
 //		@Override
 //		protected void onPostExecute(Integer result) {
 //			if(result == 0)
 //			{
 //				Toast.makeText(DrivingRecorderActivity.this, "1接面板", Toast.LENGTH_SHORT).show();
 //			}else
 //			{
 //				usbTask = new PcUsbSetAsyncTask();
 //				usbTask.execute(1);
 //			}
 //			super.onPostExecute(result);
 //		}
 //	}

     private class QueryUsbExportData extends YXAsyncTask<Integer, Integer, Boolean>
     {

         public static final int DEAL_TYPE_UPDATE_DIALOG = 1;
         public static final int DEAL_TYPE_EXPORT_DATA = 2;

         private int mDealType = -1;
         private boolean mIsHasData = false;

         public QueryUsbExportData(Context context, View view, boolean isShowPrompt, String content)
         {
             super(context, view, isShowPrompt, content);
         }

         @Override
         protected Boolean doInBackground(Integer... params)
         {
             // TODO Auto-generated method stub
             if (null != params && params.length > 0)
             {
                 mDealType = params[0];
                 return mControlApi.hasDataExportToUdisk();
             }
             return super.doInBackground(params);
         }

         @Override
         protected void onPostExecute(Boolean result)
         {
             // TODO Auto-generated method stub
             super.onPostExecute(result);
             if (null == result)
             {
                 return;
             }
             mIsHasData = result;

             if (DEAL_TYPE_UPDATE_DIALOG == mDealType)
             {
                 int text;

                 if(result)
                 {
                     btn_ok.setEnabled(true);
                     btn_ok.setTextColor(Color.WHITE);
                     text = R.string.with_data_out;
                 }
                 else
                 {
                     btn_ok.setEnabled(false);
                     btn_ok.setTextColor(Color.GRAY);
                     text = R.string.no_data_out;
                 }
                 text_message.setText(text);
             }
             else if (DEAL_TYPE_EXPORT_DATA == mDealType)
             {
                 if(result)//有无数据可以导出
                 {
                     message = R.string.with_data_out;
                 }
                 else
                 {
                     message = R.string.no_data_out;
                 }

                 //对话框ok按钮点击事件
                 OnClickListener ok_export = new OnClickListener()
                 {

                     @Override
                     public void onClick(View v)
                     {
                         if(dialog!=null)
                         {
                             if(Tools.getUidskStatu())//U盘是否挂载
                             {
                                 if(!mIsHasData)//是否有数据可以导出
                                 {
                                     //提示无数据可以导出
                                     Toast.makeText(DrivingRecorderActivity.this, R.string.no_data, Toast.LENGTH_SHORT).show();
                                 }
                                 else
                                 {//导出数据
                                     text_message.setText(R.string.exporting);
                                     btn_ok.setEnabled(false);
                                     btn_ok.setTextColor(Color.GRAY);
                                     btn_cancel.setEnabled(false);
                                     btn_cancel.setTextColor(Color.GRAY);
                                     MyAsyncTask mat = new MyAsyncTask();
                                     mat.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, "start");
                                 }
                             }
                             else
                             {//提示插入U盘
                                 Toast.makeText(DrivingRecorderActivity.this, R.string.export_data_message, Toast.LENGTH_SHORT).show();
                             }
                         }

                     }
                 };

                 OnClickListener cancel_export = new OnClickListener()
                 {

                         @Override
                         public void onClick(View v) {
 //                            if(YXApplication.mGpio != null)
 //                            {
 //                                try {
 //                                    YXApplication.mGpio.PcUsbSet(0);
 //                                } catch (RemoteException e) {
 //                                    // TODO Auto-generated catch block
 //                                    e.printStackTrace();
 //                                }
 //                            }
                             if(dialog!=null)
                             {
                                 dialog.dismiss();
                                 dialog=null;
                             }
 //                            if(usbTask != null)
 //                            {
 //                                usbTask.cancel(true);
 //                            }

                         }
                     };


                 onClickExportOrPrintData(R.layout.export_print_data_dialog, message,
                         ok_export, cancel_export);
             }
         }


     }

     private class SetLoadStatus extends YXAsyncTask<Integer, Integer, Boolean>
     {
         public SetLoadStatus(Context context, View view, boolean isShowPrompt, String content)
         {
             super(context, view, isShowPrompt, content);
         }

         @Override
         protected Boolean doInBackground(Integer... params)
         {
             // TODO Auto-generated method stub
             if (null != params && params.length > 0)
             {
                return  mControlApi.setVehicleLoadStatus(params[0]);
             }
             return super.doInBackground(params);
         }

         @Override
         protected void onPostExecute(Boolean result)
         {
             // TODO Auto-generated method stub
             super.onPostExecute(result);

             if (null != result)
             {
                 if (result)
                 {
                     mAdapter.updataLoadText(load_type_str);//更新列表上的载重信息
                     last_load = load_type;
                     dialog.dismiss();
                     dialog=null;
                 }
                 else
                 {
                     System.out.println("set load status fail------"+last_load);
                     Toast.makeText(DrivingRecorderActivity.this, R.string.set_fal, Toast.LENGTH_SHORT).show();
                 }
             }
         }

     }
 }
