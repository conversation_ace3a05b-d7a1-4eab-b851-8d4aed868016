package com.yaxon.adapter;

import android.content.Context;

import androidx.databinding.DataBindingUtil;

import com.bumptech.glide.Glide;
import com.yaxon.base.R;
import com.yaxon.base.databinding.LayoutHotInfoItemBinding;
import com.yaxon.telematics.service.taxi.aidl.YxHotAreaInfo;
import com.yaxon.utils.HotInfoUtils;

import java.util.List;

public class HotInfoAdapter extends EmptyAdapter<YxHotAreaInfo>{
    public HotInfoAdapter(Context context, List<YxHotAreaInfo> listData) {
        super(context, listData);
    }

    @Override
    public int getItemViewId() {
        return R.layout.layout_hot_info_item;
    }

    @Override
    public void bindViewData(BindingHolder holder,
                             YxHotAreaInfo yxHotAreaInfo, int position) {

        LayoutHotInfoItemBinding binding = DataBindingUtil.getBinding(holder.itemView);
        binding.setAddress(yxHotAreaInfo.hotAreaName);
        binding.setCount(yxHotAreaInfo.hotAreaEmptyCar);
        int ret = HotInfoUtils.convertHotAreaIcon(yxHotAreaInfo.hotAreaIconUrl);
        if (ret == 0){
            Glide.with(getContext())
                    .load(yxHotAreaInfo.hotAreaIconUrl)
                    .error(R.drawable.icon_other)
                    .into(binding.ivTypeIcon);
        } else if (ret > 0){
            Glide.with(getContext())
                    .load(ret)
                    .into(binding.ivTypeIcon);
        }
        binding.executePendingBindings();
    }
}
