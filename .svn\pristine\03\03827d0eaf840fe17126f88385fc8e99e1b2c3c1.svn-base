package com.yaxon.utils;

import android.view.MotionEvent;
import android.view.View;

public final class ViewUtils {
    /**
     * 触摸点是否在该view的范围内
     *
     * @param view
     * @param ev
     * @return
     */
    public static boolean isInRangeOfView(View view, MotionEvent ev) {
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        int x_Pos = location[0];
        int y_Pos = location[1];
        if (ev.getX() < x_Pos
                || ev.getX() > (x_Pos + view.getWidth())
                || ev.getY() < y_Pos
                || ev.getY() > (y_Pos + view.getHeight())) {
            return false;
        }

        return true;
    }
}

