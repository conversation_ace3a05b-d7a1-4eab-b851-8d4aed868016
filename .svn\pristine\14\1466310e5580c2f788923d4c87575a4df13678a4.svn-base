<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="368dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/gztaxi_background_bg">
    <!-- 第一行 -->
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="fill_parent"
        android:layout_height="44dp"
        android:layout_gravity="center_vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            style="@style/GzTaxiCommText"
            android:text="@string/gztaxi_cotake_first" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="2dp"
            android:background="@drawable/gztaxi_cotake_taxi_icon" />

        <TextView
            android:id="@+id/first_realmoney"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            style="@style/MoneyText" />
    </LinearLayout>
    <!-- 第二行 -->

    <TextView
        android:layout_width="368dp"
        android:layout_height="2dp"
        android:background="@color/specialtitle" />

    <!-- 第三行 -->
    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="16dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/first_miles"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/TimeTextBlack"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/first_onemile_money"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/TimeTextBlack"
            android:layout_marginLeft="194dp" />
    </RelativeLayout>

    <!-- 第四行 -->
    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="16dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/first_waittime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/TimeTextBlack"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/first_saveoff"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/TimeTextBlack"
            android:layout_marginLeft="194dp" />
    </RelativeLayout>


    <!-- 第四行 -->
    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="16dp"
        android:orientation="horizontal"
        android:layout_marginBottom="10dp">

        <Button
            android:id="@+id/first_getoncar"
            android:layout_width="164dp"
            android:layout_height="50dp"
            android:gravity="center"
            android:background="@drawable/taxi_call_gztaxi_btn_x"
            android:onClick="onClick"
            style="@style/GzTaxiCommTitle"
            android:text="@string/gztaxi_dianzhao_getoncar"

            />

        <Button
            android:id="@+id/first_getoffcar"
            android:layout_width="164dp"
            android:layout_height="50dp"
            android:gravity="center"
            android:layout_marginLeft="8dp"
            android:background="@drawable/taxi_call_gztaxi_btn_x"
            android:onClick="onClick"
            style="@style/GzTaxiCommTitle"
            android:text="@string/gztaxi_dianzhao_getoffcar"

            />
    </LinearLayout>
</LinearLayout>