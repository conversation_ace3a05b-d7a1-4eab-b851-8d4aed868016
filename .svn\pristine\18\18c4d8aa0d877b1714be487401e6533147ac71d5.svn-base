package com.yaxon.yx_facedetect.proxy;

import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.util.Log;

import com.yaxon.yx_facedetect.FaceParams;
import com.yaxon.yx_facedetect.IFaceDetect;
import com.yaxon.yx_facedetect.IFaceInterface;
import com.yaxon.yx_facedetect.YxFaceParameterInfo;

/**
 * <AUTHOR> sprint
 * @date : 21-3-18下午2:49
 * @desc :虹软人脸识别
 */
public class YxAcrFaceServiceManager implements IFaceDetect {
    private static final String TAG = "YxAcrFaceServiceManager";
    private Context mContext;
    private IFaceInterface mFaceService;
    private static YxAcrFaceServiceManager mInstance;
    private final static String ACTION_FACE_SERVICE = "com.yaxon.yx_facedetect.IFaceInterface";
    private final static String FACE_SERVICE_PACKAGE = "com.yaxon.yx_facedetect";
    private boolean isUnbinding;

    private YxAcrFaceServiceManager() {

    }

    public static synchronized YxAcrFaceServiceManager getInstance() {
        if (mInstance == null) {
            mInstance = new YxAcrFaceServiceManager();
        }
        return mInstance;
    }

    private ServiceConnection connection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "onServiceConnected: ");
            mFaceService = IFaceInterface.Stub.asInterface(service);
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "onServiceDisconnected: ");
            mFaceService = null;
            if (!isUnbinding){
                bindService(mContext);
            }
        }
    };


    @Override
    public void bindService(Context context) {
        Log.d(TAG, "bindService: ");
        if (context == null) {
            return;
        }
        mContext = context.getApplicationContext();
        if (mContext != null) {
            Intent intent = new Intent(ACTION_FACE_SERVICE);
            intent.setPackage(FACE_SERVICE_PACKAGE);
            mContext.bindService(intent, connection, Service.BIND_AUTO_CREATE);
        }
    }

    @Override
    public void unBindService() {
        Log.d(TAG, "unBindService: ");
        try {
            if (mContext != null) {
                mContext.unbindService(connection);
                mContext = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void faceConfig(boolean detectTest, int idKeyCount, String appId, String apikey,
                           String secretkey, float similar_threshold) {
        Log.d(TAG, "faceConfig: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "faceConfig: interface is null");
                return;
            }
            mFaceService.faceConfig(detectTest, idKeyCount, appId, apikey, similar_threshold);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void activityFaceSdk() {
        Log.d(TAG, "activityFaceSdk: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "activityFaceSdk: interface is null");
                return;
            }
            mFaceService.activityFaceSdk();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startFaceRegister(boolean isTest, String driverName) {
        Log.d(TAG, "startFaceRegister: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "startFaceRegister: interface is null");
                return;
            }
            mFaceService.startFaceRegister(isTest, driverName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void registerFaceByImg(String filePath, String fileName) {
        Log.d(TAG, "registerFaceByImg: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "registerFaceByImg: interface is null");
                return;
            }
            mFaceService.registerFaceByImg(filePath, fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startTimerFace(YxFaceParameterInfo info) {
        Log.d(TAG, "startTimerFace: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "startTimerFace: interface is null");
                return;
            }
            mFaceService.startTimerFace(info);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startFaceService(String driverName, boolean isTest, FaceParams faceParams) {
        Log.d(TAG, "startFaceService: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "startFaceService: interface is null");
                return;
            }
            mFaceService.startFaceService(driverName, isTest, faceParams);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startFaceCountService(boolean isTest, boolean isCheck) {
        Log.d(TAG, "startFaceCountService: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "startFaceCountService: interface is null");
                return;
            }
            mFaceService.startFaceCountService(isTest, isCheck);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean checkRegisterDriver(String driverName) {
        Log.d(TAG, "checkRegisterDriver: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "checkRegisterDriver: interface is null");
                return false;
            }
            return mFaceService.checkRegisterDriver(driverName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void stopFaceDetect() {
        Log.d(TAG, "stopFaceDetect: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "stopFaceDetect: interface is null");
                return;
            }
            mFaceService.stopFaceDetect();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startFaceCountActivity() {
        Log.d(TAG, "startFaceCountActivity: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "startFaceCountActivity: interface is null");
                return;
            }
            mFaceService.startFaceCountActivity();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startFaceManagerActivity() {
        Log.d(TAG, "startFaceManagerActivity: ");
        try {
            if (mFaceService == null) {
                Log.e(TAG, "startFaceManagerActivity: interface is null");
                return;
            }
            mFaceService.startFaceManagerActivity();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setUnbinding(boolean isUndinding) {
        this.isUnbinding = isUndinding;
    }

    @Override
    public void removeRegisterFace(String fileName) {
        try {
            if (mFaceService == null) {
                Log.e(TAG, "removeRegisterFace: interface is null");
                return;
            }
            mFaceService.removeRegisterFace(fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getSpecifyAlgorithmCameraChannel(int type) {
        Log.e(TAG, "getSpecifyAlgorithmCameraChannel: function not implemented" );
        return -1;
    }

    @Override
    public boolean isActivate() {
        Log.e(TAG, "isActivate: the Interface Is Not Implemented" );
        return false;
    }

    @Override
    public String faceDetectByImg(String filePath, String fileName, boolean isFuzzyQuery) {

        Log.e(TAG, "faceDetectByImg" );
        return null;
    }

    @Override
    public String registerFaceByPath(String path) {

        Log.e(TAG, "registerFaceByPath" );

        return null;
    }
}
