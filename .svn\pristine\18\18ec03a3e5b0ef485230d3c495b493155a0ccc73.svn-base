package com.yaxon.utils;

public class ChString {
	public static final String Kilometer = "\u516c\u91cc";// "公里";
	public static final String Meter = "\u7c73";// "米";
	public static final String ByFoot = "\u6b65\u884c";// "步行";
	public static final String To = "\u53bb\u5f80";// "去往";
	public static final String Station = "\u8f66\u7ad9";// "车站";
	public static final String TargetPlace = "\u76ee\u7684\u5730";// "目的地";
	public static final String StartPlace = "\u51fa\u53d1\u5730";// "出发地";
	public static final String About = "\u5927\u7ea6";// "大约";
	public static final String Direction = "\u65b9\u5411";// "方向";

	public static final String GetOn = "\u4e0a\u8f66";// "上车";
	public static final String GetOff = "\u4e0b\u8f66";// "下车";
	public static final String Zhan = "\u7ad9";// "站";

	public static final String cross = "\u4ea4\u53c9\u8def\u53e3"; // 交叉路口
	public static final String type = "\u7c7b\u522b"; // 类别
	public static final String address = "\u5730\u5740"; // 地址
	public static final String PrevStep = "\u4e0a\u4e00\u6b65";
	public static final String NextStep = "\u4e0b\u4e00\u6b65";
	public static final String Gong = "\u516c\u4ea4";
	public static final String ByBus = "\u4e58\u8f66";
	public static final String Arrive = "\u5230\u8FBE";// 到达
}
