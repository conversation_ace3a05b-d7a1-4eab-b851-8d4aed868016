package com.yaxon.business.home.fragment;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.OvalShape;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.PopupWindow;
import android.widget.SeekBar;

import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.yaxon.adapter.BaseRVAdapter;
import com.yaxon.adapter.SongListAdapter;
import com.yaxon.base.R;
import com.yaxon.base.common.BaseFragment;
import com.yaxon.base.databinding.FragmentMediaBinding;
import com.yaxon.business.home.player.Player;
import com.yaxon.business.home.viewmodel.HomeViewModel;
import com.yaxon.business.home.player.PlayerProxy;
import com.yaxon.business.home.player.PlayerType;
import com.yaxon.common.CommDef;
import com.yaxon.common.LogTag;
import com.yaxon.datasource.sharepreference.SharedPrefsManager;
import com.yaxon.datasource.sharepreference.SharedPrefsTag;
import com.yaxon.musicplayer.model.MusicInfo;
import com.yaxon.utils.GlideCircleTransform;
import com.yaxon.utils.LoggerUtil;
import com.yaxon.utils.MusicUtil;
import com.yaxon.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

public class MediaFragment extends BaseFragment<FragmentMediaBinding, HomeViewModel> {
    private static List<MusicInfo> songLists = new ArrayList<>();

    private volatile int isPlayingIndexSong = -1;
    private MusicInfo curMusicInfo = null;
    private Animation ivAnimation;
    private PopupWindow songListWindow;
    private SongListAdapter songListAdapter;
    private RecyclerView rvSongList;

    public static MediaFragment getInstance(){
        return new MediaFragment();
    }
    @Override
    public void bindingVariable() {
        getBinding().setClickEvent(new ClickEvent());
    }

    @Override
    public HomeViewModel initViewModel() {
        return createActivityViewModel(getActivity(), HomeViewModel.class);
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void initParam() {
        initViews();
        /** 更新多媒体类型 */
        updateMediaType();
        /** 更新多媒体状态 */
        updateMediaState();
        /** 处理观察事件 */
        dealObserveEvent();
        /** 更新歌曲 */
        if (PlayerProxy.getInstance().getPlayerType() == PlayerType.MP3){
            getViewModel().updateSongList();
        }
    }

    /**
     * 初始化View
     */
    private void initViews(){
        ivAnimation = AnimationUtils.loadAnimation(getActivity().getApplicationContext(),
                R.anim.set_image_rotate);

        getBinding().sbSongProgress.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress,
                                          boolean fromUser) {
                if (fromUser){
                    PlayerProxy.getInstance().seekTo(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
    }

    /**
     * 切换多媒体类型
     */
    private void updateMediaType(){
        if (SharedPrefsManager.getInt(SharedPrefsTag.KEY_MEDIA_TYPE) == PlayerType.FM.getType()){
            getBinding().rbFM.setChecked(true);
        } else {
            getBinding().rbMP3.setChecked(true);
        }
    }
    /**
     * 更新多媒体状态
     */
    private void updateMediaState(){
        if (SharedPrefsManager.getInt(SharedPrefsTag.KEY_MEDIA_TYPE) != PlayerType.FM.getType()){
            getBinding().setMediaType(PlayerType.MP3);
            PlayerProxy.getInstance().setPlayer(PlayerType.MP3);

            isPlayingIndexSong = PlayerProxy.getInstance().getCurrentPlayingIndex();
            if (isPlayingIndexSong >= songLists.size() || isPlayingIndexSong < 0){
                isPlayingIndexSong = 0;
                PlayerProxy.getInstance().setCurrentPlayingIndex(0);
            }

            if (songLists.size() > 0){
                curMusicInfo = songLists.get(isPlayingIndexSong);

                if (curMusicInfo == null){
                    LoggerUtil.e(getCurTag(), "err, the curMusicInfo is null");
                    return;
                }

                if (!curMusicInfo.isValidInfo()){
                    MusicUtil.getFullInfoWithPath(curMusicInfo);
                }

                int curProgress = PlayerProxy.getInstance().getCurrentPlayingPosition();
                //如果当前歌曲为空或是当前进度大于总的长度时，则将当前进度置0
                if (curMusicInfo == null || curProgress > curMusicInfo.durarion){
                    curProgress = 0;
                }

                getBinding().setMusicInfo(curMusicInfo);
                getBinding().setProgress(curProgress);
                getBinding().setMaxProgress(curMusicInfo.durarion);
                Drawable dAlbum = MusicUtil.getAlbumImage(curMusicInfo.albumId);
                setAlbumBackgroud(dAlbum);

                String lastSong = "";
                String nextSong = "";


                if (isPlayingIndexSong > 0){
                    MusicInfo lastInfo = songLists.get(isPlayingIndexSong - 1);
                    if (lastInfo != null){
                        lastSong = lastInfo.title;
                    }
                }

                if (isPlayingIndexSong < songLists.size() - 1){
                    MusicInfo nextInfo = songLists.get(isPlayingIndexSong + 1);

                    if (nextInfo != null){
                        nextSong = nextInfo.title;
                    }
                }

                getBinding().setLastSong(StringUtil.isNotNullOrEmpty(lastSong) ? "上一曲  :  " + lastSong : "");
                getBinding().setNextSong(StringUtil.isNotNullOrEmpty(nextSong) ? "下一曲  :  " + nextSong : "");
            } else {
                getBinding().setMusicInfo(null);
                getBinding().setProgress(0);
                getBinding().setLastSong("");
                getBinding().setNextSong("");
            }
        } else {
            stopMusicAnimation();
            PlayerProxy.getInstance().setPlayer(PlayerType.FM);
            getBinding().setMediaType(PlayerType.FM);
        }
    }

    /**
     * 设置歌曲的专辑背景图片
     * @param drawable
     */
    private void setAlbumBackgroud(Drawable drawable){
        Glide.with(getActivity().getApplicationContext())
                .load(drawable)
                .transform(new GlideCircleTransform())
                .into(getBinding().ivMusic);
    }
    /**
     * 处理观察事件
     */
    private void dealObserveEvent(){
        getViewModel().timer.observe(this, new Observer<Long>() {
            @Override
            public void onChanged(Long aLong) {
                if (PlayerProxy.getInstance().getPlayerType() == PlayerType.MP3){
                    if (PlayerProxy.getInstance().isPlaying() == Player.ERR_SUCCESS){
                        int curProgress = PlayerProxy.getInstance().getCurrentPlayingPosition();
                        getBinding().setProgress(curProgress);
                    }
                }
            }
        });

        getViewModel().mediaType.observe(this, new Observer<PlayerType>() {
            @Override
            public void onChanged(PlayerType playerType) {
                LoggerUtil.printLog(LoggerUtil.PRINT_DBG, LogTag.TAG_MP3, "【mediaType】 :"+ playerType);
                updateMediaType();
                updateMediaState();
                if (playerType == PlayerType.FM){
                    stopMusicAnimation();
                }
            }
        });

        getViewModel().songLists.observe(this, new Observer<List<MusicInfo>>() {
            @Override
            public void onChanged(List<MusicInfo> musicInfos) {
                songLists.clear();

                if (musicInfos != null && musicInfos.size() > 0){
                    songLists.addAll(musicInfos);
                }

                int beSelectedPos = PlayerProxy.getInstance().getCurrentPlayingIndex();
                if (songListAdapter != null){
                    songListAdapter.setSelectedPos(beSelectedPos);
                }

                updateMediaState();
            }
        });

        //监听fragment的切换
        getViewModel().pagerSwitcher.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                LoggerUtil.printLog(LoggerUtil.PRINT_DBG, LogTag.TAG_MP3, "【pagerSwitcher】 :"+ integer);

                if (integer == CommDef.TAG_MEDIA_FRAGMENT){
                    if (PlayerProxy.getInstance().getPlayerType() == PlayerType.MP3){
                        getViewModel().updateSongList();
                        if (PlayerProxy.getInstance().isPlaying() == Player.ERR_SUCCESS){
                            startMusicAnimation();
                        }
                    }
                } else {
                    hideSongListWindow();
                    stopMusicAnimation();
                }
            }
        });

        getViewModel().isMusicPlaying.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                LoggerUtil.printLog(LoggerUtil.PRINT_DBG, LogTag.TAG_MP3, "【isMusicPlaying】 :" + aBoolean);

                if (PlayerProxy.getInstance().getPlayerType() == PlayerType.MP3){
                    if (aBoolean){
                        startMusicAnimation();
                    } else {
                        stopMusicAnimation();
                    }
                }
            }
        });
    }

    /**
     * 展示歌曲列表
     */
    private void showSongListWindow() {
        if (songListWindow == null) {
            LayoutInflater inflater =
                    (LayoutInflater) getActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            View view = inflater.inflate(R.layout.layout_song_list, null);
            rvSongList =
                    (RecyclerView) view.findViewById(R.id.rvSongList);
            LinearLayoutManager llm =
                    new LinearLayoutManager(getActivity().getApplicationContext(),
                            LinearLayoutManager.VERTICAL, false);
            songListAdapter = new SongListAdapter(getActivity().getApplicationContext(),
                    songLists);
            songListAdapter.setOnItemClickListener(new BaseRVAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(View v, int position) {
                    if (PlayerProxy.getInstance().getPlayerType() == PlayerType.MP3){
                        if (songLists == null || position >= songLists.size()){
                            LoggerUtil.e(getCurTag(), "err, songLists == null || position >= songLists.size()");
                            return;
                        }

                        int stopRet = PlayerProxy.getInstance().doStop();
                        if (stopRet != Player.ERR_SUCCESS){
                            return;
                        }

                        MusicInfo curInfo = songLists.get(position);
                        int setRet = PlayerProxy.getInstance().setCurrentPlayingIndex(position);
                        if (setRet != Player.ERR_SUCCESS){
                            return;
                        }

                        int playRet = PlayerProxy.getInstance().doPlay(curInfo.path);
                        if (playRet != Player.ERR_SUCCESS){
                            return;
                        }
                        updateMediaState();
                        getViewModel().isMusicPlaying.setValue(true);
                    }
                }
            });
            rvSongList.setAdapter(songListAdapter);
            rvSongList.setLayoutManager(llm);
            songListWindow = new PopupWindow(view,
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    WindowManager.LayoutParams.WRAP_CONTENT);
        }

        if (rvSongList != null){
            rvSongList.scrollToPosition(songListAdapter.getBeSelectedPos());
        }

        //解决PX3点击屏幕其它地方弹出框不消失
        songListWindow.setBackgroundDrawable(getDrawable());//设置背景透明以便点击外部消失
        songListWindow.setFocusable(true);
        songListWindow.setOutsideTouchable(true);  //设置点击屏幕其它地方弹出框消失
        songListWindow.showAtLocation(getLayoutInflater().inflate(R.layout.fragment_media, null), Gravity.RIGHT, 0, 0);
    }

    /**
     * 生成一个 透明的背景图片
     */
    private Drawable getDrawable(){
        ShapeDrawable bgdrawable =new ShapeDrawable(new OvalShape());
        bgdrawable.getPaint().setColor(getResources().getColor(android.R.color.transparent));
        return   bgdrawable;
    }

    @Override
    public void onStop() {
        super.onStop();
        hideSongListWindow();
    }

    /**
     * 隐藏歌曲列表
     */
    private void hideSongListWindow(){
        if (songListWindow != null && songListWindow.isShowing()){
            songListWindow.dismiss();
        }
    }

    /**
     * 停止动画
     */
    private void stopMusicAnimation(){
        getBinding().ivMusic.clearAnimation();
    }

    /**
     * 启动音乐动画
     */
    private void startMusicAnimation(){
        if (getBinding().ivMusic.getAnimation() == null){
            getBinding().ivMusic.startAnimation(ivAnimation);
        }
    }

    @Override
    public int layoutId() {
        return R.layout.fragment_media;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        hideSongListWindow();
        stopMusicAnimation();
    }

    @Override
    protected String getCurTag() {
        return MediaFragment.class.getSimpleName();
    }

    public class ClickEvent {
        public void clickBack() {
            getViewModel().pagerSwitcher.setValue(CommDef.TAG_HOME_FRAGMENT);
        }

        public void clickRightMenu() {
            getViewModel().updateSongList();
            showSongListWindow();
        }
    }
}
