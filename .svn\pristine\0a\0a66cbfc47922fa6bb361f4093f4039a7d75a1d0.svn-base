package com.yaxon.adapter;

import android.content.Context;
import android.view.View;

import androidx.databinding.DataBindingUtil;

import com.yaxon.base.R;
import com.yaxon.base.databinding.LayoutRvRadioItemBinding;
import com.yaxon.business.home.viewmodel.RadioChannelInfo;
import com.yaxon.utils.LoggerUtil;
import com.yaxon.utils.StringUtil;

import java.util.List;

public class RadioAdapter extends BindingAdapter<RadioChannelInfo>{
    private static final String TAG = "RadioAdapter";
    private int beSelectedId = -1;
    private int beSelectedChannel;

    public RadioAdapter(Context context, List<RadioChannelInfo> listData) {
        super(context, listData);
    }

    @Override
    public int getConvertViewId() {
        return R.layout.layout_rv_radio_item;
    }

    @Override
    public void bindViewDatas(BindingHolder holder,
                              RadioChannelInfo radioChannelInfo, int position) {
        LayoutRvRadioItemBinding vdb = DataBindingUtil.getBinding(holder.itemView);

        if (beSelectedChannel == radioChannelInfo.getChannel()){
            vdb.setBeSelected(true);
        } else {
            vdb.setBeSelected(false);
        }
        String mFreq = StringUtil.toStrAndKeepOneAfterApoint(radioChannelInfo.getChannel());
        vdb.setContent(mFreq);
        vdb.executePendingBindings();
    }

    @Override
    protected void onItemClick(View v, int position) {
        notifyPartDataChange(position);
        super.onItemClick(v, position);
    }

    /**
     * 通知局部数据变化
     * @param toPos
     */
    private void notifyPartDataChange(int toPos){
        beSelectedChannel = getListData().get(toPos).getChannel();
        notifyItemChanged(beSelectedId);
        beSelectedId = toPos;
        notifyItemChanged(beSelectedId);
    }
    /**
     * 设置被选中的频道信息
     * @param channel
     */
    public void setSelectedInfo(int channel){
        if (channel <= 0){
            LoggerUtil.e(TAG, "err, the channel is invalid");
            return;
        }

        int toBeSelectedId = -1;
        for (int i = 0; i < getListData().size(); i++){
            RadioChannelInfo curInfo = getListData().get(i);
            if (channel == curInfo.getChannel()){
                toBeSelectedId = i;
                beSelectedChannel = channel;
                break;
            }
        }
        if (toBeSelectedId >= 0){
            notifyPartDataChange(toBeSelectedId);
        }
    }

    /**
     * 获取被选中的信息
     * @return
     */
    public int getBeSelectedChannel(){
        return beSelectedChannel;
    }

    /**
     * 更新收音机数据
     * @param infos
     */
    public void updateData(List<RadioChannelInfo> infos){
        if (infos == null){
            return;
        }
        int curSize = getListData().size();
        notifyItemRangeRemoved(0, curSize);
        getListData().clear();
        getListData().addAll(infos);
        notifyItemRangeChanged(0, infos.size());
    }
}
