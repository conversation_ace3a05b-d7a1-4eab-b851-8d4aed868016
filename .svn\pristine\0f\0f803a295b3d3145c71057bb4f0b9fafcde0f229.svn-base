package com.yaxon.datasource.database;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;

import com.yaxon.telematics.service.aidl.main.QuestionInfo;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

/**
 * 中心提问数据库管理类
 * <AUTHOR>
 * @version 2013-4-28
 */
public class QuestionDBManager 
{
	private static String TAG="QuestionDBManager";
	private static QuestionDBManager mInstance;
	private DBHelper helper;//数据库服务类
	private SQLiteDatabase db;
	private int maxOldIndex=0;
	
	private HashMap<Integer,ArrayList<String> > mAnswerMap=new HashMap<Integer,ArrayList<String> >();
	
	private static final int MAX_QUESTION_ITEM=10;
	
	private QuestionDBManager(Context context)
	{
		helper=DBHelper.getInstance(context);
		db=helper.getWritableDatabase();
	}

	public static QuestionDBManager getInstance(Context context){
		if (mInstance == null){
			synchronized (QuestionDBManager.class){
				if (mInstance == null){
					mInstance = new QuestionDBManager(context);
				}
			}
		}
		return mInstance;
	}
	/**
	 * 保存中心提问信息
	 * @param questionInfo
	 */
	public void saveQuestion(QuestionInfo questionInfo)
	{
		String sql="insert into question(question_index,question_content,answer_content,is_answer,rec_time) values (?,?,?,?,?)";
		
		try
		{
			if(db==null)
				return;
			
			if( isQuestionExist(db,questionInfo.mQuestionIdx) )
			{
				Log.i(TAG, "saveQuestion: QuestionIndex already exist:" + questionInfo.mQuestionIdx);
				return;
			}
			
			//查询数据库中已保存的个数
			int count=queryQuestionCount(db);
			if(count==-1)
			{
				return;//保存出错
			}
			if(count>=MAX_QUESTION_ITEM)//已满时
			{
				deleteOldQuestion(db);
			}
			//插入新的中心提问信息
			String answerContent="";
			int len=questionInfo.mAnswers.size();
			for(int i=0;i<len;i++)
			{
				if(i==len-1)
				{
					answerContent+=questionInfo.mAnswers.get(i);
				}
				else
				{
					answerContent+=questionInfo.mAnswers.get(i)+",";
				}
			}
			Object[] object=new Object[]
			                           {
					questionInfo.mQuestionIdx,
					questionInfo.mQuestionContent,
					answerContent,
					-1,
					questionInfo.recTime};
			//插入
			db.execSQL(sql, object);
			
			//将问题答案插入答案列表
			String ansSql="insert into question_ans(question_index,answer_content,answer_index) values(?,?,?)";
			for(int i=0;i<questionInfo.mAnswers.size();i++)
			{
				String strIndex=questionInfo.mAnswers.get(i).substring(0, 2);
				int ansIndex=Integer.parseInt(strIndex,16);
				//String strContent=questionInfo.mAnswers.get(i).substring(3);
				String strContent=questionInfo.mAnswers.get(i);
												
				Object[] obj=new Object[]{questionInfo.mQuestionIdx,strContent,ansIndex};
				db.execSQL(ansSql, obj);
			}
			
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
	}
	
	/**
	 * 查询已保存的个数
	 * @param db
	 * @return
	 */
	public int queryQuestionCount(SQLiteDatabase db)
	{
		String sql="select * from question";
		try
		{
			Cursor cursor=db.rawQuery(sql, null);
			if(cursor.getCount()>=MAX_QUESTION_ITEM)
			{
				cursor.moveToFirst();
				maxOldIndex=cursor.getInt(0);
			}
			cursor.close();
			return cursor.getCount();
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
		return -1;
	}
	
	/**
	 * 删除老数据
	 * @param db
	 */
	public void deleteOldQuestion(SQLiteDatabase db)
	{
		String sql="delete from question where question_index=?";
		try
		{
			db.execSQL(sql,new Object[]{maxOldIndex});
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
		
		//同时删除答案表中的答案
		sql="delete from question_ans where question_index=?";
		try
		{
			db.execSQL(sql,new Object[]{maxOldIndex});
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}		
		
	}
	
	//将数据库中所有答案提取出来，方便后续查询
	private void queryAllAnswers()
	{
		mAnswerMap.clear();
		
//		SQLiteDatabase tmpDb=helper.getWritableDatabase();
//		if(null==tmpDb)
//			return;
		
		try
		{
			String sql="select * from question_ans";
			Cursor cursor=db.rawQuery(sql, null);
			if(cursor==null)
				return;
			
			//int ansIdx;//答案索引
			int qIndex;//问题流水号
			String ansContent;//答案内容
			ArrayList<String> tmpArray=new ArrayList<String>();
			while(cursor.moveToNext())
			{
				qIndex=cursor.getInt(0);
				ansContent=cursor.getString(1);
				//ansIdx=cursor.getInt(2);
				tmpArray=mAnswerMap.get(qIndex);
				if(null==tmpArray)
					tmpArray=new ArrayList<String>();
				
				tmpArray.add(ansContent);
				mAnswerMap.put(qIndex, tmpArray);
				
			}
			
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
//		finally
//		{
//			if(tmpDb!=null)
//			{
//				tmpDb.close();
//				tmpDb=null;
//			}
//		}
	}
	
	private void queryAnswers(Question q)
	{
		queryAllAnswers();
        
		ArrayList<String> ansArray;
		ansArray=mAnswerMap.get(q.questionIndex);
		if(ansArray==null)
		{
			Log.i(TAG, "question index not found in Answer table");
			return;
		}
	    
		String[] tmp=new String[ansArray.size()];		
		String[] strAns=ansArray.toArray(tmp);
		
		q.answers=strAns;			
	}
	
	/**
	 * 查询所有中心提问
	 * @return
	 */
	public ArrayList<Question> queryAllQuestion()
	{
		String sql="select * from question";
		ArrayList<Question> infos=new ArrayList<Question>();
		try
		{
			//db=helper.getWritableDatabase();
			Cursor cursor=db.rawQuery(sql, null);
			while(cursor.moveToNext())
			{
				Question question=new Question();
				question.questionIndex=cursor.getInt(0);
				question.questionContent=cursor.getString(1);				
				question.isAnswer=cursor.getInt(3);
				question.recTime=cursor.getString(4);
				queryAnswers(question);
				if(question.answers==null || question.answers.length<1)
					question.answers=cursor.getString(2).split(",");
												
				infos.add(question);
			}
			cursor.close();
			
			Collections.reverse(infos);
			
			return infos;
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
//		finally
//		{
//			if(db!=null)
//				db.close();
//		}
		return null;
	}
	
	/**
	 * 更新问题
	 * @param questionIdx
	 * @param answerIdx
	 */
	public void updateQuestion(int questionIdx,int answerIdx)
	{
		String sql="update question set is_answer="+answerIdx+" where question_index="+questionIdx;
		try
		{
			//db=helper.getWritableDatabase();
			db.execSQL(sql);
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
//		finally
//		{
//			if(db!=null)
//				db.close();
//		}
	}
	
	/**
	 * 指定索引的问题是否已经存在
	 * @param db
	 * @return
	 */
	public boolean isQuestionExist(SQLiteDatabase db,int quesIndex)
	{
		boolean result=false;
		
		String sql="select * from question where question_index=" + quesIndex;
		try
		{
			Cursor cursor=db.rawQuery(sql, null);

			if(cursor==null)
				return false;
			
			if(cursor.getCount()>0)
				result=true;
			
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
		
		return result;
	}

	/**
	 * 关闭数据库
	 */
	public void closeDB(){
		if (db != null){
			db.close();
		}
	}
}
