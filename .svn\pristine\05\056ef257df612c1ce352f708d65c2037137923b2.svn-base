<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/main_background"
    android:orientation="horizontal" >

    <LinearLayout
        android:id="@+id/title_bar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:orientation="horizontal" >

        <include layout="@layout/main_title_simple" >
        </include>
    </LinearLayout>

    <Spinner
        android:id="@+id/spinner_com"
        android:layout_width="150dp"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/title_bar"
        android:layout_marginLeft="40dp"
        android:layout_marginTop="76dp" />

    <RadioGroup
        android:id="@+id/radioGroup1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/spinner_com"
        android:layout_marginLeft="24dp"
        android:layout_toRightOf="@+id/spinner_com" >

        <RadioButton
            android:id="@+id/radio0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/project_menu_rk_debug_enable_log" />

        <RadioButton
            android:id="@+id/radio1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/project_menu_rk_debug_disable_log" />
    </RadioGroup>

    <RadioGroup
        android:id="@+id/radioLockMenu"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignTop="@id/radioGroup1"
        android:layout_marginRight="96dp" >

        <RadioButton
            android:id="@+id/radioLock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/project_menu_rk_debug_restore" />

        <RadioButton
            android:id="@+id/radioUnlock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/project_menu_rk_debug_unlock_menu" />
    </RadioGroup>

    <TextView
        android:id="@+id/textView1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/radioLockMenu"
        android:layout_marginRight="34dp"
        android:layout_marginTop="28dp"
        android:layout_toLeftOf="@+id/radioLockMenu"
        android:text="@string/project_menu_rk_debug_menu"
        android:textAppearance="?android:attr/textAppearanceLarge" />
    
    <!-- Com2模式、省电模式 -->
    <LinearLayout    
        android:id="@+id/debug_layout_com2_mode"
        android:layout_alignParentTop="true"
        android:layout_marginTop="280dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"        
        android:orientation="horizontal" >

	    <TextView
	        android:id="@+id/textCom2Mode"
	        android:layout_width="wrap_content"
	        android:layout_height="wrap_content"
	        android:layout_marginLeft="40dp"	        
	        android:text="@string/project_menu_rk_debug_com2_mode"
	        android:textAppearance="?android:attr/textAppearanceLarge" />
	    
	    <RadioGroup
	        android:id="@+id/radioGpCom2Mode"
	        android:layout_width="wrap_content"
	        android:layout_height="wrap_content"
	        android:layout_marginLeft="20dp"
	        android:layout_marginRight="96dp" >
	
	        <RadioButton
	            android:id="@+id/radioCom2Debug"
	            android:layout_width="wrap_content"
	            android:layout_height="wrap_content"
	            android:text="@string/project_menu_rk_debug_com2_mode_debug" />
	
	        <RadioButton
	            android:id="@+id/radioCom2Peripheral"
	            android:layout_width="wrap_content"
	            android:layout_height="wrap_content"
	            android:text="@string/project_menu_rk_debug_com2_mode_peripheral" />
	    </RadioGroup>	    
        
	    <!--省电模式-->
	    <TextView
	        android:id="@+id/debug_txt_power_mode"
	        android:layout_width="wrap_content"
	        android:layout_height="wrap_content"
	        android:layout_marginLeft="40dp"	        
	        android:text="@string/project_menu_rk_debug_power_mode"
	        android:textAppearance="?android:attr/textAppearanceLarge" />
	    	    
	    <RadioGroup
	        android:id="@+id/radioGpPowerMode"
	        android:layout_width="wrap_content"
	        android:layout_height="wrap_content"
	        android:layout_marginLeft="10px" >
	
	        <RadioButton
	            android:id="@+id/radioPowerModeEnable"
	            android:layout_width="wrap_content"
	            android:layout_height="wrap_content"
	            android:text="@string/project_menu_rk_debug_power_mode_enable" />
	
	        <RadioButton
	            android:id="@+id/radioPowerModeDisable"
	            android:layout_width="wrap_content"
	            android:layout_height="wrap_content"
	            android:text="@string/project_menu_rk_debug_power_mode_disable" />
	    </RadioGroup>	
	    	    
    </LinearLayout>       
    

</RelativeLayout>