package com.yaxon.telematics.service.protocol.takepic;

import com.yaxon.telematics.service.CommServiceDefine;
import com.yaxon.telematics.service.aidl.main.PhotoFileInfo;
import com.yaxon.telematics.service.aidl.main.PhotoQueryInfo;
import com.yaxon.telematics.service.communicate.MonitorIndustryCommunicateManager;
import com.yaxon.telematics.service.protocol.monitor.MediaInfoManage;
import com.yaxon.telematics.service.protocol.monitor.MonitorProtocolDealBase;
import com.yaxon.telematics.service.util.DataTransformer;
import com.yaxon.telematics.service.util.LogUtil;
import com.yaxon.telematics.service.util.ProtocolDataQueueElement;

public class MonitorTakePicFunciton extends MonitorProtocolDealBase {

    private static final String TAG = "MonitorTakePicFunciton";
    private static MonitorTakePicFunciton mMonitorTakePicFunciton = null;

    static {
        mMonitorTakePicFunciton = new MonitorTakePicFunciton();
    }

    private MonitorTakePicFunciton() {
        super();
    }

    public static MonitorTakePicFunciton getInstance() {
        if (null == mMonitorTakePicFunciton) {
            mMonitorTakePicFunciton = new MonitorTakePicFunciton();
        }
        return mMonitorTakePicFunciton;
    }

    @Override
    public void dealProtocol(
            ProtocolDataQueueElement protocolDataQueueElement) {
        // TODO Auto-generated method stub
        if (null == protocolDataQueueElement) {
            return;
        }

        switch (protocolDataQueueElement.mMsgId) {
            //拍照请求/应答（90H）
            case CommServiceDefine.PROTOCOL_MONITOR_TAKE_PIC_REQUEST: {
                byte[] centerData = protocolDataQueueElement.mData;
                dealStartTakePic(centerData);
            }
            break;
            //拍照文件数据读取请求/应答（92H）
            case CommServiceDefine.PROTOCOL_MONITOR_TAKE_PIC_DATA_REQUEST: {
                byte[] centerData = protocolDataQueueElement.mData;
                dealReadPicFile(centerData);
            }
            break;
            //获取照片文件信息请求/应答（93H）
            case CommServiceDefine.PROTOCOL_MONITOR_GET_PIC_FILE_INFO: {
                byte[] centerData = protocolDataQueueElement.mData;
                dealGetPicFileInfo(centerData);
            }
            break;
            //根据条件检索照片文件索引号信息请求/应答（94H）
            case CommServiceDefine.PROTOCOL_MONITOR_GET_PIC_FILE_INDEX: {
                byte[] centerData = protocolDataQueueElement.mData;
                dealSearchPicFileIndex(centerData);
            }
            break;
            default:
                break;
        }
    }


    public void dealStartTakePic(byte[] userData) {
        if (userData == null) {
            LogUtil.printLog(TAG, "dealStartTakePic::userData == null");
            return;
        }

        LogUtil.printLogHex(TAG, "dealStartTakePic", userData, userData.length);

        if (userData.length != 37) {
            LogUtil.printLog(TAG, "dealStartTakePic::userData.length != 37 " + userData.length);
            return;
        }
        int currentIndex = 0;
        //摄像头ID	1字节
        int cameraID = DataTransformer.getUnsignedByte(userData[currentIndex++]);
        //拍照数量	1字节
        int takePicNum = DataTransformer.getUnsignedByte(userData[currentIndex++]);
        //拍照间隔	1字节
        int takePicInterval = DataTransformer.getUnsignedByte(userData[currentIndex++]);

        //时间	6字节
        byte year = userData[currentIndex++];// 年
        byte month = userData[currentIndex++];// 月
        byte day = userData[currentIndex++];// 日
        byte hour = userData[currentIndex++];// 时
        byte minute = userData[currentIndex++];// 分
        byte second = userData[currentIndex++];// 秒

        // 经纬度        
        byte[] lanBytes = new byte[]{0x00, 0x00, 0x00, 0x00};
        byte[] lonBytes = new byte[]{0x00, 0x00, 0x00, 0x00};
        System.arraycopy(userData, currentIndex, lanBytes, 0, lanBytes.length);
        currentIndex += lanBytes.length;
        System.arraycopy(userData, currentIndex, lonBytes, 0, lonBytes.length);
        currentIndex += lonBytes.length;
        //将经纬度转成整型
        int lan = DataTransformer.getDWord(lanBytes[0],
                lanBytes[1], lanBytes[2], lanBytes[3]);
        int lon = DataTransformer.getDWord(lonBytes[0],
                lonBytes[1], lonBytes[2], lonBytes[3]);
        // 转换为度分格式:ddmm.mmmm
       /* double lan_double= DataTransformer.getUnsignedByte(lanBytes[0]) * 100 
                        + DataTransformer.getUnsignedByte(lanBytes[1])
                        + (double)DataTransformer.getUnsignedByte(lanBytes[2]) / 100 
                        + (double)DataTransformer.getUnsignedByte(lanBytes[3]) / (100 * 100);
                        
        double lon_double = DataTransformer.getUnsignedByte(lonBytes[0]) * 100 
                        + DataTransformer.getUnsignedByte(lonBytes[1])
                        + (double)DataTransformer.getUnsignedByte(lonBytes[2]) / 100 
                        + (double)DataTransformer.getUnsignedByte(lonBytes[3]) / (100 * 100);*/

        // 转换为ddmm.mmmm格式的字符串
        /*String lanString = String.format("%.4f", lan);
        String lonString = String.format("%.4f", lon);*/


        // 速度：取值范围00H~FFH（单位：公里/小时）
        int speed = DataTransformer.getUnsignedByte(userData[currentIndex++]);

        // 方向：取值范围00H~78H（120），换算公式：相对正北方向的偏移角度/3+1，舍弃小数部分        
        byte directionPosByte1 = userData[currentIndex++];
        byte directionPosByte2 = userData[currentIndex++];
        //转成整型        
        int direction = DataTransformer.getWord(directionPosByte1, directionPosByte2);
        //int direction = DataTransformer.getUnsignedByte(userData[currentIndex++]);   
        // 转换后的方向
        //int transDirection = (direction -1) * 3; 


        //海拔
        byte elevationPosByte1 = userData[currentIndex++];
        byte elevationPosByte2 = userData[currentIndex++];
        //转成整型        
        int elevation = DataTransformer.getWord(elevationPosByte1, elevationPosByte2);
        //int elevation = DataTransformer.getUnsignedByte(userData[currentIndex++]);  

        // 报警信息
        byte[] alarmData = new byte[4];
        System.arraycopy(userData, currentIndex, alarmData, 0, alarmData.length);
        currentIndex += 4;
        int alarm = DataTransformer.getDWord(alarmData[0],
                alarmData[1], alarmData[2], alarmData[3]);
        // 写入系统属性
        /*String alarmString = new String(alarmData);
        SystemProperties.set(PROPERTY_ALARM_INFO, alarmString);*/

        // 传感器状态
        byte[] sensorData = new byte[4];
        System.arraycopy(userData, currentIndex, sensorData, 0, sensorData.length);
        currentIndex += 4;
        int sensor = DataTransformer.getDWord(sensorData[0],
                sensorData[1], sensorData[2], sensorData[3]);
        // 写入系统属性
        /*String sensorString = new String(sensorData);
        SystemProperties.set(PROPERTY_SENSOR_INFO, sensorString);*/

        //拍照事件
        byte takePicEvent = userData[currentIndex++];

        /**
         * 拍照分辨率
         */
        byte takePicResolution = userData[currentIndex++];

        /**
         * 拍照画质
         */
        byte takePicQqulity = userData[currentIndex++];

        /**
         * 拍照亮度
         */
        byte takePicBrightness = userData[currentIndex++];

        /**
         * 对比度
         */
        byte takePicContrast = userData[currentIndex++];
        //饱和度
        byte takePicSaturation = userData[currentIndex++];
        //色度
        byte takePicChroma = userData[currentIndex++];

        if (!ROMRemainingSpace.ISSpaceAvailable()) {
            //空间处理
        }

        PhotoFileInfo pfi = new PhotoFileInfo(cameraID, year, month, day, hour, minute, second, lon, lan, speed, direction, elevation, alarm, sensor, takePicEvent, takePicResolution, takePicQqulity, takePicBrightness, takePicContrast, takePicSaturation, takePicChroma);

        //启动拍照
        CameraUtil TakePicUtil = CameraUtil.getInstance();
        TakePicUtil.setmMonitorProtocolDealBase(this);
        TakePicUtil.startTakePic(pfi, takePicNum, takePicInterval);
    }

    /**
     * 响应启动拍照应答
     *
     * @param type
     */
    public void responseStartTakePic(byte type) {
        byte[] sendUserData = new byte[1];
        byte[] sendProtocolData = null;
        sendUserData[0] = type;
        sendProtocolData = makeProtocol(CommServiceDefine.PROTOCOL_MONITOR_TAKE_PIC_REQUEST,
                sendUserData);
        if (null != sendProtocolData) {
            MonitorIndustryCommunicateManager.sendData(sendProtocolData, sendProtocolData.length);
        }
    }

    /**
     * 拍照结果通知
     *
     * @param type
     */
    public void takePicResultNotify(int type, byte[] generalResponse) {

        //byte[] generalResponse = null;

        byte[] sendProtocolData = null;
        byte[] sendUserData = new byte[10];

        switch (type) {
            case 1:
                //generalResponse =  CameraUtil.getInstance().getmCameraView().makeContentDataForTakePicResulteNotifyResponse(CameraUtil.getInstance().getmCameraView().getmCurrentFilePath());
                sendUserData[0] = 1;
                break;
            case 2:
                //generalResponse =  CameraUtil.getInstance().getmCameraView().makeContentDataForTakePicResulteNotifyResponse(null);
                sendUserData[0] = 2;
                break;
        }
        if (generalResponse != null) {
            System.arraycopy(generalResponse, 0, sendUserData, 1, generalResponse.length);
        }
        sendProtocolData = makeProtocol(CommServiceDefine.PROTOCOL_MONITOR_TAKE_PIC_RESULT,
                sendUserData);
        if (null != sendProtocolData) {
            MonitorIndustryCommunicateManager.sendData(sendProtocolData, sendProtocolData.length);
        }
    }

    private void dealReadPicFile(byte[] userData) {
        // TODO Auto-generated method stub
        if (userData == null) {
            LogUtil.printLog(TAG, "dealReadPicFile::userData == null");
            return;
        }
        if (userData.length < 10) {
            LogUtil.printLog(TAG, "dealReadPicFile::userData.length < 10 " + userData.length);
            return;
        }
        int currentIndex = 0;

        //照片文件索引号读取	4字节
        byte fileIndexByte1 = userData[currentIndex++];
        byte fileIndexByte2 = userData[currentIndex++];
        byte fileIndexByte3 = userData[currentIndex++];
        byte fileIndexByte4 = userData[currentIndex++];

        int picFileIndex = DataTransformer.getDWord(fileIndexByte1,
                fileIndexByte2, fileIndexByte3, fileIndexByte4);

        //读取位置	4字节
        byte readPosByte1 = userData[currentIndex++];
        byte readPosByte2 = userData[currentIndex++];
        byte readPosByte3 = userData[currentIndex++];
        byte readPosByte4 = userData[currentIndex++];

        int picPos = DataTransformer.getDWord(readPosByte1,
                readPosByte2, readPosByte3, readPosByte4);


        //读取长度	2字节
        byte readLengthByte1 = userData[currentIndex++];
        byte readLengthByte2 = userData[currentIndex++];
        int readLength = DataTransformer.getWord(readLengthByte1, readLengthByte2);

        //查找文件名
        String picFileName = MediaInfoManage.getInstance().queryPhotoFilePathByIndex(picFileIndex);

        LogUtil.printLog(TAG, picFileName + " " + picPos + " " + readLength);

        if (picFileName == null) {
            readPicFileErrorResponse(fileIndexByte1, fileIndexByte2, fileIndexByte3, fileIndexByte4, (byte) 2);
            return;
        }

        byte[] data = CameraView.readPicFileDataContent(picFileName, picPos, readLength);

        if (data != null && data.length > 0) {
            LogUtil.printLog(TAG, "data!=null&&data.length>0");
            byte[] sendProtocolData = null;
            byte[] sendUserData = new byte[11 + data.length];

            sendUserData[0] = fileIndexByte1;
            sendUserData[1] = fileIndexByte2;
            sendUserData[2] = fileIndexByte3;
            sendUserData[3] = fileIndexByte4;

            sendUserData[4] = 1;

            sendUserData[5] = readPosByte1;
            sendUserData[6] = readPosByte2;
            sendUserData[7] = readPosByte3;
            sendUserData[8] = readPosByte4;

            sendUserData[9] = readLengthByte1;
            sendUserData[10] = readLengthByte2;

            System.arraycopy(data, 0, sendUserData, 11, data.length);

            sendProtocolData = makeProtocol(CommServiceDefine.PROTOCOL_MONITOR_TAKE_PIC_DATA_REQUEST,
                    sendUserData);
            if (null != sendProtocolData) {
                MonitorIndustryCommunicateManager.sendData(sendProtocolData, sendProtocolData.length);
            }
        } else {
            LogUtil.printLog(TAG, "!(data!=null&&data.length>0)");
            //严格意义上这里并不都是3（读取位置超出文件总长）的错误
            readPicFileErrorResponse(fileIndexByte1, fileIndexByte2, fileIndexByte3, fileIndexByte4, (byte) 3);
        }
    }

    public void readPicFileErrorResponse(byte pos1, byte pos2, byte pos3, byte pos4, byte type) {
        byte[] sendProtocolData = null;
        byte[] sendUserData = new byte[5];

        sendUserData[0] = pos1;
        sendUserData[1] = pos2;
        sendUserData[2] = pos3;
        sendUserData[3] = pos4;

        sendUserData[4] = type;

        sendProtocolData = makeProtocol(CommServiceDefine.PROTOCOL_MONITOR_TAKE_PIC_DATA_REQUEST,
                sendUserData);
        if (null != sendProtocolData) {
            MonitorIndustryCommunicateManager.sendData(sendProtocolData, sendProtocolData.length);
        }
    }


    private void dealGetPicFileInfo(byte[] userData) {
        // TODO Auto-generated method stub
        if (userData == null) {
            LogUtil.printLog(TAG, "dealGetPicFileInfo::userData == null");
            return;
        }
        if (userData.length < 3) {
            LogUtil.printLog(TAG, "dealGetPicFileInfo::userData.length < 3 " + userData.length);
            return;
        }
        int currentIndex = 0;

        //流水号		2字节
        byte flowByte1 = userData[currentIndex++];
        byte flowByte2 = userData[currentIndex++];

        byte index = userData[currentIndex++];

        if (index <= 0 || userData.length < 3 + 4 * index) {
            LogUtil.printLog(TAG, "dealGetRecorderFileInfo::userData.length < 3+ " + userData.length);
            return;
        }

        byte[] sendProtocolData = null;


        int[] tempIndexs = new int[index];

        for (int i = 0; i < index; i++) {
            byte indexByte1 = userData[currentIndex++];
            byte indexByte2 = userData[currentIndex++];
            byte indexByte3 = userData[currentIndex++];
            byte indexByte4 = userData[currentIndex++];
            int fileIndex = DataTransformer.getDWord(indexByte1,
                    indexByte2, indexByte3, indexByte4);
            tempIndexs[i] = fileIndex;
        }


        PhotoFileInfo[] pfi = MediaInfoManage.getInstance().queryPhotoFileInfoByIndex(tempIndexs);
        byte[] sendUserData = null;
        if (pfi != null) {
            int pfiLength = pfi.length;
            sendUserData = new byte[43 * pfiLength + 3];
            int currentSendUserDataIndex = 0;
            sendUserData[currentSendUserDataIndex++] = flowByte1;
            sendUserData[currentSendUserDataIndex++] = flowByte2;
            sendUserData[currentSendUserDataIndex++] = (byte) pfiLength;

            if (pfiLength > 0) {
                for (int i = 0; i < pfiLength; i++) {
                    //索引号
                    int pfiIndex = pfi[i].getmIndex();
                    byte[] pfiIndex_byte = DataTransformer.intToByteArray(pfiIndex);
                    System.arraycopy(pfiIndex_byte, 0, sendUserData, currentSendUserDataIndex, pfiIndex_byte.length);
                    currentSendUserDataIndex += 4;

                    //文件大小
                    byte[] sizeByte = DataTransformer.intToByteArray((int) pfi[i].getmSize());
                    System.arraycopy(sizeByte, 0, sendUserData, currentSendUserDataIndex, sizeByte.length);
                    currentSendUserDataIndex += 4;

                    //摄像头ID
                    sendUserData[currentSendUserDataIndex++] = (byte) pfi[i].getmCameraId();

                    //时间
                    sendUserData[currentSendUserDataIndex++] = pfi[i].getmYear();
                    sendUserData[currentSendUserDataIndex++] = pfi[i].getmMonth();
                    sendUserData[currentSendUserDataIndex++] = pfi[i].getmDay();
                    sendUserData[currentSendUserDataIndex++] = pfi[i].getmHour();
                    sendUserData[currentSendUserDataIndex++] = pfi[i].getmMinute();
                    sendUserData[currentSendUserDataIndex++] = pfi[i].getmSecond();

                    //位置信息
                    byte[] lanBytes = DataTransformer.intToByteArray(pfi[i].getmLatitude());
                    byte[] lonBytes = DataTransformer.intToByteArray(pfi[i].getmLongitude());
                    System.arraycopy(lanBytes, 0, sendUserData, currentSendUserDataIndex, lanBytes.length);
                    currentSendUserDataIndex += lanBytes.length;
                    System.arraycopy(lonBytes, 0, sendUserData, currentSendUserDataIndex, lonBytes.length);
                    currentSendUserDataIndex += lonBytes.length;
                    sendUserData[currentSendUserDataIndex++] = (byte) pfi[i].getmSpeed();
                    byte[] direction = DataTransformer.intTo2Byte(pfi[i].getmDirection());
                    System.arraycopy(direction, 0, sendUserData, currentSendUserDataIndex, direction.length);
                    currentSendUserDataIndex += direction.length;
                    byte[] height = DataTransformer.intTo2Byte(pfi[i].getmHeight());
                    System.arraycopy(height, 0, sendUserData, currentSendUserDataIndex, height.length);
                    currentSendUserDataIndex += height.length;
                    //报警信息
                    byte[] alarm = DataTransformer.intToByteArray(pfi[i].getmAlarmState());
                    System.arraycopy(alarm, 0, sendUserData, currentSendUserDataIndex, alarm.length);
                    currentSendUserDataIndex += alarm.length;
                    //传感器
                    byte[] sensor = DataTransformer.intToByteArray(pfi[i].getmSensorState());
                    System.arraycopy(sensor, 0, sendUserData, currentSendUserDataIndex, sensor.length);
                    currentSendUserDataIndex += sensor.length;
                    //事件
                    sendUserData[currentSendUserDataIndex++] = (byte) pfi[i].getmTriggerEvent();
                    //分辨率
                    sendUserData[currentSendUserDataIndex++] = (byte) pfi[i].getmResolution();
                    //画质
                    sendUserData[currentSendUserDataIndex++] = (byte) pfi[i].getmQuality();
                    //亮度
                    sendUserData[currentSendUserDataIndex++] = (byte) pfi[i].getmBrightness();

                    //对比度
                    sendUserData[currentSendUserDataIndex++] = (byte) pfi[i].getmContrast();
                    //饱和度
                    sendUserData[currentSendUserDataIndex++] = (byte) pfi[i].getmSaturation();

                    //色度
                    sendUserData[currentSendUserDataIndex++] = (byte) pfi[i].getmChroma();

                }

            }
        } else {
            sendUserData = new byte[3];
            int currentSendUserDataIndex = 0;
            sendUserData[currentSendUserDataIndex++] = flowByte1;
            sendUserData[currentSendUserDataIndex++] = flowByte2;
            sendUserData[currentSendUserDataIndex++] = 0;
        }

        sendProtocolData = makeProtocol(CommServiceDefine.PROTOCOL_MONITOR_GET_PIC_FILE_INFO,
                sendUserData);
        if (null != sendProtocolData) {
            MonitorIndustryCommunicateManager.sendData(sendProtocolData, sendProtocolData.length);
        }
    }


    private void dealSearchPicFileIndex(byte[] userData) {
        // TODO Auto-generated method stub
        if (userData == null) {
            LogUtil.printLog(TAG, "dealSearchPicFileIndex::userData == null");
            return;
        }
        if (userData.length < 3) {
            LogUtil.printLog(TAG, "dealSearchPicFileIndex::userData.length < 3 " + userData.length);
            return;
        }
        int currentIndex = 0;

        //流水号		2字节
        byte flowByte1 = userData[currentIndex++];
        byte flowByte2 = userData[currentIndex++];
        //条件个数
        byte num = userData[currentIndex++];
        if (num > 0) {
            PhotoQueryInfo pqi = new PhotoQueryInfo();
            for (int i = 0; i < num; i++) {
                byte type = userData[currentIndex++];
                switch (type) {
                    case 0x01:
                        if (userData.length >= currentIndex + 12) {
                            currentIndex++;
                            pqi.setmYear(userData[currentIndex++]);
                            pqi.setmMonth(userData[currentIndex++]);
                            pqi.setmDay(userData[currentIndex++]);
                            pqi.setmHour(userData[currentIndex++]);
                            pqi.setmMinute(userData[currentIndex++]);
                            pqi.setmSecond(userData[currentIndex++]);
                            pqi.setmEndYear(userData[currentIndex++]);
                            pqi.setmEndMonth(userData[currentIndex++]);
                            pqi.setmEndDay(userData[currentIndex++]);
                            pqi.setmEndHour(userData[currentIndex++]);
                            pqi.setmEndMinute(userData[currentIndex++]);
                            pqi.setmEndSecond(userData[currentIndex++]);
                        }
                        break;
                    case 0x02:
                        if (userData.length >= currentIndex + 1) {
                            currentIndex++;
                            pqi.setmCameraId(userData[currentIndex++]);
                        }
                        break;
                    case 0x03:
                        if (userData.length >= currentIndex + 1) {
                            currentIndex++;
                            pqi.setmEvent(userData[currentIndex++]);
                        }
                        break;
                }
            }
            int[] indexs = MediaInfoManage.getInstance().queryPhotoFileIndex(pqi);

            byte[] sendProtocolData = null;
            byte[] sendUserData = null;
            if (indexs != null && indexs.length > 0) {
                sendUserData = new byte[4 * indexs.length + 3];

                int currentSendUserDataIndex = 0;
                sendUserData[currentSendUserDataIndex++] = flowByte1;
                sendUserData[currentSendUserDataIndex++] = flowByte2;
                sendUserData[currentSendUserDataIndex++] = (byte) (indexs.length);

                for (int j = 0; j < indexs.length; j++) {
                    byte[] indexsByte = DataTransformer.intToByteArray(indexs[j]);
                    System.arraycopy(indexsByte, 0, sendUserData, currentSendUserDataIndex, indexsByte.length);
                    currentSendUserDataIndex += indexsByte.length;
                }

            } else {
                sendUserData = new byte[3];
                int currentSendUserDataIndex = 0;
                sendUserData[currentSendUserDataIndex++] = flowByte1;
                sendUserData[currentSendUserDataIndex++] = flowByte2;
                sendUserData[currentSendUserDataIndex++] = 0;
            }
            sendProtocolData = makeProtocol(CommServiceDefine.PROTOCOL_MONITOR_GET_PIC_FILE_INDEX,
                    sendUserData);
            if (null != sendProtocolData) {
                MonitorIndustryCommunicateManager.sendData(sendProtocolData, sendProtocolData.length);
            }

        }
    }


}
