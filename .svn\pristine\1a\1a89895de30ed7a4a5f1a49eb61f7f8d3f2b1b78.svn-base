/*
 * 文 件 名:  DianZhaoInfo.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2013-4-30
 * 文件描述:  定义电召信息封装类。
 *****************************修改记录********************************
 * 
 * 修改者: 
 * 修改单号： [可选]
 * 修改描述:
 *
 *********************************************************************  
 */
package com.yaxon.telematics.service.aidl.main;

import android.os.Parcel;
import android.os.Parcelable;
import android.util.Log;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

import com.yaxon.telematics.service.taxi.TaxiConstDef;


/**
 * 封装了电召信息各个字段，包括订单号，要车时间，描述信息。
 * 
 * <AUTHOR>
 * @version   V1.0，2013-4-30
 * @see       
 * @since     V1.1.2
 */
@Entity(tableName = DianZhaoInfo.TABLE_NAME)
public class DianZhaoInfo implements Parcelable
{
    @Ignore
    public static final String TABLE_NAME = "yx_order";

    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "_id")
    public int _id;
    /**
     * 订单号
     */
    @ColumnInfo(name = "order_no")
    public int mNumber = 0;
    /**
     * 业务类型，0x00–即时召车，0x01–预约召车，0x02-车辆指派
     */
    @ColumnInfo(name = "order_type")
    public int mType = -1;
    /**
     * 用车时间：yymmddhhmmss
     */
    @ColumnInfo(name = "need_time")
    public String mUseCarTime = null;
    /**
     * 描述信息
     */
    @ColumnInfo(name = "des_info")
    public String mDescribeInfo = null;
    /**
     * 纬度
     */
    @ColumnInfo(name = "s_lat")
    public int mLatitude = 0;
    /**
     * 经度
     */
    @ColumnInfo(name = "s_lng")
    public int mLongi = 0;
    
    /**
     * 显示与否的标识
     */
    @Ignore
    public int mFlag = 0;
    /**
     * 乘客电话
     */
    @ColumnInfo(name = "passenger_phone")
    public String mPassengerPhone = "";
    /**
     * 电召费
     */
    @ColumnInfo(name = "order_fee")
    public int mFee = 0;
    /**
     * 目的地纬度
     */
    @ColumnInfo(name = "d_lat")
    public int dLatitude = 0;
    /**
     * 目的地经度
     */
    @ColumnInfo(name = "d_lng")
    public int dLongi = 0;
    /**
     * 电召状态
     */
    @ColumnInfo(name = "order_state")
    public int mState = -1;
    /**
     * 操作订单的时间，以便作异常超时处理
     */
    @Ignore
    public long mExecuteTime = 0;
    
    @Override
    public int describeContents()
    {
        // TODO Auto-generated method stub
        return 0;
    }
    @Override
    public void writeToParcel(Parcel dest, int flags)
    {
        // TODO Auto-generated method stub
        dest.writeInt(mNumber);
        dest.writeInt(mType);
        dest.writeString(mUseCarTime);
        dest.writeString(mDescribeInfo);
        dest.writeInt(mLatitude);
        dest.writeInt(mLongi);
        dest.writeInt(mFlag);
        dest.writeString(mPassengerPhone);
        dest.writeInt(mFee);
        dest.writeInt(dLatitude);
        dest.writeInt(dLongi);
        dest.writeInt(mState);
        dest.writeLong(mExecuteTime);
    }
    
    public static final Parcelable.Creator<DianZhaoInfo> CREATOR = new Parcelable.Creator<DianZhaoInfo>()
    {

        @Override
        public DianZhaoInfo createFromParcel(Parcel source)
        {
            // TODO Auto-generated method stub
            DianZhaoInfo dianZhaoInfo = new DianZhaoInfo();
            dianZhaoInfo.mNumber = source.readInt();
            dianZhaoInfo.mType = source.readInt();
            dianZhaoInfo.mUseCarTime = source.readString();
            dianZhaoInfo.mDescribeInfo = source.readString();
            dianZhaoInfo.mLatitude = source.readInt();
            dianZhaoInfo.mLongi = source.readInt();
            dianZhaoInfo.mFlag  = source.readInt();
            dianZhaoInfo.mPassengerPhone = source.readString();
            dianZhaoInfo.mFee = source.readInt();
            dianZhaoInfo.dLatitude = source.readInt();
            dianZhaoInfo.dLongi = source.readInt();
            dianZhaoInfo.mState = source.readInt();
            dianZhaoInfo.mExecuteTime = source.readLong();
            return dianZhaoInfo;
        }

        @Override
        public DianZhaoInfo[] newArray(int size)
        {
            // TODO Auto-generated method stub
            return new DianZhaoInfo[size];
        }
    };

    /**
     * 订单结束
     * @return
     */
    public boolean isFinish(){
        if (mState == TaxiConstDef.OrderState.SUCCESS.getOrderState()){
            return true;
        }

        return false;
    }

    /**
     * 订单状态
     * @return
     */
    public int isOrderState(){
        int ret;
        switch (TaxiConstDef.OrderState.valueOf(mState)){
            case SUCCESS:
            case CANCELLED_BY_CENTER:
            case FAIL:
               ret = 0;
                break;
            case PICKUP:
            case TO_DESTINATION:
            case TO_EXECUTE:
                ret = 1;
                break;
            default:
                ret = 2;
                break;
        }

        return ret;
    }

    /**
     * 赋值
     * @param info
     */
    public void set(DianZhaoInfo info){
        if (info == null){
            return;
        }

        this.mNumber = info.mNumber;
        this.mType = info.mType;
        this.mUseCarTime = info.mUseCarTime;
        this.mDescribeInfo = info.mDescribeInfo;
        this.mLatitude = info.mLatitude;
        this.mLongi = info.mLongi;
        this.mFlag = info.mFlag;
        this.mPassengerPhone = info.mPassengerPhone;
        this.mFee = info.mFee;
        this.dLatitude = info.dLatitude;
        this.dLongi = info.dLongi;
        this.mState = info.mState;
        this.mExecuteTime = info.mExecuteTime;
    }

    /**
     * 该订单是否处于执行中
     * @return
     */
    public boolean isExecutingOrder(){
        if (mState == TaxiConstDef.OrderState.TO_DESTINATION.getOrderState()
                || mState == TaxiConstDef.OrderState.PICKUP.getOrderState()){
            return true;
        }

        return false;
    }

    @Override
    public String toString() {
        return "DianZhaoInfo{" +
                "mNumber=" + mNumber +
                ", mType=" + mType +
                ", mUseCarTime='" + mUseCarTime + '\'' +
                ", mDescribeInfo='" + mDescribeInfo + '\'' +
                ", mLatitude=" + mLatitude +
                ", mLongi=" + mLongi +
                ", mFlag=" + mFlag +
                ", mPassengerPhone='" + mPassengerPhone + '\'' +
                ", mFee='" + mFee + '\'' +
                ", dLatitude=" + dLatitude +
                ", dLongi=" + dLongi +
                ", mState=" + mState +
                ", mExecuteTime=" + mExecuteTime +
                '}';
    }
}
