<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id=":YX_CommunicatePro_120ND" external.linked.project.path="$MODULE_DIR$" external.root.project.path="$MODULE_DIR$/.." external.system.id="GRADLE" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="android-gradle" name="Android-Gradle">
      <configuration>
        <option name="GRADLE_PROJECT_PATH" value=":YX_CommunicatePro_120ND" />
        <option name="LAST_SUCCESSFUL_SYNC_AGP_VERSION" value="3.6.3" />
        <option name="LAST_KNOWN_AGP_VERSION" value="3.6.3" />
      </configuration>
    </facet>
    <facet type="android" name="Android">
      <configuration>
        <option name="SELECTED_BUILD_VARIANT" value="meigRelease" />
        <option name="ASSEMBLE_TASK_NAME" value="assembleMeigRelease" />
        <option name="COMPILE_JAVA_TASK_NAME" value="compileMeigReleaseSources" />
        <afterSyncTasks>
          <task>generateMeigReleaseSources</task>
        </afterSyncTasks>
        <option name="ALLOW_USER_CONFIGURATION" value="false" />
        <option name="MANIFEST_FILE_RELATIVE_PATH" value="/src/main/AndroidManifest.xml" />
        <option name="RES_FOLDER_RELATIVE_PATH" value="/src/main/res" />
        <option name="RES_FOLDERS_RELATIVE_PATH" value="file://$MODULE_DIR$/src/main/res" />
        <option name="TEST_RES_FOLDERS_RELATIVE_PATH" value="" />
        <option name="ASSETS_FOLDER_RELATIVE_PATH" value="/src/main/assets" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_7">
    <output url="file://$MODULE_DIR$/build/intermediates/javac/meigRelease/classes" />
    <output-test url="file://$MODULE_DIR$/build/intermediates/javac/meigReleaseUnitTest/classes" />
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/build/generated/renderscript_source_output_dir/meigRelease/out" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/aidl_source_output_dir/meigRelease/out" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/ap_generated_sources/meigRelease/out" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/source/buildConfig/meig/release" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/res/rs/meig/release" type="java-resource" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/meigRelease/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/meigRelease/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/meigRelease/assets" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/meigRelease/aidl" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/meigRelease/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/meigRelease/rs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/meigRelease/shaders" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/ap_generated_sources/meigReleaseUnitTest/out" isTestSource="true" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeigRelease/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeigRelease/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeigRelease/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeigRelease/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeigRelease/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeigRelease/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeigRelease/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/meig/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/meig/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/meig/assets" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/meig/aidl" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/meig/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/api_71/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/meig/rs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/meig/shaders" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestMeig/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestMeig/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestMeig/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestMeig/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestMeig/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestMeig/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestMeig/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeig/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeig/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeig/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeig/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeig/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeig/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testMeig/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/release/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/release/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/release/assets" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/release/aidl" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/release/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/release/rs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/release/shaders" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/testRelease/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testRelease/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testRelease/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testRelease/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testRelease/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testRelease/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testRelease/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/assets" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/aidl" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/rs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/shaders" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/shaders" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/build" />
    </content>
    <orderEntry type="jdk" jdkName="Android API 28 Platform" jdkType="Android SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Gradle: __local_aars__:F./Project/c1608/Yx_taxi_4.0_meige/YX_CommunicatePro_120ND/libs/commons-net-3.6.jar:unspecified@jar" level="project" />
    <orderEntry type="library" name="Gradle: __local_aars__:F./Project/c1608/Yx_taxi_4.0_meige/YX_CommunicatePro_120ND/libs/yx-ipc-sdk-core.1.3.7(Build.20190924-213144).jar:unspecified@jar" level="project" />
    <orderEntry type="library" name="Gradle: __local_aars__:F./Project/c1608/Yx_taxi_4.0_meige/YX_CommunicatePro_120ND/libs/yx-ipc-sdk-server.1.3.7(Build.20190924-213144).jar:unspecified@jar" level="project" />
    <orderEntry type="library" name="Gradle: __local_aars__:F./Project/c1608/Yx_taxi_4.0_meige/YX_CommunicatePro_120ND/libs/classes_meig_71.jar:unspecified@jar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.code.gson:gson:2.8.1@jar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.room:room-common:2.2.6@jar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.collection:collection:1.1.0@jar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.arch.core:core-common:2.1.0@jar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-common:2.3.0-alpha07@jar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.annotation:annotation:1.2.0-alpha01@jar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.appcompat:appcompat:1.3.0-alpha02@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.fragment:fragment:1.3.0-alpha08@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.activity:activity:1.2.0-alpha08@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.appcompat:appcompat-resources:1.3.0-alpha02@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.drawerlayout:drawerlayout:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.viewpager:viewpager:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.loader:loader:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.vectordrawable:vectordrawable-animated:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.vectordrawable:vectordrawable:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.customview:customview:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.core:core:1.5.0-alpha02@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.cursoradapter:cursoradapter:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.0-alpha07@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.savedstate:savedstate:1.1.0-alpha01@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.room:room-runtime:2.2.6@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.sqlite:sqlite-framework:2.0.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.sqlite:sqlite:2.0.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-livedata:2.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.arch.core:core-runtime:2.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-runtime:2.3.0-alpha07@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.versionedparcelable:versionedparcelable:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-viewmodel:2.3.0-alpha07@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-livedata-core:2.3.0-alpha07@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.interpolator:interpolator:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.annotation:annotation-experimental:1.0.0@aar" level="project" />
    <orderEntry type="module" module-name="YX_Common" />
  </component>
</module>