<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/main_background">

    <LinearLayout
        android:id="@+id/title_bar"
        android:orientation="horizontal"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true">

        <include layout="@layout/main_title_simple"></include>
    </LinearLayout>

    <ListView
        android:id="@+id/list_testing_menu"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title_bar"
        android:padding="2px"
        android:background="@drawable/list_bg"
        android:cacheColorHint="#00000000"
        android:saveEnabled="false"
        android:scrollbars="none"
        android:layout_margin="3px"
        android:divider="@color/listview_seperator"
        android:dividerHeight="1px" />

    <RelativeLayout
        android:visibility="gone"
        android:id="@+id/layout_btn_next_pre"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="15px"
        android:layout_marginRight="15px">

        <Button
            android:id="@+id/btn_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:background="@drawable/main_common_btn_next_x"
            android:onClick="onClick" />

        <Button
            android:id="@+id/btn_pre"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@+id/btn_next"
            android:layout_marginRight="5px"
            android:layout_alignParentBottom="true"
            android:background="@drawable/main_common_btn_prev_x"
            android:onClick="onClick" />
    </RelativeLayout>
</RelativeLayout>
