<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/main_background">
    <include
        android:id="@+id/title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/h50dp"
        layout="@layout/main_title_simple" />
    <TextView
        android:id="@+id/tvTestBtnTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/title_layout"
        android:layout_marginTop="@dimen/h20dp"
        android:textSize="@dimen/h20dp"
        android:visibility="visible"
        android:textColor="@color/white"
        android:text="@string/testing_button_meige950_tips"/>
    <LinearLayout
        android:id="@+id/ll_left_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/title_layout"
        android:layout_toRightOf="@id/tvTestBtnTips"
        android:layout_marginLeft="@dimen/w10dp"
        android:layout_marginTop="@dimen/h10dp"
        android:orientation="vertical"
        android:layout_centerHorizontal="true">

        <CheckBox
            android:id="@+id/cb_power"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/testing_button_power" />

        <CheckBox
            android:id="@+id/cb_main_page"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/testing_button_mainpage" />

        <CheckBox
            android:id="@+id/cb_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/testing_button_back" />
        <CheckBox
            android:id="@+id/cb_vol_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/testing_button_vol_add" />
        <CheckBox
            android:id="@+id/cb_vol_sub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/testing_button_vol_sub" />


        <Button
            android:id="@+id/testing_button_ok"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:background="@drawable/btn_bg"
            android:text="@string/main_confirm"
            android:textColor="@color/white" />
    </LinearLayout>


</RelativeLayout>