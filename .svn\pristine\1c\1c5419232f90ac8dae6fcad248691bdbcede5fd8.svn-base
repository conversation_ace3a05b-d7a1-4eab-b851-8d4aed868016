package com.yaxon.base.rxjava.subscribe;

import com.yaxon.base.rxjava.exception.RxException;
import com.yaxon.base.rxjava.exception.RxExceptionHandler;
import com.yaxon.utils.LoggerUtil;

import io.reactivex.observers.DisposableObserver;

public abstract class RxSubscriber<T> extends DisposableObserver<T> {

    @Override
    protected void onStart() {
    }

    @Override
    public void onComplete() {
    }

    @Override
    public void onNext(T t) {
        try {
            onSuccess(t);
        } catch (Throwable e) {
            e.printStackTrace();
            onError(e);
        }
    }

    @Override
    public final void onError(Throwable e) {
        LoggerUtil.e("RxSubscriber", "-->Subscriber is onError");
        try {
            if (e instanceof RxException) {
                LoggerUtil.e("RxSubscriber", "--> e instanceof RxException, message:" + e.getMessage());
                onError((RxException) e);
            } else {
                LoggerUtil.e("RxSubscriber", "e !instanceof RxException, message:" + e.getMessage());
                onError(RxExceptionHandler.handleException(e));
            }
        } catch (Throwable throwable) {  //防止onError中执行又报错导致rx.exceptions.OnErrorFailedException: Error occurred when trying to propagate error to Observer.onError问题
            e.printStackTrace();
        }
    }

    /**
     * 出错
     *
     * @param e
     */
    public abstract void onError(RxException e);

    /**
     * 安全版的{@link #onNext},自动做了try-catch
     *
     * @param t
     */
    public abstract void onSuccess(T t);
}
