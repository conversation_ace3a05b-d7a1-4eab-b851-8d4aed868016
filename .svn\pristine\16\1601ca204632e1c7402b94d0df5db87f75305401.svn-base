package com.yaxon.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.yaxon.common.HardwareManager;
import com.yaxon.common.ProgramLoader;
import com.yaxon.common.ServiceBindManage;
import com.yaxon.common.YXDefine;
import com.yaxon.utils.Log;

/**
 * <一句话功能简述> <功能详细描述>
 *
 * 描述：AsrReceiver.java
 *
 */
public class AsrReceiver extends BroadcastReceiver {
    private static final String TAG = AsrReceiver.class.getSimpleName();
    private HardwareManager mHardwareManager = new HardwareManager();// 硬件管理对象
    @Override
    public void onReceive(Context context, Intent intent) {
        String mAction = intent.getAction();
        Log.println(TAG, "Main, mAction: " + mAction);
        switch (mAction) {
            case YXDefine.ACTION_ASR_EVENT_MAIN:
                int eventType = intent.getIntExtra(YXDefine.KEY_ASR_EVENT_MAIN_FUNCTION_TYPE, 0);
                if (eventType == 0) {
                    return;
                }
                dealAsrEvent(eventType);
                break;

            default:
                break;
        }
    }

    //语音识别
    private void dealAsrEvent(int eventType) {
        Log.println(TAG, "Main, mEventType: " + eventType);
        if (eventType == YXDefine.MSG_ASR_EVENT_MAIN_OPEN_VIDEO_PREVIEW) {
            //打开视频预览
            ProgramLoader.videoPreview();
        } else if (eventType == YXDefine.MSG_ASR_EVENT_MAIN_CLOSE_VIDEO_PREVIEW) {
            //关闭预览
            ProgramLoader.videoPreviewExit();

        } else if (eventType == YXDefine.MSG_ASR_EVENT_MAIN_OPEN_VIDEO_SEARCH) {
            //打开视频回放
            ProgramLoader.videoSearch();

        } else if (eventType == YXDefine.MSG_ASR_EVENT_MAIN_CLOSE_VIDEO_SEARCH) {
            //关闭回放
            ProgramLoader.videoPlayBackExit();

        } else if (eventType == YXDefine.MSG_ASR_EVENT_MAIN_PLAY_MUSIC) {
            //打开音乐
            ServiceBindManage.switchUsb(0);
            ProgramLoader.audio();
        } else if (eventType == YXDefine.MSR_ASR_EVENT_MAIN_OPEN_VOLUME_INCREASE) {
            //增大音量
            int max = mHardwareManager.getMediaVoiceMax();
            int cur = mHardwareManager.getMediaVoice();
            if (cur < max) {
                mHardwareManager.setMendiaVoice(++cur);
            }
        } else if (eventType == YXDefine.MSR_ASR_EVENT_MAIN_OPEN_VOLUME_DECREASE) {
            //减小音量
            int cur = mHardwareManager.getMediaVoice();
            if (cur > 0) {
                mHardwareManager.setMendiaVoice(--cur);
            }
        }
    }
}
