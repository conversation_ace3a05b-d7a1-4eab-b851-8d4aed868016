package com.yaxon.business.home.player;


import com.yaxon.business.presenter.CommonPresenter;
import com.yaxon.datasource.api.RadioApi;

import java.util.List;

public class RadioPlayer extends Player {

    @Override
    public int doLast() {
        if (!CommonPresenter.getInstance().isSupportRadio()){
            return ERR_RADIO_NOT_BEEN_SUPPORTED;
        }

        if (isPlaying() == ERR_FAIL){
            return ERR_RADIO_IS_OFF;
        }

        return RadioApi.getInstance().changeToNeighbourValidFreq(0);
    }

    @Override
    public int doNext() {
        if (!CommonPresenter.getInstance().isSupportRadio()){
            return ERR_RADIO_NOT_BEEN_SUPPORTED;
        }

        if (isPlaying() == ERR_FAIL){
            return ERR_RADIO_IS_OFF;
        }

        return RadioApi.getInstance().changeToNeighbourValidFreq(1);
    }

    @Override
    public int doPlay(String path) {
        if (!CommonPresenter.getInstance().isSupportRadio()){
            return ERR_RADIO_NOT_BEEN_SUPPORTED;
        }

        return RadioApi.getInstance().create();
    }

    @Override
    public int doPause() {
        if (!CommonPresenter.getInstance().isSupportRadio()){
            return ERR_RADIO_NOT_BEEN_SUPPORTED;
        }

        return RadioApi.getInstance().pause();
    }

    @Override
    public int doExit() {
        if (!CommonPresenter.getInstance().isSupportRadio()){
            return ERR_RADIO_NOT_BEEN_SUPPORTED;
        }

        return RadioApi.getInstance().destory();
    }

    @Override
    public int isPlaying() {
        if (!CommonPresenter.getInstance().isSupportRadio()){
            return ERR_RADIO_NOT_BEEN_SUPPORTED;
        }

        return RadioApi.getInstance().isStartFm();
    }

    @Override
    public Player getPlayer() {
        return null;
    }


    @Override
    public PlayerType getPlayerType() {
        return PlayerType.FM;
    }
    /**
     * 自动搜台或打开试听
     * @param isAudition true:试听模式， false:自动搜台
     * @param isStart true 启动搜索或试听 false 停止搜索或试听
     */
    public int autoSearchOrTestPref(boolean isAudition,boolean isStart){
        if (!CommonPresenter.getInstance().isSupportRadio()){
            return ERR_RADIO_NOT_BEEN_SUPPORTED;
        }

        if (isPlaying() == ERR_FAIL){
            return ERR_RADIO_IS_OFF;
        }

        return  RadioApi.getInstance().autoSearchOrTestPref(isAudition, isStart);
    }

    /**
     * 查询搜台记录
     * @return
     */
    public List<String> queryRecordPref(){
        if (!CommonPresenter.getInstance().isSupportRadio()){
            return null;
        }

        if (isPlaying() == ERR_FAIL){
            return null;
        }

        return RadioApi.getInstance().queryRecordPref();
    }

    /**
     * 清除当前搜台记录
     * @return
     */
    public int clearCurRecordPref(){
        if (!CommonPresenter.getInstance().isSupportRadio()){
            return ERR_RADIO_NOT_BEEN_SUPPORTED;
        }

        if (isPlaying() == ERR_FAIL){
            return ERR_RADIO_IS_OFF;
        }

        return RadioApi.getInstance().clearCurRecordPref();
    }
    /**
     * 频率微调, FM步进：100, AM步进：９
     * @param type 0：向前， 1：向后
     */
    public int adjustFreq(int type){
        if (!CommonPresenter.getInstance().isSupportRadio()){
            return ERR_RADIO_NOT_BEEN_SUPPORTED;
        }

        if (isPlaying() == ERR_FAIL){
            return ERR_RADIO_IS_OFF;
        }

        return RadioApi.getInstance().adjustFreq(type);
    }

    /**
     * 改变频率
     * @param freq
     * @return
     */
    public int changeFreq(int freq){
        if (!CommonPresenter.getInstance().isSupportRadio()){
            return ERR_RADIO_NOT_BEEN_SUPPORTED;
        }

        if (isPlaying() == ERR_FAIL){
            return ERR_RADIO_IS_OFF;
        }

        return RadioApi.getInstance().changeFreq(freq);
    }
}
