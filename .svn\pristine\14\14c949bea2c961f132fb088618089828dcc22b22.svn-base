<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View"/>
        <import type="com.yaxon.utils.StringUtil"/>
        <import type="com.yaxon.business.home.player.PlayerType"/>

        <variable
            name="clickEvent"
            type="com.yaxon.business.home.fragment.HomeFragment.ClickEvent" />

        <variable
            name="picUrl"
            type="String" />

        <variable
            name="isLogType"
            type="Integer" />

        <variable
            name="viewModel"
            type="com.yaxon.business.home.viewmodel.HomeViewModel" />

        <variable
            name="playType"
            type="PlayerType" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <!--左侧布局-->

        <!--左侧具体布局-->
        <ViewFlipper
            android:id="@+id/vfLeft"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@drawable/home_card_bg"
            android:flipInterval="10000"
            android:inAnimation="@anim/anim_top_in"
            android:outAnimation="@anim/anim_bottom_out"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/llMiddle"
            app:layout_constraintTop_toTopOf="parent"
            android:focusableInTouchMode="true"
            android:focusable="true">
            <include
                android:id="@+id/iOperationData"
                layout="@layout/layout_operation_data"
                app:clickEvent="@{clickEvent}"/>

            <include
                android:id="@+id/iHotArea"
                layout="@layout/layout_hot_area"
                app:clickEvent="@{clickEvent}"/>
        </ViewFlipper>
        <!--中间布局-->
        <LinearLayout
            android:id="@+id/llMiddle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/w12dp"
            android:layout_marginRight="@dimen/w12dp"
            android:background="@drawable/home_card_bg"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/vfLeft"
            app:layout_constraintLeft_toRightOf="@id/vfLeft"
            app:layout_constraintRight_toLeftOf="@+id/llRight"
            app:layout_constraintTop_toTopOf="@id/vfLeft"/>
        <!--右侧布局-->
        <LinearLayout
            android:id="@+id/llRight"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@drawable/home_card_bg"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/vfLeft"
            app:layout_constraintLeft_toRightOf="@id/llMiddle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/vfLeft" />

        <!--中间详细布局-->
        <ImageView
            android:id="@+id/ivMessageIcon"
            android:layout_width="@dimen/w32dp"
            android:layout_height="@dimen/h32dp"
            android:layout_marginLeft="@dimen/w20dp"
            android:layout_marginTop="@dimen/h16dp"
            app:layout_constraintLeft_toLeftOf="@id/llMiddle"
            app:layout_constraintTop_toTopOf="@id/llMiddle"
            android:src="@drawable/selector_home_messge_bg"
            android:onClick="@{view->clickEvent.clickMessage()}"/>

        <TextView
            android:id="@+id/tvMessageCount"
            android:layout_width="@dimen/w24dp"
            android:layout_height="@dimen/h24dp"
            android:background="@drawable/shape_red_circle"
            android:singleLine="true"
            android:gravity="center"
            android:textSize="@dimen/w16sp"
            android:textColor="@color/white"
            app:layout_constraintLeft_toLeftOf="@id/llMiddle"
            app:layout_constraintTop_toTopOf="@id/llMiddle"
            android:layout_marginLeft="@dimen/w12dp"
            android:layout_marginTop="@dimen/h8dp"
            android:text="@{viewModel.messageCount}"/>

        <androidx.constraintlayout.widget.Group
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="@{StringUtil.isNotNullOrEmpty(viewModel.messageCount) ? View.VISIBLE : View.GONE}"
            app:constraint_referenced_ids="ivMessageIcon, tvMessageCount"/>

        <ImageView
            android:id="@+id/ivDriverPic"
            android:layout_width="@dimen/w148dp"
            android:layout_height="@dimen/h148dp"
            android:layout_marginTop="@dimen/h23dp"
            android:padding="@dimen/w8dp"
            app:layout_constraintLeft_toLeftOf="@id/llMiddle"
            app:layout_constraintRight_toRightOf="@id/llMiddle"
            app:layout_constraintTop_toTopOf="@id/llMiddle"
            android:background="@drawable/home_picture_bg"
            android:scaleType="fitCenter"
            app:url="@{picUrl}"
            app:isCircle="@{true}"
            android:onClick="@{view->clickEvent.clickPermit()}"/>

        <TextView
            android:id="@+id/tvRole"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/h29dp"
            android:text="@{isLogType == 0 ? @string/home_role_visitor : @string/home_role_super_admin}"
            android:textColor="@color/blue_1"
            android:textSize="@dimen/w28sp"
            app:layout_constraintLeft_toLeftOf="@id/ivDriverPic"
            app:layout_constraintRight_toRightOf="@id/ivDriverPic"
            app:layout_constraintTop_toBottomOf="@id/ivDriverPic" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gRole"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="tvRole"
            android:visibility="@{isLogType == 2 ? View.GONE : View.VISIBLE}"/>

        <ScrollView
            android:layout_width="@dimen/w236dp"
            android:layout_height="match_parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:visibility="@{isLogType == 2 ? View.VISIBLE : View.GONE}"
            android:layout_marginTop="@dimen/h180dp"
            android:layout_marginLeft="@dimen/w275dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/tvDriverNameText"
                    style="@style/Yx_Textview_White_20"
                    android:layout_marginTop="@dimen/h8dp"
                    android:text="司　　机: "/>

                <TextView
                    android:id="@+id/tvDriverName"
                    style="@style/Yx_Textview_White_20"
                    android:layout_toRightOf="@+id/tvDriverNameText"
                    android:layout_marginTop="@dimen/h8dp"
                    />

                <TextView
                    android:id="@+id/tvDriverNoTitle"
                    style="@style/Yx_Textview_White_20"
                    app:layout_constraintLeft_toLeftOf="parent"
                    android:layout_marginTop="@dimen/h20dp"
                    android:layout_below = "@+id/tvDriverName"
                    android:text="资格证号："/>

                <TextView
                    android:id="@+id/tvDriverNo"
                    style="@style/Yx_Textview_White_20"
                    android:layout_toRightOf="@+id/tvDriverNameText"
                    android:layout_marginTop="@dimen/h20dp"
                    android:layout_below = "@+id/tvDriverName"
                    />

                <TextView
                    android:id="@+id/tvSuperviseText"
                    style="@style/Yx_Textview_White_20"
                    app:layout_constraintLeft_toLeftOf="parent"
                    android:layout_marginTop="@dimen/h20dp"
                    android:layout_below = "@+id/tvDriverNo"
                    android:text="监督电话："/>

                <TextView
                    android:id="@+id/tvSupervise"
                    style="@style/Yx_Textview_White_20"
                    android:layout_toRightOf="@+id/tvDriverNameText"
                    android:layout_marginTop="@dimen/h20dp"
                    android:layout_below = "@+id/tvDriverNo"
                    />
            </RelativeLayout>

        </ScrollView>

<!--        <TextView-->
<!--            android:id="@+id/tvDriverName"-->
<!--            style="@style/Yx_Textview_White_23"-->
<!--            android:layout_marginLeft="@dimen/w30dp"-->
<!--            android:layout_marginRight="@dimen/w30dp"-->
<!--            android:layout_marginTop="@dimen/h20dp"-->
<!--            app:layout_constraintLeft_toLeftOf="@id/llMiddle"-->
<!--            app:layout_constraintTop_toBottomOf="@id/ivDriverPic"/>-->

<!--        <TextView-->
<!--            android:id="@+id/tvDriverNo"-->
<!--            style="@style/Yx_Textview_White_23"-->
<!--            android:layout_marginLeft="@dimen/w30dp"-->
<!--            android:layout_marginRight="@dimen/w30dp"-->
<!--            android:layout_marginTop="@dimen/w8dp"-->
<!--            app:layout_constraintLeft_toLeftOf="@id/llMiddle"-->
<!--            app:layout_constraintTop_toBottomOf="@id/tvDriverName"/>-->

<!--        <TextView-->
<!--            android:id="@+id/tvSupervise"-->
<!--            style="@style/Yx_Textview_White_23"-->
<!--            android:layout_marginLeft="@dimen/w30dp"-->
<!--            android:layout_marginRight="@dimen/w30dp"-->
<!--            android:layout_marginTop="@dimen/h8dp"-->
<!--            app:layout_constraintLeft_toLeftOf="@id/llMiddle"-->
<!--            app:layout_constraintTop_toBottomOf="@id/tvDriverNo"/>-->

<!--        <androidx.constraintlayout.widget.Group-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:visibility="@{isLogType  == 2 ? View.VISIBLE : View.GONE}"-->
<!--            app:constraint_referenced_ids="tvDriverName, tvDriverNo, tvSupervise" />-->

        <com.yaxon.view.ToggleSwitch
            android:id="@+id/tsMediaMenu"
            android:layout_width="@dimen/w150dp"
            android:layout_height="@dimen/h64dp"
            android:padding="@dimen/w2dp"
            android:background="@drawable/shape_home_media_menu_bg"
            android:gravity="center"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/llRight"
            app:layout_constraintBottom_toBottomOf="@id/llRight"
            app:activeBgColor="@color/white"
            app:inactiveBgColor="@color/transform"
            app:activeTextColor="@color/black"
            app:inactiveTextColor="@color/white"
            app:textToggleLeft="@string/media_menu_mp3"
            app:textToggleRight="@string/media_menu_fm"
            app:toggleWidth="0dp"
            app:cornerRadius="@dimen/w6dp"
            app:clickInterval="500"
            android:textSize="@dimen/w18sp"
            android:layout_marginBottom="@dimen/w12dp"
            android:layout_marginLeft="@dimen/w12dp"/>

        <ImageButton
            android:id ="@+id/btn_click_media"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="@id/llRight"
            app:layout_constraintBottom_toBottomOf="@id/llRight"
            android:layout_marginTop="@dimen/w12dp"
            android:layout_marginRight="@dimen/w12dp"
            android:layout_marginLeft="@dimen/w12dp"
            android:layout_marginBottom="@dimen/w12dp"
            android:background="@drawable/enlarge_small_normal"
            android:src="@drawable/selector_home_enlarge"
            android:onClick="@{view->clickEvent.clickMedia()}"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvFm"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="@dimen/h12dp"
            android:paddingLeft="@dimen/w12dp"
            android:paddingTop="@dimen/h16dp"
            android:paddingRight="@dimen/w12dp"
            android:paddingBottom="@dimen/h16dp"
            android:visibility="@{playType == PlayerType.FM ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toTopOf="@id/btn_click_media"
            app:layout_constraintLeft_toLeftOf="@id/llRight"
            app:layout_constraintRight_toRightOf="@id/llRight"
            app:layout_constraintTop_toTopOf="@id/llRight" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvMP3"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="@dimen/h12dp"
            android:paddingLeft="@dimen/w12dp"
            android:paddingRight="@dimen/w12dp"
            android:paddingTop="@dimen/h16dp"
            android:paddingBottom="@dimen/h16dp"
            app:layout_constraintTop_toTopOf="@id/llRight"
            app:layout_constraintBottom_toTopOf="@id/btn_click_media"
            app:layout_constraintLeft_toLeftOf="@id/llRight"
            app:layout_constraintRight_toRightOf="@id/llRight"
            android:visibility="@{playType == PlayerType.MP3 ? View.VISIBLE : View.GONE}"/>

        <TextView
            android:id="@+id/tvSum"
            style="@style/Yx_Textview_White_23"
            android:layout_marginLeft="@dimen/w30dp"
            android:layout_marginRight="@dimen/w30dp"
            android:layout_marginTop="@dimen/h30dp"
            app:layout_constraintTop_toTopOf="@id/llRight"
            app:layout_constraintLeft_toLeftOf="@id/llRight"
            android:gravity="center"
            android:textSize="@dimen/h36dp"
            android:textColor="@color/blue_1"
            android:text="@string/home_defaunt_0_0"
            android:singleLine="true"
            android:maxWidth="@dimen/w200dp"
            android:background="@drawable/shape_home_frame" />

        <TextView
            android:id="@+id/tvUnitPrice"
            style="@style/Yx_Textview_White_23"
            android:layout_marginLeft="@dimen/w30dp"
            android:layout_marginRight="@dimen/w30dp"
            android:layout_marginTop="@dimen/w20dp"
            app:layout_constraintLeft_toLeftOf="@id/llRight"
            app:layout_constraintTop_toBottomOf="@id/tvSum"
            android:text="单价： 0.0 元/公里"/>

        <TextView
            android:id="@+id/tvTaxi"
            style="@style/Yx_Textview_White_23"
            android:layout_marginLeft="@dimen/w30dp"
            android:layout_marginRight="@dimen/w30dp"
            android:layout_marginTop="@dimen/w20dp"
            app:layout_constraintLeft_toLeftOf="@id/llRight"
            app:layout_constraintTop_toBottomOf="@id/tvUnitPrice"
            android:text="计程： 0.0 公里"/>

        <TextView
            android:id="@+id/tvTime"
            style="@style/Yx_Textview_White_23"
            android:layout_marginLeft="@dimen/w30dp"
            android:layout_marginRight="@dimen/w30dp"
            android:layout_marginTop="@dimen/h20dp"
            app:layout_constraintLeft_toLeftOf="@id/llRight"
            app:layout_constraintTop_toBottomOf="@id/tvTaxi"
            android:text="计时： 00:00:00"/>

        <TextView
            android:id="@+id/tvSpeed"
            style="@style/Yx_Textview_White_23"
            android:layout_marginLeft="@dimen/w30dp"
            android:layout_marginRight="@dimen/w30dp"
            android:layout_marginTop="@dimen/h20dp"
            app:layout_constraintLeft_toLeftOf="@id/llRight"
            app:layout_constraintTop_toBottomOf="@id/tvTime"
            android:text="速度： 0.0 公里/时"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>