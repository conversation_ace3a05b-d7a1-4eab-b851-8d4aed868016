/*
 * 文 件 名:  ImplSocketService.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2012-11-26
 * 文件描述:  定义AIDL接口ICommService的实现类ImplCommService，实现socket和电话的管理。
 *****************************修改记录********************************
 * 
 * 修 改 者:  
 * 修改单号： [可选]
 * 修改描述:
 *
 *********************************************************************  
 */
package com.yaxon.telematics.service;

import android.os.RemoteException;
import android.util.Log;

import androidx.room.PrimaryKey;

import com.yaxon.telematics.service.aidl.comm.ICommService;
import com.yaxon.telematics.service.aidl.comm.PhoneBookItem;
import com.yaxon.telematics.service.aidl.comm.VoipParramInfo;
import com.yaxon.telematics.service.http.HttpRequest;
import com.yaxon.telematics.service.protocol.monitor.MonitorDeviceCtrl;
import com.yaxon.telematics.service.protocol.monitor.MonitorLinkManage;
import com.yaxon.telematics.service.protocol.monitor.MonitorParramSet;
import com.yaxon.telematics.service.protocol.monitor.MonitorPhoneFunction;
import com.yaxon.telematics.service.protocol.monitor.MonitorSocketManage;
import com.yaxon.telematics.service.protocol.recorder.MediaRecordUtil;

import java.util.List;

/**
 * 实现接口描述文件ICommService定义的接口，包括socket关闭、socket连接
 * socket数据发送、socket数据接收、socket连接判断、电话相关接口。
 *
 * <AUTHOR>
 * @version V1.0，2012-11-26
 * @see MonitorSocketManage
 * @since V1.0.1
 */
public class ImplCommService extends ICommService.Stub {

    private static final String TAG = "ImplCommService";
    MonitorSocketManage mMonitorSocketManage = null;
    MonitorPhoneFunction mMonitorPhoneFunction = null;
    private HttpRequest mHttp = null;

    private void stopAudioRecorder() {
        Log.i(TAG, "stopAudioRecorder");
        if (MediaRecordUtil.getInstance().getmRecorder() != null) {
            MediaRecordUtil.getInstance().getmRecorder().stopRecordingForce();
        }
    }

    public ImplCommService() {
        mMonitorSocketManage = MonitorSocketManage.getInstance();
        mMonitorPhoneFunction = MonitorPhoneFunction.getInstance();
        mHttp = HttpRequest.getInstance();
    }

    @Override
    public boolean close(int channel) throws RemoteException {
        // TODO Auto-generated method stub
        if (null == mMonitorSocketManage) {
            mMonitorSocketManage = MonitorSocketManage.getInstance();
        }

        return mMonitorSocketManage.close(channel);
    }

    @Override
    public boolean connect(int channel, String ip, int port)
            throws RemoteException {
        // TODO Auto-generated method stub
        if (null == mMonitorSocketManage) {
            mMonitorSocketManage = MonitorSocketManage.getInstance();
        }

        if (!MonitorLinkManage.isConnected()) {
            Log.e(TAG, ">>>>connect: com NOT connected");
            return false;
        }

        if (null == ip) {
            return false;
        }
        return mMonitorSocketManage.connect(channel, ip, port);
    }

    @Override
    public boolean isConnected(int channel) throws RemoteException {
        // TODO Auto-generated method stub
        if (null == mMonitorSocketManage) {
            mMonitorSocketManage = MonitorSocketManage.getInstance();
        }

        if (!MonitorLinkManage.isConnected()) {
            Log.i(TAG, ">>>>isConnected: com NOT connected");
            return false;
        }

        return mMonitorSocketManage.isConnected(channel);
    }

    @Override
    public byte[] receive(int channelId) throws RemoteException {
        // TODO Auto-generated method stub
        if (null == mMonitorSocketManage) {
            mMonitorSocketManage = MonitorSocketManage.getInstance();
        }

        if (!MonitorLinkManage.isConnected()) {
            Log.e(TAG, ">>>>receive: com NOT connected");
            return null;
        }

        return mMonitorSocketManage.receive(channelId);
    }

    @Override
    public int send(int channel, byte[] dataBuf, int dataLen)
            throws RemoteException {
        // TODO Auto-generated method stub
        if (null == mMonitorSocketManage) {
            mMonitorSocketManage = MonitorSocketManage.getInstance();
        }

        if (!MonitorLinkManage.isConnected()) {
            Log.e(TAG, ">>>>send: com NOT connected");
            return 0;
        }

        if (null == dataBuf || dataBuf.length != dataLen) {
            return 0;
        }

        return mMonitorSocketManage.send(channel, dataBuf, dataLen);
    }


    @Override
    public int connectHttpChannel(int channel, String ip, int port) throws RemoteException {
        // TODO Auto-generated method stub

        if (!MonitorLinkManage.isConnected()) {
            Log.e(TAG, ">>>>connectHttpChannel: com NOT connected");
            return -1;
        }

        return mHttp.connectHttpChannel(channel, ip, port);
    }

    @Override
    public void disconnectHttpChannel(int channel) throws RemoteException {
        // TODO Auto-generated method stub
        mHttp.disconnectHttpChannel(channel);
    }

    @Override
    public byte[] doHttpRequest(int channel, byte[] entityData, String ip, int port)
            throws RemoteException {
        // TODO Auto-generated method stub
        return mHttp.doHttpRequest(channel, entityData, ip, port);
    }

    @Override
    public int dial(String phoneNum) throws RemoteException {
        // TODO Auto-generated method stub        
        //stopAudioRecorder();
        if (!MonitorLinkManage.isConnected()){
            return -1;
        }
        return mMonitorPhoneFunction.dial(phoneNum);
    }

    @Override
    public boolean pickup() throws RemoteException {
        // TODO Auto-generated method stub
        if (!MonitorLinkManage.isConnected()){
            return false;
        }
        return mMonitorPhoneFunction.pickup();
    }

    @Override
    public boolean hangup() throws RemoteException {
        // TODO Auto-generated method stub
        if (!MonitorLinkManage.isConnected()){
            return false;
        }
        return mMonitorPhoneFunction.hangup();
    }

    @Override
    public boolean sendDtmf(String dtmfString) throws RemoteException {
        // TODO Auto-generated method stub
        if (!MonitorLinkManage.isConnected()){
            return false;
        }
        return mMonitorPhoneFunction.sendDtmf(dtmfString);
    }

    @Override
    public int sendSm(String phoneNum, String smContent) throws RemoteException {
        // TODO Auto-generated method stub
        if (!MonitorLinkManage.isConnected()){
            return -1;
        }
        return mMonitorPhoneFunction.sendSm(phoneNum, smContent);
    }

    @Override
    public int syncPhoneBook(int flowNo) throws RemoteException {
        // TODO Auto-generated method stub
        if (!MonitorLinkManage.isConnected()){
            return -1;
        }
        return mMonitorPhoneFunction.syncPhoneBook(flowNo);
    }

    @Override
    public List<PhoneBookItem> requestPhoneBook() throws RemoteException {
        // TODO Auto-generated method stub
        if (!MonitorLinkManage.isConnected()){
            return null;
        }
        return mMonitorPhoneFunction.requestPhoneBook();
    }


    @Override
    public boolean isPhoneBusy() throws RemoteException {
        // TODO Auto-generated method stub
        if (!MonitorLinkManage.isConnected()){
            return false;
        }
        return MonitorPhoneFunction.isBusy();
    }

    @Override
    public VoipParramInfo getVoipParramInfo() throws RemoteException {
        if (!MonitorLinkManage.isConnected()){
            return null;
        }
        return MonitorParramSet.getInstance().requestVoipParramInfo();
    }

    @Override
    public boolean enableAudioAmplifier(int stat) throws RemoteException {
        if (!MonitorLinkManage.isConnected()){
            return false;
        }
        return MonitorDeviceCtrl.getInstance().switchAudioAmplifier(stat);
    }

    @Override
    public int setVoipParram(String domain, String port) throws RemoteException {
        if (!MonitorLinkManage.isConnected()){
            return -1;
        }
        return MonitorParramSet.getInstance().setVoipParram(domain, port);
    }
}
