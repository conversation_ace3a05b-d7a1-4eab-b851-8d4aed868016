package com.yaxon.base.productesting;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.yaxon.base.R;

import java.util.ArrayList;
import java.util.HashMap;

public class TestingMenuAdapter extends BaseAdapter
{
	// added by DerekGuo
	private final int count = 10;
	private Context mContext = null;
	private ArrayList<TestingInfo> mArray = null;
	private static int TESTING_STATUS_NORMAL = 1;// 正常
	private static int TESTING_STATUS_ERROR = 2; // 故障

	// added by Derek<PERSON><PERSON>
	private int[] list_data = { R.string.testing_menu_com1_data,
			R.string.testing_menu_com2, R.string.testing_menu_com3,
			R.string.testing_menu_tf, R.string.testing_menu_udisk,
			R.string.testing_menu_screen, R.string.testing_menu_tts,
			R.string.testing_menu_button, R.string.testing_menu_audio,
			R.string.testing_menu_vedio };
	private int[] list_title = { R.string.testing_menu_com1,
			R.string.testing_menu_com2, R.string.testing_menu_com3,
			R.string.testing_menu_tf, R.string.testing_menu_udisk,
			R.string.testing_menu_screen, R.string.testing_menu_tts,
			R.string.testing_menu_button, R.string.testing_menu_audio,
			R.string.testing_menu_vedio };

	private HashMap<Integer, View> view_map = new HashMap<Integer, View>();

	private LayoutInflater inflater;

	public TestingMenuAdapter(Context context)
	{
		// TODO Auto-generated constructor stub
		inflater = (LayoutInflater) context
				.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
		mContext = context;
	}

	@Override
	public int getCount()
	{
		// TODO Auto-generated method stub
		return count;
	}

	@Override
	public Object getItem(int arg0)
	{
		// TODO Auto-generated method stub
		return view_map.get(arg0);
	}

	@Override
	public long getItemId(int arg0)
	{
		// TODO Auto-generated method stub
		return arg0;
	}

	@Override
	public View getView(int position, View convertView, ViewGroup arg2)
	{
		convertView = view_map.get(position);
		ViewHolder holder;
		String text_status = mContext
				.getString(R.string.testing_common_nocheck);
		mArray = TestingHolder.getInstance().getTestingArray();
		if (convertView == null)
		{
			convertView = inflater.inflate(R.layout.testing_menu_item, null);
			holder = new ViewHolder();
			holder.view_text_title = (TextView) convertView
					.findViewById(R.id.testing_menu_title);
			holder.view_text_data = (TextView) convertView
					.findViewById(R.id.testing_menu_data);
			holder.view_text_status = (TextView) convertView
					.findViewById(R.id.testing_menu_status);
			holder.view_text_index = (TextView) convertView
					.findViewById(R.id.testing_menu_index);

			convertView.setTag(holder);
		} else
		{
			holder = (ViewHolder) convertView.getTag();
		}

		holder.view_text_index.setText(position + 1 + "");
		holder.view_text_title.setText(list_title[position]);
		holder.view_text_data.setText(list_data[position]);
		for (int i = 0; i < mArray.size(); i++)
		{
			if (mArray.get(position).mIndex == position)
			{
				if (mArray.get(position).mStatus == TESTING_STATUS_NORMAL)
				{
					holder.view_text_status
							.setText(R.string.testing_common_normal);
					holder.view_text_status.setTextColor(Color.GREEN);
				} else if (mArray.get(position).mStatus == TESTING_STATUS_ERROR)
				{
					if (mArray.get(position).mContent != null)
					{
						holder.view_text_status.setText(mContext
								.getString(R.string.testing_common_error)
								+ mContext.getString(R.string.blank)
								+ mArray.get(position).mContent);
					} else
					{
						holder.view_text_status
								.setText(R.string.testing_common_error);
					}

					holder.view_text_status.setTextColor(Color.RED);
				} else
				{
					holder.view_text_status.setText(text_status);
					holder.view_text_status.setTextColor(Color.WHITE);
				}
			}
		}

		view_map.put(position, convertView);

		return convertView;
	}

	class ViewHolder
	{
		TextView view_text_title;
		TextView view_text_data;
		TextView view_text_status;
		TextView view_text_index;
	}

}
