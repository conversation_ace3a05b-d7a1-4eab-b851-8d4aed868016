package com.yaxon.base.common;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProviders;

import com.trello.lifecycle2.android.lifecycle.AndroidLifecycle;
import com.trello.rxlifecycle3.LifecycleProvider;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * fragment基类
 */
public abstract class BaseFragment<VDB extends ViewDataBinding, VM extends BaseViewModel> extends Fragment{
    private VDB binding;
    private VM viewModel;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View mView = inflater.inflate(layoutId(), container, false);
        binding = DataBindingUtil.bind(mView);
        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (binding != null) {
            binding.unbind();
        }

    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        //私有的初始化Databinding和ViewModel方法
        initViewDataBinding();
        initParam();
    }

    /**
     * 注入绑定
     */
    private void initViewDataBinding() {
        viewModel = initViewModel();
        if (viewModel == null) {
            Class modelClass;
            Type type = getClass().getGenericSuperclass();
            if (type instanceof ParameterizedType) {
                modelClass = (Class) ((ParameterizedType) type).getActualTypeArguments()[1];
            } else {
                //如果没有指定泛型参数，则默认使用BaseViewModel
                modelClass = BaseViewModel.class;
            }
            viewModel = (VM) createFragmentViewModel(this, modelClass);
        }

        bindingVariable();
        //支持LiveData绑定xml，数据改变，UI自动会更新
        binding.setLifecycleOwner(this);
        //让ViewModel拥有View的生命周期感应
        getLifecycle().addObserver(viewModel);
        //注入RxLifecycle生命周期
        LifecycleProvider<Lifecycle.Event> lifecycleProvider = AndroidLifecycle.createLifecycleProvider(this);
        viewModel.injectLifecycleProvider(lifecycleProvider);
    }

    /**
     * 跳转页面
     *
     * @param clz 所跳转的目的Activity类
     */
    public void startActivity(Class<?> clz) {
        startActivity(new Intent(getContext(), clz));
    }

    /**
     * 跳转页面
     *
     * @param clz    所跳转的目的Activity类
     * @param bundle 跳转所携带的信息
     */
    public void startActivity(Class<?> clz, Bundle bundle) {
        Intent intent = new Intent(getContext(), clz);
        if (bundle != null) {
            intent.putExtras(bundle);
        }
        startActivity(intent);
    }

    /**
     * =====================================================================
     **/
    public abstract void bindingVariable();
    /**
     * 初始化参数
     */
    public abstract void initParam();

    /**
     * 初始化根布局
     *
     * @return 布局layout的id
     */
    public abstract int layoutId();

    /**
     * 初始化ViewModel
     *
     * @return 继承BaseViewModel的ViewModel
     */
    public VM initViewModel() {
        return null;
    }

    public boolean isBackPressed() {
        return false;
    }

    /**
     * 创建ViewModel
     *
     * @param cls
     * @param <VM>
     * @return
     */
    public <VM extends ViewModel> VM createFragmentViewModel(Fragment fragment, Class<VM> cls) {
        return ViewModelProviders.of(fragment).get(cls);
    }

    /**
     * 创建ViewModel
     *
     * @param cls
     * @param <VM>
     * @return
     */
    public <VM extends ViewModel> VM createActivityViewModel(FragmentActivity activity, Class<VM> cls) {
        return ViewModelProviders.of(activity).get(cls);
    }

    /**
     * 获取binding
     * @param
     * @return
     */
    protected VDB getBinding(){
        if (binding == null){
            throw new NullPointerException("the binding can not be null!");
        }
        return binding;
    }

    /**
     * 获取ViewModel
     * @return
     */
    protected VM getViewModel(){
        if (viewModel == null){
            throw  new NullPointerException("the viewModel can not be null");
        }
        return viewModel;
    }

    protected abstract String getCurTag();
}
