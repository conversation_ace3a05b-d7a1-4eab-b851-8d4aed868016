<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="0dp"
    android:layout_height="match_parent"
    android:clickable="true"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/text_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:gravity="center"
        android:paddingBottom="@dimen/w8dp"
        android:paddingLeft="@dimen/w8dp"
        android:paddingRight="@dimen/w8dp"
        android:paddingTop="@dimen/w8dp" />

    <View
        android:id="@+id/separator"
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/w2dp"
        android:layout_marginTop="@dimen/w2dp"
        android:visibility="invisible" />

</LinearLayout>