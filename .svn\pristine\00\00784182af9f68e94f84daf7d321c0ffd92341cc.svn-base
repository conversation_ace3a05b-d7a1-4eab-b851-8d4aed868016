<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/main_background">

    <LinearLayout
        android:id="@+id/title_bar"
        android:orientation="horizontal"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content">

        <include layout="@layout/main_title_simple"></include>
    </LinearLayout>


    <LinearLayout
        android:background="@drawable/list_bg"
        android:orientation="horizontal"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginTop="40dp">

        <TextView
            android:id="@+id/textView_UpgHintTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/hostparam_upg_host_text_hint"
            android:layout_marginLeft="30dp"
            android:textSize="25sp" />

        <TextView
            android:id="@+id/textView_UpgHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="30sp" />

    </LinearLayout>

    <LinearLayout
        android:background="@drawable/list_bg"
        android:orientation="horizontal"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginTop="40dp">

        <TextView
            android:id="@+id/textView_UpgProgress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="24dp"
            android:text="@string/hostparam_upg_host_progress"
            android:textSize="25sp" />

        <ProgressBar
            android:id="@+id/progressBar_UpgProgress"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="260dp"
            android:layout_height="30dp" />


        <Button
            android:id="@+id/btn_reUpgrade"
            android:visibility="gone"
            android:layout_marginLeft="200dp"

            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:text="@string/hostparam_upg_host_retry" />

    </LinearLayout>

</LinearLayout>
