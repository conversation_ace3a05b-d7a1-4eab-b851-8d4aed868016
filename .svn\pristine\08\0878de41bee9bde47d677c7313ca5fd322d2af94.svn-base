package com.yaxon.telematics.service.taxi;

public final class AckResult {
    /** 参数异常 */
    public static final int ACK_PARRAM_ERR       = -2;
    /** 与车台未连接 */
    public static final int ACK_NOT_LINK         = -1;
    /** 应答失败 */
    public static final int ACK_FAIL             = 0;
    /** 应答成功 */
    public static final int ACK_SUCCESS          = 1;

    /**
     * 获取应答类型对应的提示
     * @param result
     * @return
     */
    public static String getAckTip(int result){
        String strRet;
        switch (result){
            case ACK_PARRAM_ERR:
                strRet = "参数出错";
                break;
            case ACK_NOT_LINK:
                strRet = "未连接车台";
                break;
            case ACK_FAIL:
                strRet = "应答失败";
                break;
            case ACK_SUCCESS:
                strRet = "应答成功";
                break;
            default:
                strRet = "未知应答(" + result + "）";
                break;
        }
        return strRet;
    }
}
