package com.yaxon.business.presenter;

import com.yaxon.business.ad.activity.AdActivity;
import com.yaxon.common.ActivityLauncher;
import com.yaxon.telematics.service.taxi.TaxiConstDef;
import com.yaxon.utils.LoggerUtil;

/**
 * 订单业务处理类
 * */
public class OrderPresenter extends PresenterBase{
    private static final String TAG = OrderPresenter.class.getSimpleName();
    private static OrderPresenter mInstance;
    private int curTaxiCarrayState = TaxiConstDef.TAXI_CAR_STATE_STOP;

    private OrderPresenter(){

    }

    public static OrderPresenter getInstance(){
        if (mInstance == null){
            synchronized (OrderPresenter.class){
                if (mInstance == null){
                    mInstance = new OrderPresenter();
                }
            }
        }

        return mInstance;
    }


    /**
     * 处理车辆营运状态
     *
     * @param carryState
     */
    public void dealTaxiState(int carryState){
        //空车变重车且本地扫描到广告列表时跳转到广告播放界面
        if (curTaxiCarrayState == TaxiConstDef.TAXI_CAR_STATE_EMPTY
                && carryState == TaxiConstDef.TAXI_CAR_STATE_HEAVY){
            if (CommonPresenter.getInstance().getAdLists() != null){
                ActivityLauncher.jumpActivity(AdActivity.class);
            } else {
                LoggerUtil.w(TAG, "warning, the ad list is null");
            }
        }

        if (carryState != curTaxiCarrayState){
            curTaxiCarrayState = carryState;
            switch (carryState){
                case TaxiConstDef.TAXI_CAR_STATE_DZ:

                    break;
                case TaxiConstDef.TAXI_CAR_STATE_CHARTERED:
                case TaxiConstDef.TAXI_CAR_STATE_STOP:

                    break;
                case TaxiConstDef.TAXI_CAR_STATE_EMPTY:

                    break;
                case TaxiConstDef.TAXI_CAR_STATE_HEAVY:
                    break;
                default:
                    break;
            }
        }
    }


}
