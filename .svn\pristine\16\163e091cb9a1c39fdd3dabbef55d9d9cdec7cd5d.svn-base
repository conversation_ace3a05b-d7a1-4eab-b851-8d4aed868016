package com.yaxon.yx_facedetect.proxy;

import android.os.Environment;

import java.io.File;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

import androidx.annotation.IntDef;

/**
 * 人脸识别常量类
 *
 * <AUTHOR> wangzw
 * @date : 19-11-25下午4:04
 * @desc : 人脸识别常量类
 */
public class FaceConstant {
    /**
     * shared_prefs键值存储文件名
     */
    public static final String SHAREDPREF_NAME = "com.yaxon.facedetect.SharedPreferences";
    /**
     * 司机证件照标识
     */
    public static final String SUFFIX_IDENTIFICATION = "_z";
    /**
     * 司机生活照标识
     */
    public static final String SUFFIX_NORMAL = "_s";
    /**
     * 人脸注册文件后缀名
     */
    public static final String SUFFIX_FACE_FILE = ".data";
    /**
     * 人脸照片文件后缀名
     */
    public static final String SUFFIX_FACE_PIC = ".jpg";
    /**
     * 保存人脸识别相关文件的文件夹路径
     */
    public static final String ROOT_DIR = Environment.getExternalStorageDirectory().getAbsolutePath()
            + File.separator + "arcFace3.0" + File.separator;

    public static final String FEATURES_DIR = ROOT_DIR + "arc_register" + File.separator + "features";

    /**
     * 图片上传给车台的临时缓存文件
     */
    public static final String UPLOAD_TEMP_PATH = ROOT_DIR + "arc_register" + File.separator + "imgs" +
            File.separator + "temp.jpg";

    /**
     * 下载图片的路径
     */
    public static final String FACE_DOWNLOAD_DIR = ROOT_DIR + File.separator + "face_download";

    public static final String PIC_DOWNLOAD_DIR = ROOT_DIR + File.separator + "pic_download";

    public static final String FACE_IMG = ROOT_DIR + "arc_register" + File.separator + "imgs";

    /**
     * 人头统计图片保存路径
     */
    public static final String FACE_COUNT_DIR = ROOT_DIR + File.separator + "face_count";

    /**
     * 人脸宽度占previewWidth的比例model = videoMode[2-16], imageModel[2-32]
     */
    public static final int DETECT_FACE_VAL = 16;

    /**
     * 最大可检测的人脸数量 最大50
     */
    public static final int MAX_DETECT_FACE_NUMB = 10;

    /**
     * 最大识别时间 10*1000ms
     */
    public static final int MAX_DETECT_COUNT = 10;

    /**
     * 视频模式无法检测人脸则跳转到图片模式的最大时间
     */
    public static final int MAX_IMAGE_MODEL_TIME = 5;

    /**
     * 人脸识别成功标志
     */
    public static final int DETECT_SUCCESS = 1;

    /**
     * 人脸识别失败的标志
     */
    public static final int DETECT_FAIL = 2;

    /**
     * 活体检测标志
     */
    public static final int DETECT_NOT_ALIVE = 3;

    /**
     * 调度信息广播
     */
    public final static String ATTEMPTER_INFO_ACTION = "com.yaxon.telematics.ACTION_RECEIVE_DISPATCH_INFO";

    /**
     * 调度信息广播Intent携带数据的关键字定义
     */
    public static final String KEY_DISPATH_INFO = "KEY_DISPATCH_INFO";
    /**
     * 当前预览的数据
     */
    public static byte[] nv21;


    /**
     * 当前人脸识别的分辨率
     */
    public static int previewWidth;
    public static int previewHeight;

    /**
     * 该变量用于防止平台频繁发送定时参数导致摄像头抓拍失败
     */
    public static boolean canStartDetect = false;

    /**
     * 该变量用于防止图片模式和视频模式同时发送数据到车台导致抓拍破图
     */
    public static boolean canSend;


    public static final int UNKNOWN = -1;
    public static final int NOT_ALIVE = 0;
    public static final int ALIVE = 1;
    public static final int FACE_NUM_MORE_THAN_ONE = -2;

    /**
     * 车台要求保存注册或者上传图片的文件路径
     */
    public static String imgSavePath;


    /**
     * 速度达到标准启动识别
     */
    public static final int SPEED_STANDARD = 20;

    /**
     * 人头统计速度标准
     */
    public static final int SPEED_STANDARD_FACE_COUNT = 30;

    /**
     * true ：先人脸识别后签到  false:反之
     */
    public static boolean isDetectFirst = false;
    /**
     * 左下角人脸直接登录（可能是选择图片的登录）
     */
    public static boolean isImageLogin = false;
    /**
     * acc有关的人脸登录
     */
    public static boolean isAccLogin = false;

    /**
     * 定时任务
     */
    public static boolean isTimerTask = false;

    /**
     * 是否全屏
     */
    public static boolean isFullScreen = false;
    /**
     * 是否为体验版本
     */

    public static boolean isTest = false;
    /**
     * 是否播报语音提示
     */
    public static boolean shouldPlayTts = false;

    public static final boolean CLOSE_FACE_DETECT = false;

    public static final String KEY_FACE_REGISTER = "KEY_FACE_REGISTER";
    /**
     * 人脸sdk激活广播
     */
    public static final String ACTION_FACE_ENGINE_ACTIVITY = "com.yaxon.face.ACTION_FACE_ENGINE_ACTIVITY";

    /**
     * 激活次数
     */
    public static final String KEY_FACE_ENGINE_ACTIVITY_COUNT = "com.yaxon.face.KEY_FACE_ENGINE_ACTIVITY_COUNT";
    /**
     * 摄像头异常广播
     */
    public static final String ACTION_CAMERA_OPEN_FAIL = "com.yaxon.face.ACTION_CAMERA_OPEN_FAIL";
    /**
     * 人脸识别结果广播
     */
    public static final String ACTION_FACE_DETECT_RESULT = "com.yaxon.face.ACTION_FACE_RESULT";
    /**
     * 人脸识别结果
     */
    public static final String KEY_FACE_DETECT_RESULT = "KEY_FACE_DETECT_RESULT";

    /**
     * 人脸识别分数
     */
    public static final String KEY_FACE_DETECT_POINT = "KEY_FACE_DETECT_POINT";

    /**
     * 载客不打表通知
     */
    public static final String ACTION_FACE_CHECK_RESULT = "com.yaxon.face.ACTION_FACE_CHECK_RESULT";

    /**
     * 载客不打表当前人数
     */
    public static final String KEY_FACE_CHECK_RESULT = "KEY_FACE_CHECK_RESULT";

    /**
     * 人数统计通知
     */
    public static final String ACTION_FACE_COUNT_RESULT = "com.yaxon.face.ACTION_FACE_COUNT_RESULT";

    /**
     * 人数统计当前人数
     */
    public static final String KEY_FACE_COUNT_RESULT = "KEY_FACE_COUNT_RESULT";

    /**
     * 人数统计/载客不打表 演示版本
     */

    public static final String KEY_FACE_COUNT_TEST = "KEY_FACE_COUNT_TEST";
    /**
     * 人数统计/载客不打表
     */
    public static final String KEY_FACE_CHECK = "KEY_FACE_CHECK";

    /**
     * 司机工号
     */
    public static final String KEY_DRIVER_NAME = "KEY_DRIVER_NAME";


    /**
     * 当前配置的属性
     */
    public static final String KEY_FACE_PARAMS = "KEY_FACE_PARAMS";

    /**
     * 人脸图片注册结果广播名字
     */
    public static final String ACTION_FACE_REGISTER_RESULT = "com.yaxon.face.ACTION_FACE_REGISTER_RESULT";
    /**
     * 人脸注册结果
     */
    public static final String KEY_FACE_REGISTER_RESULT = "KEY_FACE_REGISTER_RESULT";

    /**
     * 是否为检测版本
     */
    public static final String KEY_FACE_FOR_TEST = "KEY_FACE_FOR_TEST";


    public static final String FACE_ACTIVITY = "face_activity";//人脸引擎激活tag
    public static final String IS_FACE_DETECT_ON = "face_detect_on";//是否启用人脸识别功能
    public static final String IS_START_TIMER = "timer_detect_on";//是否启动定时人脸识别
    public static final String IS_START_ACC = "acc_detect_on";//是否启动acc人脸识别
    public static final String IS_START_LOGIN = "login_detect_on";//是否启动登录认证的人脸识别
    public static final String FACE_TIMER_ACTION = "face_action";//启动人脸定时识别的广播action
    public static final String KEY_FACE_START_RECEIVER = "KEY_FACE_START_RECEIVER";//用于判断是否是第一次广播通知，车台每次下发定时设置不马上执行
    public static final String FACE_TIMER_TIME = "face_time";//定时时间
    public static final String IS_LOGIN = "isLogin";//是否登录
    public static final String DRIVER_NAME = "driver_name";//工号
    public static final String IS_FACE_CHECK_ON = "IS_FACE_CHECK_ON";
    public static final String IS_FACE_COUNT_ON = "IS_FACE_COUNT_ON";
    public static final String IMG_VERSION_CODE = "IMG_VERSION_CODE";//本地保存的图片版本号
    public static final String FACE_PARAMS = "faceParams";//启动人脸识别需要的参数
    public static final String FOR_TEST = "FOR_TEST";//演示

    /**
     * 对于已经激活过  但是激活码数量超限额后需要重新激活
     */
    public static final String KEY_REACTIVITY = "KEY_REACTIVITY";
    public static final String KEY_APPID = "KEY_APPID";
    public static final String KEY_SDK_KEY = "KEY_SDK_KEY";

    /**
     * 在线激活失败
     */
    public static final String KEY_FACE_ACTIVITY_FAIL = "KEY_FACE_ACTIVITY_FAIL";

    /**
     * sdk 激活码
     */
    public static final String KEY_SDK_ACTIVITY_FAIL = "KEY_SDK_ACTIVITY_FAIL";

    /**
     * 第一步 人脸引擎初始化失败code
     */
    public static final String KEY_INIT_ENGINE_FAIL = "KEY_INIT_ENGINE_FAIL";

    /**
     * 人脸跟踪错误码key(检测人脸)
     */
    public static final String KEY_FT_FAIL = "KEY_FT_FAIL";

    /**
     * 人脸特征提取错误码key（检测出人脸后提取特征）
     */
    public static final String KEY_FR_FAIL = "KEY_FR_FAIL";

    /**
     * 默认 id key (如果客户端没有配置或者客户端配置都无法使用 申请账号 13616050881  密码 tencent2956839)
     */
    public static final String APP_ID = "2kxvSUShuSAwoJVCEtSZcRhj4WdyxjXfgjVSpG8U6q6i";

    public static final String SDK_KEY = "6U5aRoxgFCGDscrBEtRfqSgqQ7SjxvW3tHy4JUp5nyFX";

    public static final String ACTION_USE_FACE_SDK_TYPE = "com.yaxon.face.ACTION_USE_FACE_SDK_TYPE";
    public static final String KEY_FACE_USE_TYPE = "key_Face_use_type";

    //指定使用哪个算法厂家的人脸识别
    public static final int FACE_USE_CAMERA_CHANNEL = 0;
    public static final int COUNT_USE_CAMERA_CHANNEL = 1;


    //注解枚举
    @IntDef({FACE_USE_CAMERA_CHANNEL, COUNT_USE_CAMERA_CHANNEL})
    @Retention(RetentionPolicy.SOURCE)
    public @interface CameraChannelUseInfo {
    }
}
