package com.yaxon.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Region;
import android.graphics.drawable.ColorDrawable;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;

import com.yaxon.base.R;

public class RadiuCardView extends CardView {
    /** 左上角半径 */
    private float tlRadiu;
    /** 右上角半径 */
    private float trRadiu;
    /** 左下角半径 */
    private float blRadiu;
    /** 右下角半径 */
    private float brRadiu;

    public RadiuCardView(@NonNull Context context) {
        this(context, null);
    }

    public RadiuCardView(@NonNull Context context,
                         @Nullable AttributeSet attrs) {
        this(context, attrs, R.attr.materialCardViewStyle);
    }

    public RadiuCardView(@NonNull Context context,
                         @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setRadius(0);
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.RadiuCardView);
        tlRadiu = ta.getDimension(R.styleable.RadiuCardView_topLeftRadiu, 0);
        trRadiu = ta.getDimension(R.styleable.RadiuCardView_topRightRadiu, 0);
        blRadiu = ta.getDimension(R.styleable.RadiuCardView_bottomLeftRadiu, 0);
        brRadiu = ta.getDimension(R.styleable.RadiuCardView_bottomRightRadiu, 0);
        //ta.recycle();
        setBackground(new ColorDrawable());
    }

    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);
        Path path = new Path();
        RectF rectF = getRectF();
        float[] radius = new float[]{
                tlRadiu, tlRadiu, trRadiu, trRadiu, brRadiu, brRadiu, blRadiu, blRadiu
        };
        path.addRoundRect(rectF, radius, Path.Direction.CW);
        canvas.clipPath(path, Region.Op.INTERSECT);
        super.draw(canvas);
    }

    private RectF getRectF(){
        Rect rect = new Rect();
        getDrawingRect(rect);
        RectF rectF = new RectF(rect);
        return rectF;
    }
}
