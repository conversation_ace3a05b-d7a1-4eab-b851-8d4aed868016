<RelativeLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
  	android:layout_width="match_parent"
  	android:layout_height="wrap_content" 		
  	android:background="@drawable/main_background">
  	
    <Button
        android:id="@+id/btnVideoPlayPlay"
        android:text="@string/video_playback_play"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="100px"
        android:layout_centerHorizontal="true"
        android:onClick="onClick"
        android:visibility="invisible"
        />    
        
    <Button
        android:id="@+id/btnVideoPlayStop"
        android:text="@string/video_playback_stop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="180px"
        android:layout_centerHorizontal="true"
        android:onClick="onClick"
        />        
    
</RelativeLayout>