<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_enabled="false" android:drawable="@drawable/kaitian2d_test_result_normal" />

    <item android:state_focused="true" android:state_pressed="false" android:drawable="@drawable/kaitian2d_test_result_pressed" />
    <item android:state_focused="false" android:state_pressed="true" android:drawable="@drawable/kaitian2d_test_result_pressed" />

    <item android:state_focused="true" android:state_pressed="true" android:drawable="@drawable/kaitian2d_test_result_pressed" />
    <item android:state_focused="false" android:state_pressed="false" android:drawable="@drawable/kaitian2d_test_result_normal" />


</selector>