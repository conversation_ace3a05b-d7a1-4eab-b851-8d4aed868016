package com.yaxon.yx_control.system;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.RemoteException;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.TextView;

import com.yaxon.tts.TtsMessage;
import com.yaxon.tts.TtsPriority;
import com.yaxon.yx_control.R;
import com.yaxon.yx_control.YXActivity;
import com.yaxon.yx_control.YXApplication;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;

public class TtsTestActivity extends YXActivity implements OnClickListener {

    private static final String ACTION = "com.yaxon.TtsTestActivity";

    private static final String FILE_NAME = "TTS_TEST.txt";

    private boolean isPlay = false;
    private boolean isplaying = false;

    private TextView text_title;
    private TextView text_data;
    private View btn_play;
    private View btn_pause;
    private View btn_back;
//	private View btn_menu;

    private loadDataAsyncTask task;

    private String ttsStr;
    private int id = -1;


    private BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context arg0, Intent arg1) {
            int ttsid = arg1.getIntExtra("ttsID", -1);
            System.out.println("---------tts stop log--------" + ttsid);
            if (ttsid > 0 && ttsid == id) {
                System.out.println("---------tts stop --------" + ttsid);
                btn_play.setEnabled(true);
                isplaying = false;
                id = -1;
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // TODO Auto-generated method stub
        super.onCreate(savedInstanceState, R.layout.tts_test);

        text_data = (TextView) findViewById(R.id.test_tts);
        text_title = (TextView) findViewById(R.id.title_text_name);
        text_title.setText(R.string.project_menu_tts);

        task = new loadDataAsyncTask();
        task.execute("start");

        btn_play = findViewById(R.id.btn_play);
        btn_pause = findViewById(R.id.btn_pause);
        btn_back = findViewById(R.id.title_btn_exit);
//		btn_menu = findViewById(R.id.title_btn_menu);

        btn_play.setOnClickListener(this);
        btn_pause.setOnClickListener(this);
        btn_back.setOnClickListener(this);
//		btn_menu.setOnClickListener(this);

        //注册广播
        IntentFilter filter = new IntentFilter();
        filter.addAction("com.yaxon.tts.Player.PLAY_TTS_DONE");
        //注册广播和初始化
        try {
            registerReceiver(receiver, filter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_play:
                if (YXApplication.mttService != null && isPlay && ttsStr != null && !isplaying) {
                    if (!ttsStr.trim().equals("")) {
                        btn_play.setEnabled(false);
                        isplaying = true;
                        TtsMessage msg = new TtsMessage();
                        msg.mContent = ttsStr;
                        msg.mPriority = TtsPriority.TTS_PRIORITY_LOW;
                        msg.mInterruptable = 1;
                        msg.mCanDrop = 0;
                        msg.mSendBroadcast = 1;
                        try {
                            id = YXApplication.mttService.play(msg);
                            System.out.println("---------tts play --------: " + id + ", broadcast: " + msg.mSendBroadcast);
                            if (id <= 0) {
                                btn_play.setEnabled(true);
                                isplaying = false;
                            }
                        } catch (RemoteException e) {
                            // TODO Auto-generated catch block
                            e.printStackTrace();
                        }
                    }
                }
                break;
            case R.id.btn_pause:
                if (YXApplication.mttService != null && isPlay && id > 0 && isplaying) {
                    try {
                        YXApplication.mttService.stop(id);
                        System.out.println("---------tts stop onclick--------" + id);
                    } catch (RemoteException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                }
                break;
            case R.id.title_btn_exit:
                finish();
                break;
//		case R.id.title_btn_menu:
//			
//			break;
            case R.id.btn_volume:

                break;
            default:
                break;
        }
        super.onClick(v);
    }

    @Override
    protected void onDestroy() {
        if (YXApplication.mttService != null && isPlay && id > 0 && isplaying) {
            try {
                YXApplication.mttService.stop(id);
                System.out.println("---------tts stop onclick--------" + id);
            } catch (RemoteException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        if (task != null) {
            task.cancel(true);
        }

        unregisterReceiver(receiver);
        super.onDestroy();
    }


    private String getString() {
        try {
            InputStream is = getAssets().open(FILE_NAME);
            byte[] buf = new byte[is.available()];
            is.read(buf);
            isPlay = true;
            return new String(buf);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            isPlay = false;
            return getString(R.string.load_data);
        } catch (IOException e) {
            e.printStackTrace();
            isPlay = false;
            return getString(R.string.load_data);
        }
    }


    class loadDataAsyncTask extends AsyncTask<String, String, String> {

        @Override
        protected String doInBackground(String... arg0) {
            return getString();
        }

        @Override
        protected void onPostExecute(String result) {
            text_data.setText(result);
            ttsStr = result;
            super.onPostExecute(result);
        }

    }

}
