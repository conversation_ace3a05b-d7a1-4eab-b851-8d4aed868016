package com.yaxon.yx_control.system;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageButton;
import android.widget.TextView;

import com.yaxon.until.ControlApi;
import com.yaxon.yx_control.R;
import com.yaxon.yx_control.YXActivity;
import com.yaxon.yx_control.YXApplication;

public class LedPowerControlActivity extends YXActivity {

    private ControlApi mControlApi;

    private ImageButton mBtnExit;
    private TextView mTextView;

    private int mPowerLev = 0;
    private final int Led_GPIO = 149;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // TODO Auto-generated method stub
        super.onCreate(savedInstanceState, R.layout.led_pow_ctrl);

        mControlApi = ((YXApplication) getApplication()).mControlApi;

        mBtnExit = (ImageButton) findViewById(R.id.title_btn_exit);
        mBtnExit.setOnClickListener(this);

        mTextView = (TextView) findViewById(R.id.led_pow_hint);

        updateGpioState();

    }

    private void updateGpioState() {
        mPowerLev = mControlApi.getGoioState(Led_GPIO);

        mTextView.setText(getString(R.string.spectrl_led_power) + "(" + mPowerLev + ")");

    }

    @Override
    public void onClick(View v) {
        // TODO Auto-generated method stub
        int id = v.getId();

        switch (id) {
            case R.id.title_btn_exit:
                finish();
                break;
            case R.id.btn_open_led_pow://上电
                mControlApi.setGoioState(Led_GPIO, 1);
                updateGpioState();
                break;

            case R.id.btn_close_led_pow://掉电
                mControlApi.setGoioState(Led_GPIO, 0);
                updateGpioState();
                break;
        }
    }


}
