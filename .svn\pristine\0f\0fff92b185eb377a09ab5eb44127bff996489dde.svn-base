package com.yaxon.common;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Build;
import android.os.Environment;
import android.os.Looper;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.lang.Thread.UncaughtExceptionHandler;
import java.lang.reflect.Field;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CrashHandler implements UncaughtExceptionHandler
{
    private static final String TAG = CrashHandler.class.getName();

    // 系统默认的UncaughtException处理类
    private Thread.UncaughtExceptionHandler mDefaultHandler;
    // CrashHandler实例
    private static CrashHandler INSTANCE = new CrashHandler();
    // 程序的Context对象
    private Context mContext;
    // 用来存储设备信息和异常信息
    private Map<String, String> infos = new HashMap<String, String>();

    // 用于格式化日期,作为日志文件名的一部分
    private DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");

    private String mCrashDir = null;

    private int mMaxCrashFile = 100;

    /**
     * 保存文件的index
     */
    private SharedPreferences mSP;
    private int mLogIndex = 0;
    private final String keyLogIndex = "keyLogIndex";

    private CrashHandler()
    {
    }

    /** 获取CrashHandler实例 ,单例模式 */
    public static CrashHandler getInstance()
    {
        return INSTANCE;
    }

    /**
     * 初始化
     * 
     * @param context
     */
    public void init(Context context)
    {
        mContext = context;
        mSP = mContext.getSharedPreferences(TAG, Context.MODE_PRIVATE);
        mLogIndex = this.getInt(keyLogIndex, 1);

        checkCrashDir();
        // 获取系统默认的UncaughtException处理器
        mDefaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        // 设置该CrashHandler为程序的默认处理器
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    private synchronized void saveInt(String key, int value)
    {
        Editor editor = mSP.edit();
        if (editor == null)
        {
            return;
        }

        editor.putInt(key, value);
        editor.commit();
    }

    private synchronized int getInt(String key, int defValue)
    {
        return mSP.getInt(key, defValue);
    }

    private void checkCrashDir()
    {
        String pkgName = mContext.getPackageName();

        mCrashDir = YXDefine.CrashDir + "/" + pkgName + "/";
        File fi = new File(mCrashDir);
        fi.mkdirs();

        new Thread()
        {
            @Override
            public void run()
            {

                List<File> filesArray = Arrays.asList(new File(mCrashDir).listFiles());

                if (filesArray == null || filesArray.size() <mMaxCrashFile)
                {
                    return;
                }

                Collections.sort(filesArray, new Comparator<File>()
                {
                    @Override
                    public int compare(File o1, File o2)
                    {
                        if (o1.isDirectory() && o2.isFile())
                            return -1;
                        if (o1.isFile() && o2.isDirectory())
                            return 1;

                        return o1.getName().compareTo(o2.getName());
                    }
                });

                Collections.reverse(filesArray);

                for (File f : filesArray)
                {
                    Log.i(TAG, f.getName());
                }

                // 一次删除一半的异常文件，最早的异常文件仍然保留，以后有上传机制后，应该是上传后删除
                int keepNum = mMaxCrashFile / 2 + 1;

                int delNum = filesArray.size() - keepNum;

                for (int i = 0; i < delNum; i++)
                {
                    File delFile = filesArray.get(i);
                    if (delFile != null && delFile.isFile())
                    {
                        delFile.delete();
                    }
                }

            }
        }.start();

    }

    /**
     * 当UncaughtException发生时会转入该函数来处理
     */
    public void uncaughtException(Thread thread, Throwable ex)
    {
        if (!handleException(ex) && mDefaultHandler != null)
        {
            // 如果用户没有处理则让系统默认的异常处理器来处理
            mDefaultHandler.uncaughtException(thread, ex);
        } else
        {
            try
            {
                Thread.sleep(3000);
            } catch (InterruptedException e)
            {
                e.printStackTrace();
            }
            
            // 退出程序,注释下面的重启启动程序代码
            android.os.Process.killProcess(android.os.Process.myPid());
            System.exit(1);

            // Intent intent = new Intent();
            // intent.setClass(mContext,HomeActivity.class);
            // intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            // mContext.startActivity(intent);
            // android.os.Process.killProcess(android.os.Process.myPid());

        }
    }

    /**
     * 自定义错误处理,收集错误信息 发送错误报告等操作均在此完成.
     * 
     * @param ex
     * @return true:如果处理了该异常信息;否则返回false.
     */
    private boolean handleException(Throwable ex)
    {
        if (ex == null)
        {
            return false;
        }
        // 使用Toast来显示异常信息
        new Thread()
        {
            @Override
            public void run()
            {
                Looper.prepare();
                //Toast.makeText(mContext, "程序出现异常,即将退出...", Toast.LENGTH_LONG).show();
                Looper.loop();
            }
        }.start();

        // 收集设备参数信息
        collectDeviceInfo(mContext);
        // 保存日志文件
        saveCrashInfo2File(ex);
        return true;
    }

    /**
     * 收集设备参数信息
     * 
     * @param ctx
     */
    public void collectDeviceInfo(Context ctx)
    {
        try
        {
            PackageManager pm = ctx.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(ctx.getPackageName(), PackageManager.GET_ACTIVITIES);
            if (pi != null)
            {
                String versionName = pi.versionName == null ? "null" : pi.versionName;
                String versionCode = pi.versionCode + "";
                infos.put("versionName", versionName);
                infos.put("versionCode", versionCode);
            }
        } catch (NameNotFoundException e)
        {
            Log.e(TAG, "an error occured when collect package info", e);
        }

        Field[] fields = Build.class.getDeclaredFields();
        for (Field field : fields)
        {
            try
            {
                field.setAccessible(true);
                infos.put(field.getName(), field.get(null).toString());
                Log.d(TAG, field.getName() + " : " + field.get(null));
            } catch (Exception e)
            {
                Log.e(TAG, "an error occured when collect crash info", e);
            }
        }
    }

    /**
     * 保存错误信息到文件中
     * 
     * @param ex
     * @return 返回文件名称,便于将文件传送到服务器
     */
    private String saveCrashInfo2File(Throwable ex) {

        File fi = new File(mCrashDir);
        File[] fileArray = fi.listFiles();
        // crash文件超过了限定数量，则不保存新的crash文件
        if (fileArray != null && fileArray.length >= mMaxCrashFile) {
            Log.e(TAG, "saveCrashInfo2File: too many crash files");
            return null;
        }

        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String, String> entry : infos.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            sb.append(key + "=" + value + "\n");
        }

        Writer writer = new StringWriter();
        PrintWriter printWriter = new PrintWriter(writer);
        ex.printStackTrace(printWriter);
        Throwable cause = ex.getCause();
        while (cause != null) {
            cause.printStackTrace(printWriter);
            cause = cause.getCause();
        }
        printWriter.close();
        String result = writer.toString();
        sb.append(result);
        FileOutputStream fos = null;
        try {
            String strIndex = String.format("%08d", mLogIndex++);
            saveInt(keyLogIndex, mLogIndex);

            long timestamp = System.currentTimeMillis();
            String time = formatter.format(new Date());
            String fileName = "crash-" + strIndex + "-" + time + "-"
                    + timestamp + ".log";
            if (Environment.getExternalStorageState().equals(
                    Environment.MEDIA_MOUNTED)) {
                String path = mCrashDir;
                File dir = new File(path);
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                fos = new FileOutputStream(path + fileName);
                fos.write(sb.toString().getBytes());
                fos.close();
            }

            Log.e("CrashHandler", sb.toString());
            // 打包时候去掉
//            if(BuildConfig.DEBUG){
//            	Log.e("CrashHandler", ">>>>>>>>>>>>>>>>>>>>>>>>> crash <<<<<<<<<<<<<<<<<<<<<<<<<");
//            	Log.e("CrashHandler", sb.toString());
//            	Log.e("CrashHandler", ">>>>>>>>>>>>>>>>>>>>>>>>> crash <<<<<<<<<<<<<<<<<<<<<<<<<");
//            }

            return fileName;
        } catch (Exception e) {
            Log.e(TAG, "an error occured while writing file...", e);
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

}
