package com.yaxon.utils;

import com.yaxon.base.R;

public final class HotInfoUtils {

    /**
     * 根据平台指定的字符转换为对应的图标
     * @param value
     * @return
     */
    public static int convertHotAreaIcon(String value) {
        int drawableId = -1;
        if (StringUtil.isNullOrEmpty(value)) {
            return R.drawable.icon_other;
        }

        drawableId = 0;

        switch (value) {
            case "00":
                drawableId = R.drawable.icon_railway;
                break;
            case "01":
                drawableId = R.drawable.icon_bus;
                break;
            case "02":
                drawableId = R.drawable.icon_plane;
                break;
            case "03":
                drawableId = R.drawable.icon_market;
                break;
            case "04":
                drawableId = R.drawable.icon_hospital;
                break;
            case "05":
                drawableId = R.drawable.icon_school;
                break;
            case "06":
                drawableId = R.drawable.icon_park;
                break;
            case "07":
                drawableId = R.drawable.icon_government;
                break;
            case "08":
                drawableId = R.drawable.icon_garden;
                break;
            case "09":
                drawableId = R.drawable.icon_view;
                break;
            case "10":
                drawableId = R.drawable.icon_gas_station;
                break;
            case "11":
                drawableId = R.drawable.icon_library;
                break;
            case "99":
                drawableId = R.drawable.icon_other;
                break;
        }

        return drawableId;
    }
}
