package com.yaxon.telematics.service.aidl.comm;


import com.yaxon.telematics.service.aidl.comm.PhoneBookItem;
import com.yaxon.telematics.service.aidl.comm.VoipParramInfo;

interface ICommService
{
	/**
     * 
     * 关闭socket连接
     * 
     * 备注：
     * <pre>
     * </pre>
     * @param channel 通道号
     * @return true：成功；false：失败；
     * @exception    
     * @see          {@link MonitorSocketManage#connect(int, String, int)}
     * @since        V1.0.1
     */
    boolean close(int channel);
    
    /**
     * 
     * socket连接
     * 
     * 备注：
     * 1.发送连接协议到110r；
     * 2.接收110r发送的连接协议的应答；
     * 3.接收110r发送的连接结果协议
     * <pre>
     * </pre>
     * @param channel 通道，110r会分配4个socket通道，每个应用使用固定的通道
     * @param ip 中心ip地址，格式：127.0.0.1
     * @param port 中心端口
     * @return true：成功；false：失败
     * @exception    
     * @see          {@link MonitorSocketManage#close(int)}
     * @since        V1.0.1
     */
    boolean connect(int channel, String ip, int port);
    
    /**
     * 
     * 判断socket通道是否连接
     * 
     * 备注：
     * <pre>
     * </pre>
     * @param channel 通道号
     * @return true：连接；false：断开
     * @exception    
     * @see          
     * @since        V1.0.1
     */
    boolean isConnected(int channel);
    
    /**
     * 
     * 接收socket数据
     * 
     * 备注：
     * <pre>
     * </pre>
     * @param channelId 通道号
     * @return 如果有数据则返回该数据，否则返回null
     * @exception    
     * @see          {@link MonitorSocketManage#send(int, byte[], int)}
     * @since        V1.0.1
     */
    byte[] receive(int channelId); 	
	
    /**
     * 
     * 发送数据
     * 
     * 备注：
     * <pre>
     * </pre>
     * @param channel 通道号
     * @param dataBuf 待发送数据
     * @param dataLen 数据长度
     * @return 发送的字节数
     * @exception    
     * @see          {@link MonitorSocketManage#receive(int)}
     * @since        V1.0.1
     */
    int send(int channel, in byte[] dataBuf, int dataLen);
    
    /**
     * 
     * 同步电话本
     * <br>
     * 备注：
     * <pre>
     * </pre>
     * @param flowNo 
     * @return 0x01：调度屏电话本版本已经最新，无须更新；0x02：调度屏电话本版本不是最新，需要更新；
     *		   0x03：主机正忙或者其他原因，更新请求失败
     * @exception    无
     * @see          {@link MonitorSocketManage#send(int, byte[], int)}
     * @since        V1.0.1
     */
	int syncPhoneBook(int flowNo);
	
    /**
     * 
     * 发送拨打电话的请求
     * 
     * 备注：
     
     * <pre>
     * </pre>
     * @param phoneNum 电话号码
     * @return 0x01 - 表示拨号请求成功；0x02 - 表示手机正忙；0x03 - 表示SIM卡无效
     *         0x04 - 表示没有搜索到网络；0x05 - 表示被呼叫限制的号码
     * @exception    
     * @see          
     * @since        V1.0.1
     */
    int dial(String phoneNum);
    
    /**
     * 
     * 电话摘机
     * 
     * <br>
     * 备注：
     * <pre>
     * </pre>
     * @return ture：成功；false：失败
     * @exception    无  
     * @see          
     * @since        V1.0.1
     */
    boolean pickup();
    
    /**
     * 
     * 电话挂断
     * 
     * 备注：
     * <pre>
     * </pre>
     * @return true：挂断成功；false：失败
     * @exception    
     * @see          
     * @since        V1.0.1
     */
    boolean hangup();
    
    /**
     * 
     * 在通话过程中发送DTMF字符
     * 
     * 备注：
     * 如果dtmfString参数中包含多个字符，只取最后一个
     * <pre>
     * </pre>
     * @param dtmfString dtmf字符
     * @return true：成功；false：失败；
     * @exception    无
     * @see          
     * @since        V1.0.1
     */
    boolean sendDtmf(String dtmfString);
    
    /**
     * 
     * 发送短信
     * 
     * 备注：
     * <pre>
     * </pre>
     * @param phoneNum 目的手机号码
     * @param smContent 短信内容
     * @return 0x01:表示短信发送成功;
     *         0x02:表示短信发送失败
     *         0x03:发送内容长度过长
     * @exception    无
     * @see          
     * @since        V1.0.1
     */
    int sendSm(String phoneNum, String smContent);
    
    //主动请求获取110R的电话本
    List<PhoneBookItem> requestPhoneBook();
    
        /**
     * 
     * 创建HTTP通道
     * 
     * <br>
     * 备注：
     * <pre>
     * </pre>
     * @param ip 服务器IP
     * @param port 端口
     * @return 返回创建的SOCKET通道
     * @exception    
     * @see          
     * @since        V1.0
     */
     int connectHttpChannel(int channel, String ip, int port);
     
         /**
     * 
     * 销毁HTTP通道
     * 
     * <br>
     * 备注：
     * <pre>
     * </pre>
     * @param channel 通道
     * @return 返回创建的SOCKET通道
     * @exception    
     * @see          
     * @since        V1.0
     */
     void disconnectHttpChannel(int channel);
     
    
     /**
     * 
     * 请求HTTP发送
     * 
     * <br>
     * 备注：阻塞，直到获取应答
     * @param channel 通道
     * @param entityData 协议数据
     * @param ip 服务器IP
     * @param port 端口
     * @return 返回创建的SOCKET通道
     * @exception    
     * @see          
     * @since        V1.0
     */
     byte[] doHttpRequest(int channel, in byte[] entityData, String ip, int port);
         
    //电话模块当前是否忙，是否可以答电话
    //返回值：true表示忙，不可拨打电话；  false表示空闲，可以打电话
    boolean isPhoneBusy();

    //获取voip参数
    VoipParramInfo getVoipParramInfo();

    /**
     * 使能功放
     * @parram stat 0:关闭 1:开启
     **/
    boolean enableAudioAmplifier(int stat);

    /**
     * 设置VOIP参数
     * @parram domain:域名 port:端口号
     **/
    int setVoipParram(String domain, String port);
}