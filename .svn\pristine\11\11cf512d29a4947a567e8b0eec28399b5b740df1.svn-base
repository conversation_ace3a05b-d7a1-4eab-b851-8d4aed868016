/*
 * 文   件  名:  VedioExportMsg.java
 * 版          权:  厦门雅迅网络股份有限公司
 * 创   建  人:  DerekGuo，2012-10-15
 * 文件描述:  定义导出文件条件封装类，导出视频时可以给该类的属性赋值进行不同条件组合的文件筛选。
 *****************************修改记录********************************
 * 修   改  者:  
 * 修改 单号： [可选]
 * 修改描述:
 *********************************************************************  
 */
package com.yaxon.telematics.service.aidl.dvr;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 导出信息封装类
 * <AUTHOR>
 * @version   版本号，2012-10-15
 * @see       [相关类/方法]
 * @since     [产品/模块版本]
 */

public class VedioExportMsg implements Parcelable
{
	/**
	 * 文件选择方式
	 * 0x01：指定文件；
	 * 0x02：按日期；
	 * 0x03：按通道；
	 * 0x04：按日期和通道；
	 * 0x05：全部；
	 * 如果选择全部，则没有后面的字段
	 */
	int mFileSelectType = 0;
	/**
	 * 开始日期 + 结束日期
	 * YYMMDD + YYMMDD
	 */
	String mDate = "000000000000";	
	/**
	 * 通道号
	 * 长度为8的数字字符串
	 */
	String mChannelId = "00000000";	
	
	/**
	 * 视频检索文件信息封装类
	 */
	VedioFilesMsg mVedioFilesMsg = new VedioFilesMsg();
	
	public VedioExportMsg(Parcel in)
	{
		mFileSelectType = in.readInt();
		mDate = in.readString();
		mChannelId = in.readString();
		mVedioFilesMsg = in.readParcelable(VedioFilesMsg.class.getClassLoader());		
	}
	
	@Override
	public int describeContents()
	{
		// TODO Auto-generated method stub
		return 0;
	}

	/* (non-Javadoc)
	 * @see android.os.Parcelable#writeToParcel(android.os.Parcel, int)
	 */
	@Override
	public void writeToParcel(Parcel dest, int flags)
	{
		dest.writeInt(mFileSelectType);
		dest.writeString(mDate);
		dest.writeString(mChannelId);
		dest.writeParcelable(mVedioFilesMsg, flags);
		
	}
	public static final Parcelable.Creator<VedioExportMsg> CREATOR = new Parcelable.Creator<VedioExportMsg>()
	{
		@Override
		public VedioExportMsg createFromParcel(Parcel in)
		{
			return new VedioExportMsg(in);
		}

		@Override
		public VedioExportMsg[] newArray(int size)
		{
			return new VedioExportMsg[size];
		}
	};

	
	
	public VedioExportMsg() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	public VedioExportMsg(int mFileSelectType, String mDate, String mChannelId,
			VedioFilesMsg mVedioFilesMsg) {
		super();
		this.mFileSelectType = mFileSelectType;
		this.mDate = mDate;
		this.mChannelId = mChannelId;
		this.mVedioFilesMsg = mVedioFilesMsg;
	}

	public int getmFileSelectType() {
		return mFileSelectType;
	}

	public void setmFileSelectType(int mFileSelectType) {
		this.mFileSelectType = mFileSelectType;
	}

	public String getmDate() {
		return mDate;
	}

	public void setmDate(String mDate) {
		this.mDate = mDate;
	}

	public String getmChannelId() {
		return mChannelId;
	}

	public void setmChannelId(String mChannelId) {
		this.mChannelId = mChannelId;
	}

	public VedioFilesMsg getmVedioFilesMsg() {
		return mVedioFilesMsg;
	}

	public void setmVedioFilesMsg(VedioFilesMsg mVedioFilesMsg) {
		this.mVedioFilesMsg = mVedioFilesMsg;
	}
	
	
	
}
