/*
 * 文 件 名:  SensorStatus.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2013-4-24
 * 文件描述:  定义传感器状态的封装类。
 *****************************修改记录********************************
 * 
* 修 改 者:  
 * 修改单号： [可选]
 * 修改描述:
 *
 *********************************************************************  
 */
package com.yaxon.telematics.service.aidl.main;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 
 * 封装了传感器状态各个字段，包括车门、车灯、手刹等
 * 
 * <AUTHOR>
 * @version   V1.0，2013-4-24
 * @see       
 * @since     V1.1.2
 */
public class SensorStatus implements Parcelable
{
    /**
     * 前车门信号
     */
    public boolean mFrontDoor = false; 
    /**
     * 后车门信号
     */
    public boolean mBehindDoor = false; 
    /**
     * 左转向灯信号
     */
    public boolean mLeftTurnLight = false; 
    /**
     * 右转向灯信号
     */
    public boolean mRightTurnLight = false;
    /**
     * 手刹信号
     */
    public boolean mHandBreak = false; 
    /**
     * 脚刹信号
     */
    public boolean mFootBreak = false; 
    /**
     * 前大灯信号
     */
    public boolean mFrontLight = false; 
    /**
     * 后大灯信号
     */
    public boolean mBehindLight = false; 
    /**
     * ACC信号
     */
    public boolean mAcc = false;  
    /**
     * 电子喇叭信号
     */
    public boolean mLoudspeaker = false; 
    /**
     * 空重车信号
     */
    public boolean mLoad = false; 
    /**
     * 正反转信号
     */
    public boolean mReverse = false; 
    /**
     * 中控锁开锁
     */
    public boolean mCtrlUnLock = false; 
    /**
     * 中控锁关锁
     */
    public boolean mCtrllock = false; 
    /**
     * 车门信号
     */
    public boolean mVehicleDoor = false; 
    /**
     * 报警指示线
     */
    public boolean mAlarmWire = false;
    /**
     * GPS天线开路
     */
    public boolean mGpsAntennaOff = false; 
    /**
     * GPS天线短路
     */
    public boolean mGpsAntennaShort = false;
    /**
     * 主电源欠压
     */
    public boolean mPowerLack = false;
    /**
     * 车灯信号
     */
    public boolean mVehicleLight = false;
    /**
     * 远光灯信号
     */
    public boolean mFarLight = false;
    /**
     * 近光灯信号
     */
    public boolean mNearLight = false; 
    /**
     * 防劫报警信号
     */
    public boolean mRobAlarm = false;
    
    /**
     * 左车门信号
     */
    public boolean mLeftDoor = false;
    
    /**
     * 右车门信号
     */
    public boolean mRightDoor = false;
    
    /**
     * 气喇叭信号
     */
    public boolean mAirSpeaker = false;
    
    /**
     * 抢劫报警信号
     */
    public boolean mAlarm = false;
    
    /**
     * 小灯信号
     */
    public boolean mSmallLight = false;
    
    
//    //F
//    //前车门信号
//    public final static String Bit_F7="F7";
//    
//    //后车门信号
//    public final static String Bit_F6="F6";
//    
//    //左转向灯信号
//    public final static String Bit_F5="F5";
//    
//    //右转向灯信号
//    public final static String Bit_F4="F4";
//    
//    //手刹信号
//    public final static String Bit_F3="F3";
//    
//    //脚刹信号
//    public final static String Bit_F2="F2";
//    
//    //前大灯信号
//    public final static String Bit_F1="F1";    
//    
//    //后大灯信号
//    public final static String Bit_F0="F0";
//    
//    
//    //S
//    //ACC信号
//    public final static String Bit_S7="S7";
//    
//    //电子喇叭信号
//    public final static String Bit_S6="S6";
//    
//    //空重车信号
//    public final static String Bit_S5="S5";
//    
//    //正反转信号
//    public final static String Bit_S4="S4";
//    
//    //中控锁开锁
//    public final static String Bit_S3="S3";
//    
//    //中控锁关锁
//    public final static String Bit_S2="S2";
//    
//    //车门信号
//    public final static String Bit_S1="S1";
//    
//    //报警指示线
//    public final static String Bit_S0="S0";
//    
//    //T
//    //GPS天线开路
//    public final static String Bit_T7="Bit_T7";
//    
//    //GPS天线短路
//    public final static String Bit_T6="Bit_T6";
//    
//    //主电源欠压
//    public final static String Bit_T5="Bit_T5";
//    
//    //车灯信号
//    public final static String Bit_T4="Bit_T4";
//    
//    //远光灯信号
//    public final static String Bit_T3="Bit_T3";
//    
//    //近光灯信号
//    public final static String Bit_T2="Bit_T2";
//    
//    //防劫报警信号
//    public final static String Bit_T1="Bit_T1";
//    
//    
//    public final static String Bit_T0="Bit_T0";
//    
//    //R
//    //硬盘状态
//    public final static String Bit_R7="Bit_R7";
//    
//    //录像状态
//    public final static String Bit_R6="Bit_R6";
//    
//    //
//    public final static String Bit_R5="Bit_R5";
//    
//    public final static String Bit_R4="Bit_R4";
//    
//    //小灯信号
//    public final static String Bit_R3="Bit_R3";
//    
//    public final static String Bit_R2="Bit_R2";
//    
//    public final static String Bit_R1="Bit_R1";
//    
//    public final static String Bit_R0="Bit_R0";
    
  //----------------------------------------------------------------    
    private byte mByteF;
    private byte mByteS;
    private byte mByteT;
    private byte mByteR;    
    private byte mByteSS;// 座位传感器
    
    //以下为不序列化的变量
    public boolean mHDStatus=false;  //硬盘状态
    
    public boolean mRecordingStatus=false;  //录像状态
    
    public int getSensorStatus()
    {
    	int stat=(mByteF&0xff)<<24 | (mByteS&0xff)<<16 | (mByteT&0xff)<<8 | (mByteR&0xff);
    	return stat;
    }
    
    public void setSensorStatus(byte byteF,byte byteS,byte byteT,byte byteR)
    {
    	mByteF=byteF;    	    	
    	mByteS=byteS;    	    	
    	mByteT=byteT;    	    	
    	mByteR=byteR;    
    	
    	mHDStatus=((byteR & 0x80) == 0x80) ? true : false;    	
    	mRecordingStatus=((byteR & 0x40) == 0x40) ? true : false;
    	
    }
    
    public int getSeatSensorStatus()
    {
    	int stat=(mByteSS&0xff)<<24;
    	return stat;
    }
    public void setSeatSensorStatus(byte byteSS)
    {
    	mByteSS=byteSS;
    }
    
    @Override
    public int describeContents()
    {
        // TODO Auto-generated method stub
        return 0;
    }
    @Override
    public void writeToParcel(Parcel dest, int flags)
    {
        // TODO Auto-generated method stub    	    	    	
        dest.writeString(Boolean.toString(mFrontDoor));// 前车门信号               
        dest.writeString(Boolean.toString(mBehindDoor));// 后车门信号
        dest.writeString(Boolean.toString(mLeftTurnLight));// 左转向灯信号
        dest.writeString(Boolean.toString(mRightTurnLight));// 右转向灯信号
        dest.writeString(Boolean.toString(mHandBreak));// 手刹信号
        dest.writeString(Boolean.toString(mFootBreak));// 脚刹信号
        dest.writeString(Boolean.toString(mFrontLight));// 前大灯信号
        dest.writeString(Boolean.toString(mBehindLight));// 后大灯信号
        dest.writeString(Boolean.toString(mAcc));//  ACC信号
        dest.writeString(Boolean.toString(mLoudspeaker));// 电子喇叭信号
        dest.writeString(Boolean.toString(mLoad));// 空重车信号
        dest.writeString(Boolean.toString(mReverse));// 正反转信号
        dest.writeString(Boolean.toString(mCtrlUnLock));// 中控锁开锁
        dest.writeString(Boolean.toString(mCtrllock));// 中控锁关锁
        dest.writeString(Boolean.toString(mVehicleDoor));// 车门信号
        dest.writeString(Boolean.toString(mAlarmWire));// 报警指示线
        dest.writeString(Boolean.toString(mGpsAntennaOff));// GPS天线开路
        dest.writeString(Boolean.toString(mGpsAntennaShort));// GPS天线短路
        dest.writeString(Boolean.toString(mPowerLack));// 主电源欠压
        dest.writeString(Boolean.toString(mVehicleLight));// 车灯信号
        dest.writeString(Boolean.toString(mFarLight));// 远光灯信号
        dest.writeString(Boolean.toString(mNearLight));// 近光灯信号
        dest.writeString(Boolean.toString(mRobAlarm));// 防劫报警信号
        
        dest.writeString(Boolean.toString(mLeftDoor));// 左车门信号
        dest.writeString(Boolean.toString(mRightDoor));// 右车门信号
        dest.writeString(Boolean.toString(mAirSpeaker));// 气喇叭信号
        dest.writeString(Boolean.toString(mAlarm));// 抢劫报警信号
        dest.writeString(Boolean.toString(mSmallLight));// 小灯信号                    
        
        dest.writeByte(mByteF);
        dest.writeByte(mByteS);
        dest.writeByte(mByteT);
        dest.writeByte(mByteR);
        
        dest.writeByte(mByteSS);
        
    }
    
    public static final Parcelable.Creator<SensorStatus> CREATOR = new Parcelable.Creator<SensorStatus>()
    {

        @Override
        public SensorStatus createFromParcel(Parcel source)
        {
            // TODO Auto-generated method stub
            SensorStatus sensorStatus = new SensorStatus();
            
            sensorStatus.mFrontDoor = Boolean.valueOf(source.readString());// 前车门信号
            sensorStatus.mBehindDoor = Boolean.valueOf(source.readString());// 后车门信号
            sensorStatus.mLeftTurnLight = Boolean.valueOf(source.readString());// 左转向灯信号
            sensorStatus.mRightTurnLight = Boolean.valueOf(source.readString());// 右转向灯信号
            sensorStatus.mHandBreak = Boolean.valueOf(source.readString());// 手刹信号
            sensorStatus.mFootBreak = Boolean.valueOf(source.readString());// 脚刹信号
            sensorStatus.mFrontLight = Boolean.valueOf(source.readString());// 前大灯信号
            sensorStatus.mBehindLight = Boolean.valueOf(source.readString());// 后大灯信号
            sensorStatus.mAcc = Boolean.valueOf(source.readString());//  ACC信号
            sensorStatus.mLoudspeaker = Boolean.valueOf(source.readString());// 电子喇叭信号
            sensorStatus.mLoad = Boolean.valueOf(source.readString());// 空重车信号
            sensorStatus.mReverse = Boolean.valueOf(source.readString());// 正反转信号
            sensorStatus.mCtrlUnLock = Boolean.valueOf(source.readString());// 中控锁开锁
            sensorStatus.mCtrllock = Boolean.valueOf(source.readString());// 中控锁关锁
            sensorStatus.mVehicleDoor = Boolean.valueOf(source.readString());// 车门信号
            sensorStatus.mAlarmWire = Boolean.valueOf(source.readString());// 报警指示线
            sensorStatus.mGpsAntennaOff = Boolean.valueOf(source.readString());// GPS天线开路
            sensorStatus.mGpsAntennaShort = Boolean.valueOf(source.readString());// GPS天线短路
            sensorStatus.mPowerLack = Boolean.valueOf(source.readString());// 主电源欠压
            sensorStatus.mVehicleLight = Boolean.valueOf(source.readString());// 车灯信号
            sensorStatus.mFarLight = Boolean.valueOf(source.readString());// 远光灯信号
            sensorStatus.mNearLight = Boolean.valueOf(source.readString());// 近光灯信号
            sensorStatus.mRobAlarm = Boolean.valueOf(source.readString());// 防劫报警信号
            
            sensorStatus.mLeftDoor = Boolean.valueOf(source.readString());// 左车门信号
            sensorStatus.mRightDoor = Boolean.valueOf(source.readString());// 右车门信号
            sensorStatus.mAirSpeaker = Boolean.valueOf(source.readString());// 气喇叭信号
            sensorStatus.mAlarm = Boolean.valueOf(source.readString());// 抢劫报警信号
            sensorStatus.mSmallLight = Boolean.valueOf(source.readString());// 小灯信号                         
            
            //--------------------------------------------
            sensorStatus.mByteF = source.readByte();
            sensorStatus.mByteS = source.readByte();
            sensorStatus.mByteT = source.readByte();
            sensorStatus.mByteR = source.readByte();
            
            sensorStatus.mByteSS = source.readByte();
            
            sensorStatus.mHDStatus=((sensorStatus.mByteR & 0x80) == 0x80) ? true : false;    	
            sensorStatus.mRecordingStatus=((sensorStatus.mByteR & 0x40) == 0x40) ? true : false;     
            
            return sensorStatus;
        }

        @Override
        public SensorStatus[] newArray(int size)
        {
            // TODO Auto-generated method stub
            return new SensorStatus[size];
        }
        
    };
}
