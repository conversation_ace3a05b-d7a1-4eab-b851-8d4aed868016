package com.yaxon.devicemanage.aidl;

interface GPIO_Interface
{	
    /**
     * 
     * 视频AD芯片复位
     * 
     * <br>
     * 备注：
     * <pre>
     * </pre>
     * @return 0:成功；其它：失败
     * @exception    RemoteException
     * @see          
     * @since        V1.0.1
     */
	int VedioAdChipReset();
	
	/**
     * 
     * LCD电源开关
     * 
     * <br>
     * 备注：
     * <pre>
     * </pre>
     * @param iFlag: 0表示关电源，1表示开电源
     * @return 0:成功；其它：失败
     * @exception    RemoteException
     * @see          
     * @since        V1.0.1
     */
    int LCDPower(int iFlag);
    
    /**
     * 
     * 功放控制
     * 
     * <br>
     * 备注：
     * <pre>
     * </pre>
     * @param bFlag 0： 待机; 1： play
     * @return 0:成功；其它：失败
     * @exception    RemoteException
     * @see          
     * @since        V1.0.1
     */
    int AmpStandBy(int iFlag);
    
    /**
     * 
     * 打印机电源开关
     * 
     * <br>
     * 备注：
     * <pre>
     * </pre>
     * @param iFlag: 0表示关电源，1表示开电源
     * @return 0:成功；其它：失败
     * @exception    RemoteException
     * @see          
     * @since        V1.0.1
     */
    int PrintPower(int iFlag);
    
    /**
     * 
     * 按键LED灯开关控制
     * 
     * <br>
     * 备注：
     * <pre>
     * </pre>
     * @param bFlag 0： 亮；1： 灭
     * @return 0:成功；其它：失败
     * @exception    RemoteException
     * @see          
     * @since        V1.0.1
     */
    int keyLedCtl(int bFlag);
}
