<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="com.yaxon.utils.StringUtil"/>
        <variable
            name="type"
            type="Integer" />

        <variable
            name="address"
            type="String" />

        <variable
            name="count"
            type="Integer" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/h55dp">

        <ImageView
            android:id="@+id/ivTypeIcon"
            android:layout_width="@dimen/w25dp"
            android:layout_height="@dimen/h25dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        
        <com.yaxon.view.FocuseTextView
            style="@style/Yx_Textview_White_20"
            app:layout_constraintLeft_toRightOf="@id/ivTypeIcon"
            app:layout_constraintTop_toTopOf="@id/ivTypeIcon"
            app:layout_constraintBottom_toBottomOf="@id/ivTypeIcon"
            android:singleLine="true"
            android:gravity="center_vertical"
            android:scrollHorizontally="true"
            android:ellipsize="marquee"
            android:focusableInTouchMode="true"
            android:focusable="true"
            android:marqueeRepeatLimit="marquee_forever"
            android:layout_marginLeft="@dimen/w12dp"
            android:maxWidth="@dimen/w136dp"
            android:text="@{StringUtil.isNotNullOrEmpty(address) ? address : @string/hot_info_unknow_address}"/>
        
        <TextView
            style="@style/Yx_Textview_White_24"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/ivTypeIcon"
            app:layout_constraintBottom_toBottomOf="@id/ivTypeIcon"
            android:text="@{count > 999 ? @string/home_out_of_999 : String.valueOf(count)}"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>