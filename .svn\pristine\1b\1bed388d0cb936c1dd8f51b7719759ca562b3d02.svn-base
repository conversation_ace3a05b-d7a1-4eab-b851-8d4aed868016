<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:orientation="vertical"
  android:layout_width="549px"
  android:layout_height="310px"
  android:background="@drawable/dialog_bg"
  android:layout_gravity="center_vertical|center_horizontal">
  
	  <LinearLayout
	  android:orientation="horizontal"
	  android:layout_width="wrap_content"
	  android:layout_height="wrap_content"
	  android:layout_marginTop="17px"
	  android:layout_marginLeft="40px">
	  <ImageView 
	  android:layout_width="wrap_content"
	  android:layout_height="wrap_content"
	  android:src="@drawable/main_listview_btn_down_normal"
	  />
	  <TextView 
	  android:id="@+id/text_title"
	  android:layout_width="wrap_content"
	  android:layout_height="wrap_content"
	  android:layout_marginLeft="20px"
	  android:text="@string/message_notify"
	  style="@style/TitleText"
	  />
     </LinearLayout>
     
      
     
      <LinearLayout 
	  android:layout_width="fill_parent"
	  android:layout_height="fill_parent"
	  android:orientation="horizontal"
	  android:layout_weight="1"
	  android:gravity="center_vertical"
	  android:layout_marginBottom="60px"
	  android:layout_marginLeft="60px"
	  >
	   	<ImageView
	   	android:id="@+id/image_message" 
	   	android:layout_width="wrap_content"
	    android:layout_height="wrap_content"
	    android:background="@drawable/surprice"
	   	/>
	   	<TextView 
	   	android:id="@+id/text_message"
	   	android:layout_width="wrap_content"
	    android:layout_height="wrap_content"
	    android:textSize="28sp"
	    android:text="@string/app_name"
	    android:gravity="center_vertical"
	    android:textColor="@color/white"
	   	/>
    </LinearLayout>
</LinearLayout>
