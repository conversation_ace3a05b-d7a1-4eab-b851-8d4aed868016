<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
android:layout_width="fill_parent"
android:layout_height="fill_parent"
android:background="@drawable/main_background"
android:orientation="vertical"
android:id="@+id/testingwifi_layout">
<include
    android:id="@+id/wifi_title_layout"
    android:layout_width="match_parent"
    android:layout_height="@dimen/h50dp"
    layout="@layout/main_title_simple" />
<LinearLayout
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:orientation="vertical"
    android:gravity="center">
    <RadioGroup
        android:id="@+id/wifi_radioGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical">

        <!-- 正常 -->

        <RadioButton
            android:id="@+id/WifiRadioButtonNormal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/testing_common_normal"
            android:textSize="30sp" />
        <!-- 故障 -->

        <RadioButton
            android:id="@+id/WifiRadioButtonError"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/h20dp"
            android:text="@string/testing_common_error"
            android:textColor="@color/red"
            android:textSize="30sp" />
    </RadioGroup>

    <Button
        android:id="@+id/test_wifi_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/h20dp"
        android:text="@string/wifi_testing"
        android:background="@drawable/btn_bg"
        android:textColor="@color/white" />

</LinearLayout>
</LinearLayout>