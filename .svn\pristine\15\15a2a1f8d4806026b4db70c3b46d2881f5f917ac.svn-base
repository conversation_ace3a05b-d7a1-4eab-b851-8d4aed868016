package com.yaxon.telematics.service.taxi.aidl;

import android.os.Parcel;
import android.os.Parcelable;

public class YxFaceParameterInfo implements Parcelable {

    /*应答结果 0x01：成功  0x02：失败（无以下字段）*/
    public int respondCode;

    /*开关状态 0x01：开启人脸识别 0x02：关闭（无以下字段）*/
    public int openState;

    /*定时时长 0：不启用定时功能 */
    public int timer;

    /*acc on 触发1：表示开启 0：表示关闭*/
    public int accOn;

    /*表示司机登录触发条件 1：表示开启 0：表示关闭*/
    public int loginOn;

    /*定时触发 1：表示开启 0：表示关闭*/
    public int timerOn;

    public static final Creator<YxFaceParameterInfo> CREATOR = new Creator<YxFaceParameterInfo>() {
        @Override
        public YxFaceParameterInfo createFromParcel(Parcel in) {
            YxFaceParameterInfo yxFaceParameterInfo = new YxFaceParameterInfo();
            yxFaceParameterInfo.respondCode = in.readInt();
            yxFaceParameterInfo.openState = in.readInt();
            yxFaceParameterInfo.timer = in.readInt();
            yxFaceParameterInfo.accOn = in.readInt();
            yxFaceParameterInfo.loginOn = in.readInt();
            yxFaceParameterInfo.timerOn = in.readInt();
            return yxFaceParameterInfo;
        }

        @Override
        public YxFaceParameterInfo[] newArray(int size) {
            return new YxFaceParameterInfo[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(respondCode);
        dest.writeInt(openState);
        dest.writeInt(timer);
        dest.writeInt(accOn);
        dest.writeInt(loginOn);
        dest.writeInt(timerOn);
    }
}
