package com.yaxon.telematics.update;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 升级服务器信息
 *
 * <AUTHOR>
 * @see DnUpdateProtocol
 * @since V1.0.1
 */
public class UpdateServiceParam implements Parcelable {
    public int mSockType;        //0x01 TCP  0x02  UDP
    public String mUpdateIp;        //升级服务器IP
    public int mUpdatePort;      //升级服务器端口
    public int mFolderNameLen;   //升级文件夹名称长度
    public byte[] mFolderNameByte;  //升级文件夹名称字节数组
    public int mTelLen;          //本机号码长度
    public byte[] mTelName;         //本机号码

    public int getmSockType() {
        return mSockType;
    }

    public void setmSockType(int mSockType) {
        this.mSockType = mSockType;
    }

    public String getmUpdateIp() {
        return mUpdateIp;
    }

    public void setmUpdateIp(String mUpdateIp) {
        this.mUpdateIp = mUpdateIp;
    }

    public int getmUpdatePort() {
        return mUpdatePort;
    }

    public void setmUpdatePort(int mUpdatePort) {
        this.mUpdatePort = mUpdatePort;
    }

    public int getmFolderNameLen() {
        return mFolderNameLen;
    }

    public void setmFolderNameLen(int mFolderNameLen) {
        this.mFolderNameLen = mFolderNameLen;
    }

    public byte[] getmFolderNameByte() {
        return mFolderNameByte;
    }

    public void setmFolderNameByte(byte[] mFolderNameByte) {
        this.mFolderNameByte = mFolderNameByte;
    }

    public int getmTelLen() {
        return mTelLen;
    }

    public void setmTelLen(int mTelLen) {
        this.mTelLen = mTelLen;
    }

    public byte[] getmTelName() {
        return mTelName;
    }

    public void setmTelName(byte[] mTelName) {
        this.mTelName = mTelName;
    }

    @Override
    public int describeContents() {
        // TODO Auto-generated method stub
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int arg1) {
        // TODO Auto-generated method stub
        dest.writeInt(mSockType);
        dest.writeString(mUpdateIp);
        dest.writeInt(mUpdatePort);
        dest.writeInt(mFolderNameLen);
        dest.writeByteArray(mFolderNameByte);
        dest.writeInt(mTelLen);
        dest.writeByteArray(mTelName);
    }

    public static final Creator<UpdateServiceParam> CREATOR = new Parcelable.Creator<UpdateServiceParam>() {

        @Override
        public UpdateServiceParam createFromParcel(Parcel source) {
            // TODO Auto-generated method stub
            UpdateServiceParam para = new UpdateServiceParam();

            para.mSockType = source.readInt();
            para.mUpdateIp = source.readString();
            para.mUpdatePort = source.readInt();

            para.mFolderNameLen = source.readInt();
            para.mFolderNameByte = new byte[para.mFolderNameLen];
            source.readByteArray(para.mFolderNameByte);

            para.mTelLen = source.readInt();
            para.mTelName = new byte[para.mTelLen];
            source.readByteArray(para.mTelName);

            return para;
        }

        @Override
        public UpdateServiceParam[] newArray(int size) {
            // TODO Auto-generated method stub
            return new UpdateServiceParam[size];
        }

    };

}
