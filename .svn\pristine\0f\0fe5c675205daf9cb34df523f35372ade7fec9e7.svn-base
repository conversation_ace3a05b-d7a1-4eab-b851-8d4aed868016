<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    
    <TextView
        android:id="@+id/tvOrderTime"
        style="@style/Yx_Order_Item_Text_Style1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginLeft="@dimen/w28dp"
        android:text="2020/10/10 25：69"
        android:maxWidth="@dimen/h200dp"
        android:layout_marginTop="@dimen/w20dp"/>

    <View
        android:id="@+id/vDivider1"
        android:layout_width="@dimen/w1dp"
        android:layout_height="@dimen/h28dp"
        android:background="@color/gray"
        app:layout_constraintLeft_toRightOf="@id/tvOrderTime"
        app:layout_constraintTop_toTopOf="@id/tvOrderTime"
        app:layout_constraintBottom_toBottomOf="@id/tvOrderTime"
        android:layout_marginLeft="@dimen/w8dp"/>

    <TextView
        android:id="@+id/tvOrderType"
        style="@style/Yx_Order_Item_Text_Style1"
        app:layout_constraintLeft_toRightOf="@id/vDivider1"
        app:layout_constraintTop_toTopOf="@id/tvOrderTime"
        android:text="即时"
        android:maxWidth="@dimen/h60dp"
        android:layout_marginLeft="@dimen/w8dp"
        app:layout_constraintBottom_toBottomOf="@id/tvOrderTime"/>

    <TextView
        android:id="@+id/tvPhoneNo"
        style="@style/Yx_Order_Item_Text_Style1"
        app:layout_constraintLeft_toRightOf="@id/tvOrderType"
        app:layout_constraintTop_toTopOf="@id/tvOrderTime"
        android:text="1888888888"
        android:maxWidth="@dimen/h130dp"
        app:layout_constraintBottom_toBottomOf="@id/tvOrderTime"
        android:layout_marginLeft="@dimen/w8dp"/>

    <TextView
        android:id="@+id/tvOrderState"
        style="@style/Yx_Order_Item_Text_Style2"
        app:layout_constraintTop_toTopOf="@id/tvOrderTime"
        app:layout_constraintBottom_toBottomOf="@id/tvOrderTime"
        app:layout_constraintRight_toRightOf="parent"
        android:text="待执行中中"
        android:maxWidth="@dimen/h80dp"
        android:layout_marginRight="@dimen/w28dp"/>

    <TextView
        android:id="@+id/tvOrderNo"
        style="@style/Yx_Order_Item_Text_Style1"
        app:layout_constraintRight_toLeftOf="@id/tvOrderState"
        app:layout_constraintTop_toTopOf="@id/tvOrderTime"
        app:layout_constraintBottom_toBottomOf="@id/tvOrderTime"
        app:layout_constraintLeft_toRightOf="@id/tvPhoneNo"
        android:text="订单号:333333333333"
        android:maxWidth="@dimen/w180dp"
        android:layout_marginRight="@dimen/w8dp"/>
    
    <ImageView
        android:id="@+id/ivStartIcon"
        android:layout_width="@dimen/h36dp"
        android:layout_height="@dimen/w36dp"
        app:layout_constraintLeft_toLeftOf="@id/tvOrderTime"
        app:layout_constraintTop_toBottomOf="@id/tvOrderTime"
        android:src="@drawable/base_map_point_start"
        android:layout_marginTop="@dimen/w15dp"/>

    <TextView
        android:id="@+id/tvOrderStartAddr"
        style="@style/Yx_Order_Item_Text_Style2"
        app:layout_constraintLeft_toRightOf="@id/ivStartIcon"
        app:layout_constraintTop_toTopOf="@id/ivStartIcon"
        app:layout_constraintBottom_toBottomOf="@id/ivStartIcon"
        android:text="厦门市思明区"
        android:maxWidth="@dimen/w380dp"
        android:layout_marginLeft="@dimen/w10dp"/>

    <ImageView
        android:id="@+id/ivEndIcon"
        android:layout_width="@dimen/h36dp"
        android:layout_height="@dimen/w36dp"
        app:layout_constraintLeft_toLeftOf="@id/tvOrderTime"
        app:layout_constraintTop_toBottomOf="@id/ivStartIcon"
        android:src="@drawable/base_map_point_end"
        android:layout_marginTop="@dimen/w8dp"
        android:layout_marginBottom="@dimen/w28dp"/>

    <TextView
        android:id="@+id/tvOrderEndAddr"
        style="@style/Yx_Order_Item_Text_Style2"
        app:layout_constraintLeft_toRightOf="@id/ivEndIcon"
        app:layout_constraintTop_toTopOf="@id/ivEndIcon"
        app:layout_constraintBottom_toBottomOf="@id/ivEndIcon"
        android:text="厦门市思明区"
        android:maxWidth="@dimen/w380dp"
        android:layout_marginLeft="@dimen/w10dp"/>

    <Button
        android:id="@+id/btGrabOrder"
        android:layout_width="@dimen/h180dp"
        android:layout_height="@dimen/h45dp"
        android:background="@drawable/selector_grab_order"
        android:text="@string/base_titlebar_grab_order"
        android:textSize="@dimen/w24sp"
        android:textColor="@color/white"
        app:layout_constraintTop_toBottomOf="@id/tvOrderState"
        app:layout_constraintRight_toRightOf="@id/tvOrderState"
        app:layout_constraintBottom_toBottomOf="@id/ivEndIcon"/>

    <Button
        android:id="@+id/btExcuteOrder"
        android:layout_width="@dimen/w86dp"
        android:layout_height="@dimen/w45dp"
        android:background="@drawable/selector_grab_order"
        android:text="@string/dz_order_execute"
        android:textSize="@dimen/w24sp"
        android:textColor="@color/white"
        app:layout_constraintRight_toRightOf="@id/tvOrderState"
        app:layout_constraintTop_toBottomOf="@id/tvOrderState"
        app:layout_constraintBottom_toBottomOf="@id/ivEndIcon"/>

    <Button
        android:id="@+id/btCancelOrder"
        android:layout_width="@dimen/w86dp"
        android:layout_height="@dimen/w45dp"
        android:background="@drawable/selector_grab_order"
        android:text="@string/dialog_common_cancel"
        android:textSize="@dimen/w24sp"
        android:textColor="@color/white"
        app:layout_constraintRight_toLeftOf="@id/btExcuteOrder"
        app:layout_constraintTop_toTopOf="@id/btExcuteOrder"
        app:layout_constraintBottom_toBottomOf="@id/btExcuteOrder"
        android:layout_marginRight="@dimen/w8dp"/>

    <androidx.constraintlayout.widget.Group
        android:layout_width="@dimen/h120dp"
        android:layout_height="@dimen/h45dp"
        app:constraint_referenced_ids="btExcuteOrder,btCancelOrder"
        android:visibility="gone"/>

</androidx.constraintlayout.widget.ConstraintLayout>