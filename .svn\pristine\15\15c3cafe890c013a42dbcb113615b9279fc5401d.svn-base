package com.yaxon.telematics.jni;

import android.util.Log;

public class YxSystemUtil {
    private static final String TAG = "YxSystemUtil";

    static {
        try {
            System.loadLibrary("YxSystemUtil");
            Log.i(TAG, ">>>load JNI YxSystemUtil succ!");
        } catch (Exception e) {
            // TODO: handle exception
            Log.i(TAG, ">>>load JNI YxSystemUtil fail!");
        }

    }

    //获取内核版本号
    public static native String getKernelVersion();

    //设置ttyS2用于调试还是接外设
    //接外设：mode=0, 调试：mode=1
    public static native boolean setTtyS2Function(int mode);

    //设置系统的省电模式
    //返回值：返回设置完毕后，系统的用电模式
    //mode=0，正常模式; mode=1，省电模式
    public static native int setPowerMode(int mode);

    //查询触摸屏状态
    //输入值： requestCode: 
    //1:查询触摸屏      //返回值：   ：0触摸屏正常   1：触摸屏异常  
    //2：    
    public static native int getSystemStatus(int requestCode);

    //向底层发送命令,cmd:命令字; cmdCode:执行命令所需的数据
    //设置gpio:  cmd=1    cmdCode[0]~cmdCode[3]：四个字节的io号，小端模式 ;  cmdCode[4]: 设置电平的高低，0表示低电平，1表示高电平    //返回值:0成功，-1失败
    //读取gpio:  cmd=2    cmdCode[0]~cmdCode[3]：四个字节的io号，小端模式 ;  //返回值:1为高电平，0为低电平，-1表示失败
    public static native int commandControl(int cmd, byte[] cmdCode, int cmdCodeLen);

    public static native String commandControlString(int cmd, byte[] cmdCode, int cmdCodeLen);

    //设置当前视频通道
    //输入值：camera1   camera2
    //public static native int VideoChannalSwitch(String channel);

    //查询当前视频通道 返回 camera1  camera2
    public static native String VideoChannalQuery();

    //设置当前视频通道
    //输入值：0: camera1  1: camera2
    public static native int VideoChannalSwitch(int ch);

    //获取硬件型号
    public static native String getDeviceType();
}
