<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="549px"
    android:layout_height="400px"
    android:layout_gravity="center_vertical|center_horizontal"
    android:background="@drawable/dialog_bg"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40px"
        android:layout_marginTop="17px"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/main_listview_btn_down_normal" />

        <TextView
            android:id="@+id/dialog_tv_title"
            style="@style/TitleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20px"
            android:text="@string/main_hardware_wifi_add"/>
    </LinearLayout>


    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingLeft="10px"
        android:paddingRight="10px">


        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_marginLeft="@dimen/w36dp"
            android:layout_marginRight="@dimen/w36dp"
            android:background="@drawable/wifi_ripple_bg"
            android:layout_height="@dimen/h36dp">
            <TextView
                android:text="@string/main_hardware_net_name"
                android:layout_marginLeft="@dimen/w24dp"
                android:layout_marginRight="@dimen/w24dp"
                android:textColor="@color/black"
                android:gravity="center"
                android:layout_width="@dimen/w60dp"
                android:layout_height="match_parent"/>

            <View
                android:background="@drawable/divider"
                android:layout_width="@dimen/h1dp"
                android:layout_height="match_parent"/>

            <EditText
                android:id="@+id/et_net_name"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="#00000000"
                android:gravity="center|left"
                android:layout_marginLeft="@dimen/w12dp"
                android:layout_marginRight="@dimen/w12dp"
                android:textSize="@dimen/h18sp"
                android:ems="10"
                android:inputType="textPersonName" />

        </LinearLayout>

        <LinearLayout
            android:layout_marginTop="@dimen/h18dp"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_marginLeft="@dimen/w36dp"
            android:layout_marginRight="@dimen/w36dp"
            android:background="@drawable/wifi_ripple_bg"
            android:layout_height="@dimen/h36dp">

            <TextView
                android:text="安全性"
                android:layout_marginLeft="@dimen/w24dp"
                android:layout_marginRight="@dimen/w24dp"
                android:textColor="@color/black"
                android:gravity="center"
                android:layout_width="@dimen/w60dp"
                android:layout_height="match_parent"/>

            <View
                android:background="@drawable/divider"
                android:layout_width="@dimen/h1dp"
                android:layout_height="match_parent"/>

            <Spinner
                android:id="@+id/spinner"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:entries="@array/wifi_security"
                android:layout_marginLeft="@dimen/w6dp"
                android:layout_marginRight="@dimen/w6dp"
                android:spinnerMode="dropdown"
                android:background="@color/white"
                android:textColor="@color/white"
                android:textStyle="bold|italic" />

             <ImageView
                 android:layout_gravity="center"
                 android:layout_width="@dimen/w24dp"
                 android:layout_height="@dimen/h24dp"
                 android:layout_marginRight="@dimen/w6dp"
                 android:src="@drawable/icon_down"/>

        </LinearLayout>




        <LinearLayout
            android:id="@+id/ll_item_pwd"
            android:layout_marginTop="@dimen/h18dp"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_marginLeft="@dimen/w36dp"
            android:layout_marginRight="@dimen/w36dp"
            android:background="@drawable/wifi_ripple_bg"
            android:visibility="invisible"
            android:layout_height="@dimen/h36dp">
            <TextView
                android:text="@string/MachineParam_Commu_CDMA_Password"
                android:layout_marginLeft="@dimen/w24dp"
                android:layout_marginRight="@dimen/w24dp"
                android:textColor="@color/black"
                android:gravity="center"
                android:layout_width="@dimen/w60dp"
                android:layout_height="match_parent"/>

            <View
                android:background="@drawable/divider"
                android:layout_width="@dimen/h1dp"
                android:layout_height="match_parent"/>

            <EditText
                android:id="@+id/et_pwd"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="#00000000"
                android:gravity="center|left"
                android:layout_marginLeft="@dimen/w12dp"
                android:layout_marginRight="@dimen/w12dp"
                android:textSize="@dimen/h18sp"
                android:ems="10"
                android:inputType="textPassword" />
        </LinearLayout>



    </LinearLayout>

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20px"
            android:layout_marginBottom="15px"
            android:background="@drawable/btn_selector"
            android:onClick="onClick"
            android:text="@string/save"
            android:textColor="@android:color/white"
            android:textSize="20sp" />

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="1dp"/>

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_marginRight="20px"
            android:layout_marginBottom="15px"
            android:background="@drawable/btn_selector"
            android:onClick="onClick"
            android:text="@string/main_cancel"
            android:textColor="@android:color/white"
            android:textSize="20sp" />

    </LinearLayout>
</LinearLayout>
