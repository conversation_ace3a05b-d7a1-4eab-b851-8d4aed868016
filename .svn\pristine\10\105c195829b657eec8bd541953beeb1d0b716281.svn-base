<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/main_background"
    android:orientation="vertical"
    android:id="@+id/testingscreen_layout">
    <include
        android:id="@+id/title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/h50dp"
        layout="@layout/main_title_simple" />
    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:orientation="vertical"
        android:gravity="center">
        <RadioGroup
            android:id="@+id/radioGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="visible">

            <!-- 正常 -->

            <RadioButton
                android:id="@+id/RadioButtonNormal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/testing_common_normal"
                android:textSize="30sp" />
            <!-- 故障 -->

            <RadioButton
                android:id="@+id/RadioButtonError"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/h20dp"
                android:text="@string/testing_common_error"
                android:textColor="@color/red"
                android:textSize="30sp" />
        </RadioGroup>

    </LinearLayout>
</LinearLayout>