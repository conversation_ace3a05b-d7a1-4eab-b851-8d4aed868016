<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/main_background">
    <include
        android:id="@+id/title_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        layout="@layout/main_title_simple" />
    <TextView
        android:id="@+id/tvTestBtnTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/title_layout"
        android:layout_marginTop="10dp"
        android:textSize="20sp"
        android:textColor="@color/white"
        android:text="@string/testing_button_tu600_tips"/>
    <LinearLayout
        android:id="@+id/ll_left_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/title_layout"
        android:layout_toRightOf="@id/tvTestBtnTips"
        android:layout_marginTop="@dimen/h10dp"
        android:orientation="vertical"
        android:layout_centerHorizontal="true">

        <CheckBox
            android:id="@+id/testing_button_menu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/testing_button_home" />

        <CheckBox
            android:id="@+id/testing_button_up"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/testing_button_up" />

        <CheckBox
            android:id="@+id/testing_button_down"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/testing_button_down" />

        <CheckBox
            android:id="@+id/testing_button_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/testing_button_confirm" />

        <Button
            android:id="@+id/testing_button_ok"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:background="@drawable/btn_bg"
            android:text="@string/main_confirm"
            android:textColor="@color/white" />
    </LinearLayout>
    <CheckBox
        android:id="@+id/testing_button_fm_enter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/title_layout"
        android:layout_marginTop="@dimen/h10dp"
        android:layout_marginLeft="@dimen/h10dp"
        android:layout_toRightOf="@id/ll_left_button"
        android:text="@string/testing_button_fm_enter" />
    <CheckBox
        android:id="@+id/testing_button_vol_sub"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/testing_button_fm_enter"
        android:layout_alignLeft="@id/testing_button_fm_enter"
        android:text="@string/testing_button_vol_sub" />
    <CheckBox
        android:id="@+id/testing_button_vol_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/testing_button_vol_sub"
        android:layout_alignLeft="@id/testing_button_vol_sub"
        android:text="@string/testing_button_vol_add" />
</RelativeLayout>