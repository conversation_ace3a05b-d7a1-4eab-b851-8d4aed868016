/*
 * 文 件 名:  DriverInfo.java
 * 版    权:  厦门雅迅网络股份有限公司
 * 创 建 人:  lrm，2013-4-25
 * 文件描述:  定义驾驶员信息封装类。
 *****************************修改记录********************************
 *
 * 修 改 者:
 * 修改单号： [可选]
 * 修改描述:
 *
 *********************************************************************
 */
package com.yaxon.telematics.service.aidl.main;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 封装司机信息的各个字段
 *
 * <AUTHOR> 2015-2-5
 */
public class GzTaxiDriverInfo implements Parcelable {

    // 资格证号 6byte ascii 协议里要转为3bytes的BCD码
    public String mStrCertificateNum = "";

    // 车牌号码8GBK
    public String mStrCarNum = "";

    // 授权码 6byte ascii 协议里要转为3bytes的BCD码
    public String mStrAccreditCode = "";

    // 手机串号
    public String mStrIMEI = "";

    // 手机号 BCD[7]，手机号，位数不够位前面补0
    public String mStrPhoneNum = "";

    // 备注评论 GBK编码，最大不超过240字节
    public String mStrComment = "";

    /*
     * 帐号BCD[7]，资格证号或者手机号，如果刷卡登录则是卡号， 资格证是6个数字BCD，手机号码11个数字BCD，司机卡号8个数字BCD，
     * 位数不够位前面补0
     */
    public String mStrAcount = "";

    // 帐号类型：0x01资格证，0x02手机，0x03司机卡号
    public int mIntAcountType = 0x00;

    // 帐户密码 BCD[3] 6位数字
    public String mStrAcountPsw = "";

    // 登录时间BCD[6]，格式为yymmddhhmmss
    public byte[] mLoginTime = new byte[6];

    // 登出时间BCD[6]，格式为yymmddhhmmss
    public byte[] mLogoutTime = new byte[6];

    // 司机信用等级 1-10
    public int mDriverLev = 0;

    // 服务星级 1-10
    public int mDriverServeLev = 0;

    // 司机信用积分点
    public int mDriverPoint = 0;

    // 公司名称 GBK编码，最大不超过240字节
    public String mStrCompany = "";

    // 反馈结果 0x00成功，0x01失败
    public int mIntResult = 0x01;

    // 司机姓名 GBK编码，最大不超过20字节
    public String mStrDriverName = "";

    @Override
    public int describeContents() {
        // TODO Auto-generated method stub
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        // TODO Auto-generated method stub
        dest.writeString(mStrCertificateNum);
        dest.writeString(mStrCarNum);
        dest.writeString(mStrAccreditCode);
        dest.writeString(mStrIMEI);
        dest.writeString(mStrPhoneNum);

        dest.writeString(mStrComment);
        dest.writeString(mStrAcount);
        dest.writeInt(mIntAcountType);
        dest.writeString(mStrAcountPsw);
        dest.writeByteArray(mLoginTime);

        dest.writeByteArray(mLogoutTime);
        dest.writeInt(mDriverLev);
        dest.writeInt(mDriverServeLev);
        dest.writeInt(mDriverPoint);
        dest.writeString(mStrCompany);

        dest.writeInt(mIntResult);
        dest.writeString(mStrDriverName);
    }

    public static final Creator<GzTaxiDriverInfo> CREATOR = new Creator<GzTaxiDriverInfo>() {

        @Override
        public GzTaxiDriverInfo createFromParcel(Parcel source) {
            // TODO Auto-generated method stub
            GzTaxiDriverInfo driverInfo = new GzTaxiDriverInfo();

            driverInfo.mStrCertificateNum = source.readString();
            driverInfo.mStrCarNum = source.readString();
            driverInfo.mStrAccreditCode = source.readString();
            driverInfo.mStrIMEI = source.readString();
            driverInfo.mStrPhoneNum = source.readString();

            driverInfo.mStrComment = source.readString();
            driverInfo.mStrAcount = source.readString();
            driverInfo.mIntAcountType = source.readInt();
            driverInfo.mStrAcountPsw = source.readString();
            source.readByteArray(driverInfo.mLoginTime);

            source.readByteArray(driverInfo.mLogoutTime);
            driverInfo.mDriverLev = source.readInt();
            driverInfo.mDriverServeLev = source.readInt();
            driverInfo.mDriverPoint = source.readInt();
            driverInfo.mStrCompany = source.readString();

            driverInfo.mIntResult = source.readInt();
            driverInfo.mStrDriverName = source.readString();
            return driverInfo;
        }

        @Override
        public GzTaxiDriverInfo[] newArray(int size) {
            // TODO Auto-generated method stub
            return new GzTaxiDriverInfo[size];
        }

    };
}
