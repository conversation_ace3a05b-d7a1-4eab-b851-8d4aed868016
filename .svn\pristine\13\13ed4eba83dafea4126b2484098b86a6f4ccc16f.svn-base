<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/h81dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="@dimen/w64dp"
            android:layout_height="@dimen/h64dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/clLayout"
            app:layout_constraintBottom_toBottomOf="@id/clLayout"
            android:layout_marginLeft="@dimen/w26dp" />

        <View
            android:id="@+id/vDivider"
            android:layout_width="0dp"
            android:layout_height="@dimen/h2dp"
            app:layout_constraintBottom_toBottomOf="@id/clLayout"
            app:layout_constraintLeft_toRightOf="@id/ivIcon"
            app:layout_constraintRight_toRightOf="@id/clLayout"
            android:layout_marginLeft="@dimen/w20dp"
            android:layout_marginRight="@dimen/w2dp"
            android:background="@color/color_707070" />

        <TextView
            android:id="@+id/tvItemName"
            style="@style/Yx_Textview_White_24"
            app:layout_constraintLeft_toLeftOf="@id/vDivider"
            app:layout_constraintTop_toTopOf="@id/clLayout"
            app:layout_constraintBottom_toTopOf="@id/vDivider" />

        <ImageView
            android:layout_width="@dimen/w24dp"
            android:layout_height="@dimen/h24dp"
            android:layout_marginRight="@dimen/w26dp"
            app:layout_constraintTop_toTopOf="@id/tvItemName"
            app:layout_constraintBottom_toBottomOf="@id/tvItemName"
            app:layout_constraintRight_toRightOf="@id/clLayout"
            android:src="@drawable/icon_enter"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>