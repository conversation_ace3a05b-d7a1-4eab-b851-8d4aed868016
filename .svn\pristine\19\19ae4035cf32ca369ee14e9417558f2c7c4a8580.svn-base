package com.yaxon.telematics.service.aidl.main;

import com.yaxon.telematics.service.aidl.main.DriverServiceCardInfo;
import com.yaxon.telematics.service.aidl.main.GzTaxiIncomeInfo;
import com.yaxon.telematics.service.aidl.main.GzTaxiAVSet;


interface IDianZhaoService
{
    //获取中心下发的手机电召数据
    byte[] getProtocolRawData();
    
    //发送数据至中心
    boolean sendData(in byte[] data);
	 
	//获取司机刷卡信息，司机是否有刷卡在businessType体现
	DriverServiceCardInfo queryDriverServiceCardInfo();
	
	//设置终端ID，固定5字节BCD码，即10个字节ascii码。 以前的车辆注册接口中，终端ID固定为7个字节，因此需要修改才能使用
	boolean setGzTaxiTerminalId(String strTerminalId);
	
	//获取车台终端ID
	String getGzTaxiTerminalId();
	//设置乘客电话号码
	boolean  setPanssagerPhone(String mStrClientName,	String mStrClientPhone, int type) ;

   // 司机照片请求
   boolean queryDriverPictureTask(String mDriverID, String mDriverCode);
   // 设置和修改车载设备的状态，电召和非电召状态
    boolean setTerminalStatus(int mStatusType);

 //发送终端发行
    boolean sendTerminalInit();
    
        /**
    * 查询营运记录
   * @return
    */
     List<GzTaxiIncomeInfo>  queryIncomeData() ;
     
     /**
 * 导出考勤记录和营运数据
 * @param sendDataSearch
 */
   boolean sendRetrievalWorkState(in byte[] sendDataSearch);
   /**
 * 检索照片，音视频
 * @param sendData
 * @return
 */
 boolean sendRetrieval_AV(in byte[] sendData) ;
 
 /**
 * 音视频通道控制请求
 * @param mSetAvInfoArray
 * @return
 */
 List<GzTaxiAVSet>   getVideoAudeoChannel();
 
 /**
 * 音视频通道控制设置
 * @param mSetAvInfoArray
 * @return
 */
 boolean setVideoAudeoChannel(in List<GzTaxiAVSet> mSetAvInfoArray);
}
