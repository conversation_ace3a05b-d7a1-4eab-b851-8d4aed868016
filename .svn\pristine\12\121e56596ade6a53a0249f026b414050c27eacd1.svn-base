package com.yaxon.telematics.service.util;

import android.os.Handler;
import android.os.Handler.Callback;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

public class YxPrintLog implements Callback
{

	public final static String TAG="YxPrintLog";
	
	public final static int MSG_PRINT_LOG=100;
	
	private static YxPrintLog mInst;
	
	HandlerThread mHandlerThread;
	
	Handler mHandler;
	
	public static YxPrintLog getInstance()
	{
		if(mInst==null)
		{
			mInst=new YxPrintLog();
		}
		
		return mInst;
	}
	
	private YxPrintLog()
	{
		if(mHandlerThread==null)
		{
			mHandlerThread = new HandlerThread(TAG);
		}
		
		mHandlerThread.start();
		
		Looper logLooper=mHandlerThread.getLooper();
		
		while(logLooper==null)
		{
			sleep(100);
			logLooper=mHandlerThread.getLooper();
		}
		
		mHandler=new Handler(logLooper,this);		
		
	}
	
	
	private void sleep(long mili)
	{
		try
		{
			Thread.sleep(mili);
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
	}
	
	public void addPrintLogMsg(String tag, String tip, byte[] data, int datalen)
	{
		YxPrintLogObj obj = new YxPrintLogObj();
		obj.tag=tag;
		obj.tip=tip;
		obj.data=data;
		obj.datalen=datalen;
		
		Message msg=mHandler.obtainMessage(MSG_PRINT_LOG, obj);
		
		msg.sendToTarget();
		
	}
	
	private void PrintLog(YxPrintLogObj obj)
	{
		formatAndPrintLog(obj.tag,obj.tip,obj.data,obj.datalen);
	}
	
	private void formatAndPrintLog(String tag, String tip, byte[] data, int datalen)
	{
		
		if(data==null || data.length<1 || datalen<1)
		{
			return;
		}
		
      	StringBuffer sb = new StringBuffer();
    	
    	int total = Math.min(data.length, datalen);
    	int index=0;
    	
    	sb.append(tip);
    	
    	while(total>0)
    	{
    		if(total>=16)
    		{
    			sb.append(String.format("%02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x ", 
    					data[index],data[index+1],data[index+2],data[index+3],
    					data[index+4],data[index+5],data[index+6],data[index+7],
    					data[index+8],data[index+9],data[index+10],data[index+11],
    					data[index+12],data[index+13],data[index+14],data[index+15]
    							) );
    			index+=16;
    			total-=16;
    		}
    		else
    		{
    			for(int i=0;i<total;i++)
    			{
    				sb.append(String.format("%02x ", data[index]));
    				index++;
    			}
    			        			
    			total=0;
    		}
    		
    	}
    	
    	Log.i(tag, sb.toString());		
	}
	
	
	@Override
	public boolean handleMessage(Message msg) 
	{
		// TODO Auto-generated method stub
		
		switch(msg.what)
		{
		case MSG_PRINT_LOG:
			PrintLog((YxPrintLogObj) msg.obj);
			break;
			
			default:
			break;
		}
		
		return false;
	}

}
