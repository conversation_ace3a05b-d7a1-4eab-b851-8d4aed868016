package com.yaxon.version;

public class VersionConfig {
    private final String TAG = "YxVersionConfig";
    private static VersionConfig mVersionConfig;

    public static final int PROTOCOL_VER_LIANGBIAO = 1;//两标版本
    public static final int PROTOCOL_VER_UNIVERSAL = 2;//通用版

    //定义协议版本号，接收到的数据将根据协议版本号选择相应的协议版本进行解析
    public static int mProtocolVersion = PROTOCOL_VER_LIANGBIAO;//
    //如果动态换版本的话，最好还是写入配置文件，然后重启程序


//    public static final int Client_LiangBiao=0;//两标通用版
//    public static final int Client_ShenZhenTaxi=1;
//    
//    public static int mClientId=Client_ShenZhenTaxi;

    private VersionConfig() {

    }

    public synchronized static VersionConfig getInstance() {
        if (mVersionConfig == null)
            mVersionConfig = new VersionConfig();

        return mVersionConfig;
    }

    @Override
    public String toString() {
        // TODO Auto-generated method stub
        if (mProtocolVersion == VersionConfig.PROTOCOL_VER_LIANGBIAO) {
            return "######## ProtocolVersion: LiangBiao";
        } else if (mProtocolVersion == VersionConfig.PROTOCOL_VER_UNIVERSAL) {
            return "######## ProtocolVersion: Universal";
        } else {
            return "######## unknown protocol version:" + mProtocolVersion;
        }
    }


}
