package com.yaxon.base.maintain;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.SystemProperties;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.RadioButton;
import android.widget.TextView;
import android.widget.Toast;

import com.yaxon.base.R;
import com.yaxon.base.YXActivity;
import com.yaxon.base.YXApplication;
import com.yaxon.datasource.api.ControlApi;
import com.yaxon.telematics.service.taxi.aidl.YxEthernetCardInfo;
import com.yaxon.telematics.util.EthernetCfgUtils;
import com.yaxon.utils.StringUtil;
import com.yaxon.utils.TaskUtils;

public class YxEthernetAlertActivity extends YXActivity {

    private static final String TAG = "YxEthernetAlertActivity";
    private static final boolean localLOGV = false;
    private YxEthernetCardInfo curEthernetCardInfo;

    private RadioButton mConTypeDhcp, mConTypeManual, rbEthernetOn, rbEthernetOff;
    private EditText mIpaddr;
    private EditText mDns;
    private EditText mGw;
    //private EditText mMask;
    private EditText mprefix;
    private Context mContext;
    private QueryEthernetTask mQueryEthernetTask;
    private SaveEthernetCfgTask mSaveEthernetCfgTask;
    private int curState = 0;

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState, R.layout.activity_eth_alert);
        mContext = this;
        startQueryEthernetCfg();
    }

    @Override
    public void initViews() {
        super.initViews();
        ((ImageButton)findViewById(R.id.title_btn_exit)).setOnClickListener(this);
        ((ImageButton) findViewById(R.id.title_btn_menu)).setOnClickListener(this);
        ((TextView) findViewById(R.id.title_text_name)).setText(R.string.eth_config_title);
        mConTypeDhcp = (RadioButton)findViewById(R.id.dhcp_radio);
        mConTypeDhcp.requestFocus();
        mConTypeManual = (RadioButton) findViewById(R.id.manual_radio);
        rbEthernetOn = (RadioButton) findViewById(R.id.rbEthernetOn);
        rbEthernetOn.requestFocus();
        rbEthernetOff = (RadioButton) findViewById(R.id.rbEthernetOff);
        mIpaddr = (EditText) findViewById(R.id.ipaddr_edit);
        mprefix = (EditText) findViewById(R.id.prefix_edit);
        mDns = (EditText) findViewById(R.id.eth_dns_edit);
        mGw = (EditText) findViewById(R.id.eth_gw_edit);

        mConTypeDhcp.setChecked(true);
        mConTypeManual.setChecked(false);
        mIpaddr.setEnabled(false);
        // mMask.setEnabled(false);
        mprefix.setEnabled(false);
        mDns.setEnabled(false);
        mGw.setEnabled(false);

        curState = Integer.parseInt(SystemProperties.get("persist.sys.meig_ethernet", "0"));
        if (curState == 1){
            rbEthernetOn.setChecked(true);
        } else {
            rbEthernetOff.setChecked(true);
            UpdateInfo(null);
        }

        rbEthernetOn.setOnClickListener(new RadioButton.OnClickListener(){
            @Override
            public void onClick(View view) {
                EthernetCfgUtils.enableEthernet(1);

                curState = 1;
                SystemProperties.set("persist.sys.meig_ethernet", "1");
                UpdateInfo(curEthernetCardInfo);
            }
        });

        rbEthernetOff.setOnClickListener(new RadioButton.OnClickListener(){
            @Override
            public void onClick(View view) {
                EthernetCfgUtils.enableEthernet(0);

                curState = 0;
                UpdateInfo(null);
                SystemProperties.set("persist.sys.meig_ethernet", "0");
            }
        });
        mConTypeManual.setOnClickListener(new RadioButton.OnClickListener() {
            public void onClick(View v) {
                if (curState == 0){
                    UpdateInfo(null);
                    return;
                }
                mIpaddr.setEnabled(true);
                mDns.setEnabled(true);
                mGw.setEnabled(true);
                //mMask.setEnabled(true);
                mprefix.setEnabled(true);

                if (curEthernetCardInfo != null){
                    updateStaticIPCfgUI(curEthernetCardInfo);
                } else {
                    Log.w(TAG, "curIpConfiguration == null");
                }

                updateEditBg(true);
            }
        });

        mConTypeDhcp.setOnClickListener(new RadioButton.OnClickListener() {
            public void onClick(View v) {
                if (curState == 0){
                    UpdateInfo(null);
                    return;
                }
                mIpaddr.setEnabled(false);
                mDns.setEnabled(false);
                mGw.setEnabled(false);
                //mMask.setEnabled(false);
                mprefix.setEnabled(false);
                //切换dhcp/static 显示错误
                mDns.setText("");
                mGw.setText("");
                mprefix.setText("");
                mIpaddr.setText("");
                mDns.setHint(R.string.eth_none);
                mGw.setHint(R.string.eth_none);
                mprefix.setHint(R.string.eth_none);
                mIpaddr.setHint(R.string.eth_none);

                if (curEthernetCardInfo != null && curEthernetCardInfo.mIpType == 0) {
                    mIpaddr.setText(EthernetCfgUtils.getSystemIpAddr());
                    mDns.setText(EthernetCfgUtils.getSystemDnsAddr());
                    mprefix.setText(EthernetCfgUtils.getSystemMask() + "");
                    mGw.setText(EthernetCfgUtils.getSystemGateway());
                }
                updateEditBg(false);
            }
        });

        ((Button) findViewById(R.id.btn_cancel)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        ((Button) findViewById(R.id.btn_save)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (curState == 0){
                    showToast("以太网已关闭, 请先开启后设置！");
                    return;
                }
                handle_saveconf();
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mSaveEthernetCfgTask != null){
            mSaveEthernetCfgTask.cancel(true);
            mSaveEthernetCfgTask = null;
        }

        if (mQueryEthernetTask != null){
            mQueryEthernetTask.cancel(true);
            mQueryEthernetTask = null;
        }
    }

    private void updateEditBg(boolean enabe1) {
        if (enabe1) {
            mIpaddr.setBackgroundResource(R.drawable.eth_edit_enabel_bg);
            mprefix.setBackgroundResource(R.drawable.eth_edit_enabel_bg);
            mDns.setBackgroundResource(R.drawable.eth_edit_enabel_bg);
            mGw.setBackgroundResource(R.drawable.eth_edit_enabel_bg);
        } else {
            mIpaddr.setBackgroundResource(R.drawable.eth_edit_bg);
            mprefix.setBackgroundResource(R.drawable.eth_edit_bg);
            mDns.setBackgroundResource(R.drawable.eth_edit_bg);
            mGw.setBackgroundResource(R.drawable.eth_edit_bg);
        }
    }

    public void UpdateInfo(YxEthernetCardInfo ipinfo) {
//        int enable = Integer.parseInt(SystemProperties.get("persist.sys" +
//                ".ethernet", "0"));
        Log.d(TAG, "curState :" + curState);
        if (curState == 1) {
            if (ipinfo != null) {
                if (ipinfo.mIpType == 0) {
                    mConTypeDhcp.setChecked(true);
                    mIpaddr.setEnabled(false);
                    mDns.setEnabled(false);
                    mGw.setEnabled(false);
                    //mMask.setEnabled(true);
                    mprefix.setEnabled(false);

                    mIpaddr.setText(EthernetCfgUtils.getSystemIpAddr());
                    mDns.setText(EthernetCfgUtils.getSystemDnsAddr());
                    mprefix.setText(EthernetCfgUtils.getSystemMask() + "");
                    mGw.setText(EthernetCfgUtils.getSystemGateway());

                    updateEditBg(false);
                } else {
                    mConTypeManual.setChecked(true);
                    mIpaddr.setEnabled(true);
                    mDns.setEnabled(true);
                    mGw.setEnabled(true);
                    //mMask.setEnabled(true);
                    mprefix.setEnabled(true);

                    updateStaticIPCfgUI(ipinfo);
                    updateEditBg(true);
                }
            }
        } else {
            mIpaddr.setEnabled(false);
            mDns.setEnabled(false);
            mGw.setEnabled(false);
            //mMask.setEnabled(true);
            mprefix.setEnabled(false);
            mDns.setText("");
            mGw.setText("");
            mprefix.setText("");
            mIpaddr.setText("");
            mDns.setHint(R.string.eth_none);
            mGw.setHint(R.string.eth_none);
            mprefix.setHint(R.string.eth_none);
            mIpaddr.setHint(R.string.eth_none);
            updateEditBg(false);
        }
    }

    @Override
    public void onClick(View v) {
        super.onClick(v);
        switch (v.getId()){
            case R.id.title_btn_exit:
                finish();
                break;
            case R.id.title_btn_menu:
                startQueryEthernetCfg();
                break;
        }
    }

    private void updateStaticIPCfgUI(YxEthernetCardInfo config) {
        if (config == null) {
            Log.e(TAG, "err, config == null");
            return;
        }

        if (!StringUtil.isEmpty(config.mIpAddr)){
            mIpaddr.setText(config.mIpAddr);
        }

        if (!StringUtil.isEmpty(config.mSubnetMask)){
            mprefix.setText(config.mSubnetMask);
        }

        if (!StringUtil.isEmpty(config.mGatewayAddr)){
            mGw.setText(config.mGatewayAddr);
        }

        if (!StringUtil.isEmpty(config.mDnsAddr)){
            mDns.setText(config.mDnsAddr);
        }
    }

    @SuppressLint("StringFormatMatches")
    private void handle_saveconf() {
        YxEthernetCardInfo curCfg;
        if (mConTypeDhcp.isChecked()) {
            curCfg = new YxEthernetCardInfo(0, "", "", "", "");
            startSaveEthernetCfg(curCfg);
        } else {
            if (EthernetCfgUtils.isIpAddress(mIpaddr.getText().toString())
                    && EthernetCfgUtils.isIpAddress(mGw.getText().toString())
                    && EthernetCfgUtils.isDNSAddress(mDns.getText().toString())) {

                if (TextUtils.isEmpty(mIpaddr.getText().toString())
                        || TextUtils.isEmpty(mprefix.getText().toString())
                        || TextUtils.isEmpty(mGw.getText().toString())
                        || TextUtils.isEmpty(mDns.getText().toString())) {
                    Toast.makeText(mContext, R.string.eth_settings_empty,
                            Toast.LENGTH_LONG).show();
                    return;
                }

                curCfg = new YxEthernetCardInfo(1,
                        mIpaddr.getText().toString(),
                        mprefix.getText().toString(),
                        mDns.getText().toString(), mGw.getText().toString());

                int result = EthernetCfgUtils.validateIpConfigFields(curCfg);
                if (result != 0) {
                    Toast.makeText(mContext,
                            getString(R.string.eth_network_set_error, result)
                            , Toast.LENGTH_LONG).show();
                    return;
                } else {
                    startSaveEthernetCfg(curCfg);
                }
            } else {
                Toast.makeText(mContext, R.string.eth_settings_error,
                        Toast.LENGTH_LONG).show();
            }
        }
    }

    /**
     * 启动查询配置文件的任务
     */
    private void startQueryEthernetCfg() {
        if (!TaskUtils.isTaskFinsh(mQueryEthernetTask)){
            Toast.makeText(YXApplication.getInstance().getApplicationContext(), "正在查詢中", Toast.LENGTH_SHORT).show();
            return;
        }
        mQueryEthernetTask = new QueryEthernetTask();
        mQueryEthernetTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
    }

    /**
     * 启动保存配置文件的任务
     *
     * @param info
     */
    private void startSaveEthernetCfg(YxEthernetCardInfo info) {
        if (!TaskUtils.isTaskFinsh(mSaveEthernetCfgTask)) {
            Toast.makeText(YXApplication.getInstance().getApplicationContext(), "正在设置中", Toast.LENGTH_SHORT).show();
            return;
        }

        mSaveEthernetCfgTask = new SaveEthernetCfgTask();
        mSaveEthernetCfgTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, info);
    }

    private class QueryEthernetTask extends AsyncTask<Void, Void, Boolean> {
        YxEthernetCardInfo tmpInfo;
        String tip = "";

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            showToast("查询以太网配置中");
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            tmpInfo = ControlApi.getInstance().queryEthernetCardParram();
            if (tmpInfo == null) {
                tip = "未查询到有效配置参数";
                return false;
            }

            if (EthernetCfgUtils.validateIpConfigFields(tmpInfo) != 0) {
                tip = "存在无效参数";
                return false;
            }

            tip = "参数查询完成";
            return true;
        }

        @Override
        protected void onPostExecute(Boolean aBoolean) {
            super.onPostExecute(aBoolean);
            if (!StringUtil.isEmpty(tip)) {
                showToast(tip);
            }

            curEthernetCardInfo = tmpInfo;
            UpdateInfo(tmpInfo);
            mQueryEthernetTask = null;
        }
    }

    private class SaveEthernetCfgTask extends AsyncTask<YxEthernetCardInfo,
            Void, Boolean> {
        String tip = "";

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            showToast("保存以太网配置中");
        }

        @Override
        protected Boolean doInBackground(YxEthernetCardInfo... yxEthernetCardInfos) {
            if (yxEthernetCardInfos == null || yxEthernetCardInfos.length < 1) {
                tip = "配置参数出错";
                return false;
            }

            int ret =
                    ControlApi.getInstance().notifyEthernetCardParram(yxEthernetCardInfos[0]);
            if (ret == -1) {
                tip = "参数出错";
                return false;
            }

            if (ret == 0) {
                tip = "数据发送失败";
                return false;
            }

            if (ret == 2) {
                tip = "应答失败";
                return false;
            }

            tip = "参数保存成功";
            return true;
        }

        @Override
        protected void onPostExecute(Boolean aBoolean) {
            super.onPostExecute(aBoolean);
            if (!StringUtil.isEmpty(tip)) {
               showToast(tip);
            }
            if (aBoolean){
                //设置成功之后再查询一遍
                startQueryEthernetCfg();
                finish();
            }
            mSaveEthernetCfgTask = null;
        }
    }
}
