package com.yaxon.telematics.service.protocol.recorder;

import android.content.Context;
import android.content.SharedPreferences;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.media.MediaPlayer.OnCompletionListener;
import android.media.MediaPlayer.OnErrorListener;
import android.media.MediaRecorder;
import android.os.Environment;
import android.util.Log;

import com.yaxon.telematics.service.YxPlatformServiceDelegate;
import com.yaxon.telematics.service.aidl.main.AudioFileInfo;
import com.yaxon.telematics.service.protocol.monitor.MediaInfoManage;
import com.yaxon.telematics.service.util.DataTransformer;
import com.yaxon.telematics.service.util.LogUtil;
import com.yaxon.voiceswitcher.aidl.ResourceDescription;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Recorder implements OnCompletionListener, OnErrorListener {

    public static final String TAG = "Recorder";

    public static final int ERROR_STATE = -1;
    public static final int IDLE_STATE = 0;
    public static final int RECORDING_STATE = 1;
    public static final int RECORD_COMPLETED_NORMAL_STATE = 2;
    public static final int RECORD_COMPLETED_INTERUPTER_STATE = 3;
    public static final int RECORD_COMPLETED_BY_CENTER_STATE = 4;


    int mState = IDLE_STATE;

    public static final int NO_ERROR = 0;
    public static final int STORAGE_ACCESS_ERROR = 1;
    public static final int INTERNAL_PREPARE_ERROR = 2;
    public static final int INTERNAL_START_ERROR = 3;
    public static final int IN_CALL_RECORD_ERROR = 4;

    public interface OnStateChangedListener {
        public void onStateChanged(int state);

        public void onError(int error);
    }

    OnStateChangedListener mOnStateChangedListener = null;

    long mSampleStart = 0;       // time at which latest record or play operation started
    int mSampleLength = 0;      // length of current sample
    private File mSampleFile = null;

    public File getmSampleFile() {
        return mSampleFile;
    }

    MediaRecorder mRecorder = null;

    private boolean mIsUseSD = false;

    private String mSharedPreferencesName = "recordcounter";

    private String mCounterKey = "counter";

    private static Recorder mCustomerRecorder = null;

    private AudioFileInfo mAudioFileInfo = null;

    private long mStartTime = 0;
    private long mEndTime = 0;


    public long getmStartTime() {
        return mStartTime;
    }


    public void setmStartTime(long mStartTime) {
        this.mStartTime = mStartTime;
    }


    public long getmEndTime() {
        return mEndTime;
    }


    public void setmEndTime(long mEndTime) {
        this.mEndTime = mEndTime;
    }


    public AudioFileInfo getmAudioFileInfo() {
        return mAudioFileInfo;
    }


    static {
        mCustomerRecorder = new Recorder(false);
    }


    public static Recorder getInstance() {
        if (null == mCustomerRecorder) {
            mCustomerRecorder = new Recorder(false);
        }

        return mCustomerRecorder;
    }


    private Recorder(boolean useSD) {
        mIsUseSD = useSD;

    }

    public void setOnStateChangedListener(OnStateChangedListener listener) {
        mOnStateChangedListener = listener;
    }

    public int getmState() {
        return mState;
    }

    public int progress() {
        if (mState == RECORDING_STATE)
            return (int) ((System.currentTimeMillis() - mSampleStart) / 1000);
        return 0;
    }

    public int sampleLength() {
        return mSampleLength;
    }

    public File sampleFile() {
        return mSampleFile;
    }

    /**
     * Resets the recorder state. If a sample was recorded, the file is deleted.
     */
    public void delete() {
        stopRecordingForce();

        if (mSampleFile != null)
            mSampleFile.delete();

        mSampleFile = null;
        mSampleLength = 0;

        signalStateChanged(IDLE_STATE);
    }

    /**
     * Resets the recorder state. If a sample was recorded, the file is left on disk and will
     * be reused for a new recording.
     */
    public void clear() {
        stopRecordingForce();

        mSampleLength = 0;

        signalStateChanged(IDLE_STATE);
    }


    public long getCurrentGlobalID(Context context) {
        SharedPreferences sp = context.getSharedPreferences(mSharedPreferencesName, Context.MODE_PRIVATE);
        return sp.getLong(mCounterKey, 0);
    }

    public synchronized long gernalNextGlobalID(Context context) {
        SharedPreferences sp = context.getSharedPreferences(mSharedPreferencesName, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sp.edit();
        long newCounter = sp.getLong(mCounterKey, 0) + 1;
        editor.putLong(mCounterKey, newCounter);
        editor.commit();
        return newCounter;
    }

    /**
     * 根据索引号查找匹配的文件的名称（不含目录），这里没有通过数据库
     *
     * @param context 上下文
     * @param index   索引号
     * @return 匹配的文件的名称（不含目录）
     */
    public String findRecorderFileByIndex(Context context, int index) {

        File sampleDir = null;
        if (mIsUseSD) {
            sampleDir = Environment.getExternalStorageDirectory();
        } else {
            File tmp = Environment.getDataDirectory();
            //sampleDir = new File(tmp.getAbsolutePath()+"/data/"+context.getPackageName()+"/recorderfiles/");
            //sampleDir = new File(/*tmp.getAbsolutePath()+*/"/mnt/sdcard/"+context.getPackageName()+"/recorderfiles/");
            sampleDir = new File(/*tmp.getAbsolutePath()+*/"/mnt/sdcard/recorderfiles/");

        }

        if (!sampleDir.exists() || !sampleDir.isDirectory()) {
            Log.i(TAG, "sampleDir is not a directory");
        } else {
            String[] filelist = sampleDir.list();
            String patten = "*_*_*" + index + ".*";

            Pattern p = Pattern.compile(patten);
            Matcher matcher = null;

            for (int i = 0; i < filelist.length; i++) {
                File readfile = new File(sampleDir, filelist[i]);
                //System.out.println(readfile.getName());
                if (!readfile.isDirectory()) {
                    String tempName = readfile.getName();
                    matcher = p.matcher(tempName);
                    if (matcher.matches()) {
                        return tempName;
                    }

                }
            }
        }
        return null;
    }

    /**
     * 根据索引号查找匹配的文件的名称（绝对路径），这里通过数据库
     *
     * @param index 索引号
     * @return 匹配的文件的名称（绝对路径）
     */
    public static String findRecorderFileByIndex(int index) {
        return MediaInfoManage.getInstance().queryAudioFilePathByIndex(index);
    }

    public static byte[] readRecordFileDataContent(Context context, String fileName, int startPos, int dataLength) {
        LogUtil.printLog(TAG, "readRecordFileDataContent");
       /*File sampleDir = null;
          if(mIsUseSD){
      		sampleDir = Environment.getExternalStorageDirectory();       		
      	}else{
      		File tmp = Environment.getDataDirectory();       		
      		sampleDir = new File(tmp.getAbsolutePath()+"/data/"+context.getPackageName()+"/recorderfiles/");  
      	}    
	   File readFile = new File(sampleDir,fileName);*/

        File readFile = new File(fileName);
        if (!readFile.exists()) {
            LogUtil.printLog(TAG, "!readFile.exists()");
            return null;
        }

        boolean result = true;
        RandomAccessFile randomAccessFile = null;
        byte[] fileData = new byte[dataLength];
        long fileDataTotalLen = 0;

        // 从指定位置读取指定长度的数据
        try {
            randomAccessFile = new RandomAccessFile(readFile, "r");
            // 判断要读取的数据是否超出文件大小
            fileDataTotalLen = randomAccessFile.length();
            if (startPos + dataLength > fileDataTotalLen) {
                LogUtil.printLog(TAG, "startPos + dataLength > fileDataTotalLen");
                return null;
            }
            randomAccessFile.seek(startPos);
            randomAccessFile.read(fileData);
        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
            result = false;
        } finally {
            if (null != randomAccessFile) {
                try {
                    randomAccessFile.close();
                } catch (IOException e2) {
                    // TODO: handle exception
                    e2.printStackTrace();
                    result = false;
                }
            }
        }


        if (result) {
            LogUtil.printLog(TAG, "result==true");
            return fileData;
        } else {
            LogUtil.printLog(TAG, "result==false");
            return null;
        }
    }

    public int makeRecorderFileName(Context context, String extention) {

        if (mAudioFileInfo != null) {
            //生成全局唯一序号
            //mGlobalCounter = gernalNextGlobalID(context);
            int globalID = MediaInfoManage.getInstance().getAudioUsableIndex();
            mAudioFileInfo.setmIndex(globalID);

            File sampleDir = null;
            if (mIsUseSD) {
                sampleDir = Environment.getExternalStorageDirectory();
                if (!sampleDir.canWrite()) // Workaround for broken sdcard support on the device.
                    sampleDir = new File("/sdcard/sdcard");
            } else {
                File tmp = Environment.getDataDirectory();
                //sampleDir = new File(tmp.getAbsolutePath()+"/data/"+context.getPackageName()+"/recorderfiles/");
                //sampleDir = new File(tmp.getAbsolutePath()+"/mnt/sdcard/"+context.getPackageName()+"/recorderfiles/");
                sampleDir = new File("/mnt/sdcard/recorderfiles/");

                if (!sampleDir.exists()) {
                    sampleDir.mkdirs();
                }
            }

            try {
                String tmpTime = "" + mAudioFileInfo.mYear + mAudioFileInfo.mMonth + mAudioFileInfo.mDay + mAudioFileInfo.mHour + mAudioFileInfo.mMinute + mAudioFileInfo.mSecond;
                //组文件名
                String fileName = tmpTime + "_" + mAudioFileInfo.mChannel + "_" + mAudioFileInfo.mTriggerEvent + "_" + globalID + extention;
                mSampleFile = new File(sampleDir, fileName);

                if (mSampleFile.exists()) {
                    mSampleFile.delete();
                }
                mSampleFile = new File(sampleDir, fileName);
                mSampleFile.createNewFile();
                if (null == mSampleFile || !mSampleFile.exists()) {
                    setState(ERROR_STATE);
                    setError(STORAGE_ACCESS_ERROR);
                    return -1;
                }
            } catch (Exception e) {
                e.printStackTrace();
                setState(ERROR_STATE);
                setError(STORAGE_ACCESS_ERROR);
                return -1;
            }
            return 0;
        } else {
            return -1;
        }
    }


    public void stopRecordingByCenter() {
        stopRecording(RECORD_COMPLETED_BY_CENTER_STATE);
    }

    public void stopRecordingForTimeOver() {
        stopRecording(RECORD_COMPLETED_NORMAL_STATE);
    }

    public void stopRecordingForce() {
        stopRecording(RECORD_COMPLETED_INTERUPTER_STATE);
    }

    public void stopRecordingForReset() {
        stopRecording(IDLE_STATE);
    }

    public synchronized void stopRecording(int state) {
        if (mRecorder == null) {
            return;
        } else {

            mRecorder.stop();
            mRecorder.release();
            mRecorder = null;
        }

        mSampleLength = (int) ((System.currentTimeMillis() - mSampleStart) / 1000);

        mEndTime = System.currentTimeMillis();

        setState(state);

        YxPlatformServiceDelegate.getInstance().unregister(MediaRecordUtil.PACKAGE_NAME, MediaRecordUtil.APP_FLAG_NAME,
                ResourceDescription.MIC_CHANNEL_SYSTEM);

        //注销音频
//		 	try {
//		 		ComuServiceApp.mIVoiceSwitchService.unregister(MediaRecordUtil.PACKAGE_NAME, MediaRecordUtil.APP_FLAG_NAME,ResourceDescription.MIC_CHANNEL_SYSTEM);
//			} catch (RemoteException e) {
//				// TODO Auto-generated catch block
//				e.printStackTrace();
//			}

    }

    public boolean onError(MediaPlayer mp, int what, int extra) {
        stopRecordingForce();
        return true;
    }

    public void onCompletion(MediaPlayer mp) {
        stopRecordingForTimeOver();
    }

    private void setState(int state) {
        if (state == mState)
            return;

        mState = state;
        signalStateChanged(mState);
    }

    private void signalStateChanged(int state) {
        if (mOnStateChangedListener != null)
            mOnStateChangedListener.onStateChanged(state);
    }

    private void setError(int error) {
        if (mOnStateChangedListener != null)
            mOnStateChangedListener.onError(error);
    }

    public void fillOrResetOthersAttr() {
        if (mAudioFileInfo != null) {
            if (mSampleFile != null) {
                mAudioFileInfo.setmFilePath(mSampleFile.getAbsolutePath());
                mAudioFileInfo.setmSize(mSampleFile.length());
    			/*MediaPlayer mp = new  MediaPlayer();
    			try {
					mp.setDataSource(mSampleFile.getAbsolutePath());
				} catch (IllegalArgumentException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				} catch (IllegalStateException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}*/
                //mAudioFileInfo.setmDuration(mp.getDuration());
                mAudioFileInfo.setmDuration((int) (this.mEndTime - this.mStartTime) / 1000);
            }
        }
    }

    public byte[] getRecorderGeneralResponseResult() {
        byte[] sendUserData = null;

        if (mAudioFileInfo != null) {
            sendUserData = new byte[12];
            byte[] globalCounter_byte = DataTransformer.intToByteArray(mAudioFileInfo.getmIndex());
            System.arraycopy(globalCounter_byte, 0, sendUserData, 0, 4);

            byte[] length_byte = DataTransformer.intToByteArray((int) mAudioFileInfo.getmSize());
            System.arraycopy(length_byte, 0, sendUserData, 4, 4);

            byte[] duration_byte = DataTransformer.intToByteArray(mAudioFileInfo.getmDuration());
            System.arraycopy(duration_byte, 0, sendUserData, 8, 4);
        }

        return sendUserData;
    }


    public void startRecording(Context context, int outputfileformat, String extention, AudioFileInfo afi) {
        // TODO Auto-generated method stub
        if (afi != null && extention != null) {
            mAudioFileInfo = afi;
            stopRecordingForReset();
            if (makeRecorderFileName(context, extention) != 0) {
                return;
            }
            mRecorder = new MediaRecorder();

            try {
                mRecorder.setAudioSource(MediaRecorder.AudioSource.MIC);

                mRecorder.setOutputFormat(outputfileformat);
                //录音格式
                switch (afi.getmFormat()) {
                    case 0x00:
                        //设置封装格式
                        //mRecorder.setOutputFormat(MediaRecorder.OutputFormat.MP3);
                        //设置编码格式
                        mRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.DEFAULT);
                        break;
                    case 0x01:
                        //mRecorder.setOutputFormat(MediaRecorder.OutputFormat.MP3);
                        mRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.DEFAULT);
                        break;
                    case 0x02:
                        //mRecorder.setOutputFormat(MediaRecorder.OutputFormat.MP3);
                        mRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.DEFAULT);
                        break;
                    case 0x03:
                        //mRecorder.setOutputFormat(MediaRecorder.OutputFormat.MP3);
                        mRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.DEFAULT);
                        break;
                    case 0x04:
                        //mRecorder.setOutputFormat(MediaRecorder.OutputFormat.MP3);
                        mRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB);
                        break;
                    case 0x05:
                        //mRecorder.setOutputFormat(MediaRecorder.OutputFormat.MP3);
                        mRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.DEFAULT);
                        break;
                    default:
                        //mRecorder.setOutputFormat(MediaRecorder.OutputFormat.MP3);
                        mRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.DEFAULT);
                        break;
                }

                //设置采样率，4000-48000
                switch (afi.getmFrequence()) {
                    case 0x00:
                        mRecorder.setAudioSamplingRate(8192);
                        break;
                    case 0x01:
                        mRecorder.setAudioSamplingRate(8192);
                        break;
                    case 0x02:
                        mRecorder.setAudioSamplingRate(12288);
                        break;
                    case 0x03:
                        mRecorder.setAudioSamplingRate(24576);
                        break;
                    case 0x04:
                        mRecorder.setAudioSamplingRate(32768);
                        break;
                    case 0x05:
                        //需要进一步调试确认
                        mRecorder.setAudioSamplingRate(49152);
                        break;
                    default:
                        mRecorder.setAudioSamplingRate(49152);
                        break;
                }

                mRecorder.setOutputFile(mSampleFile.getAbsolutePath());
                mRecorder.prepare();
            } catch (Exception exception) {
                mRecorder.reset();
                mRecorder.release();
                mRecorder = null;
                setState(ERROR_STATE);
                setError(INTERNAL_PREPARE_ERROR);
            }
            // Handle RuntimeException if the recording couldn't start
            try {

                mRecorder.start();
            } catch (RuntimeException exception) {
                AudioManager audioMngr = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
                boolean isInCall = audioMngr.getMode() == AudioManager.MODE_IN_CALL;
                if (isInCall) {
                    setState(ERROR_STATE);
                    setError(IN_CALL_RECORD_ERROR);
                } else {
                    setState(ERROR_STATE);
                    setError(INTERNAL_START_ERROR);
                }
                mRecorder.reset();
                mRecorder.release();
                mRecorder = null;
            }
            mSampleStart = System.currentTimeMillis();
            mStartTime = System.currentTimeMillis();
            mEndTime = mStartTime;
            setState(RECORDING_STATE);
        }
    }
}
