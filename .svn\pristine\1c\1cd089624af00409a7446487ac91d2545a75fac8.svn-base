package com.yaxon.adapter;

import android.content.Context;
import android.view.View;

import androidx.databinding.DataBindingUtil;

import com.yaxon.base.R;
import com.yaxon.base.databinding.LayoutFmChannelItemBinding;
import com.yaxon.business.home.viewmodel.RadioChannelInfo;
import com.yaxon.utils.LoggerUtil;
import com.yaxon.utils.StringUtil;

import java.util.List;

public class FmChannelAdpter extends BindingAdapter<RadioChannelInfo>{
    private int isSelectedPos = 0;
    private RadioChannelInfo curChannelInfo;

    public FmChannelAdpter(Context context, List<RadioChannelInfo> listData) {
        super(context, listData);
    }


    @Override
    public int getConvertViewId() {
        return R.layout.layout_fm_channel_item;
    }

    @Override
    protected void onItemClick(View v, int position) {
        notifyPartDataChange(position);
        super.onItemClick(v, position);
    }

    @Override
    public void bindViewDatas(BindingHolder holder,
                              RadioChannelInfo radioChannelInfo, int position) {
        LayoutFmChannelItemBinding binding = DataBindingUtil.getBinding(holder.itemView);
        if (isSelectedPos == position){
            binding.setIsSelected(true);
        } else {
            binding.setIsSelected(false);
        }

        String curFreq = StringUtil.toStrAndKeepOneAfterApoint(radioChannelInfo.getChannel());
        binding.setFmChannel(curFreq);
        binding.executePendingBindings();
    }
    /**
     * 通知局部数据变化
     * @param toPos
     */
    private void notifyPartDataChange(int toPos){
        curChannelInfo = getListData().get(toPos);
        notifyItemChanged(isSelectedPos);
        isSelectedPos = toPos;
        notifyItemChanged(isSelectedPos);
    }

    /**
     * 设置被选中的频道
     * @param channel
     */
    public RadioChannelInfo setBeSelectedChannel(int channel){
        if (channel <= 0){
            LoggerUtil.e("FmChannelAdpter", "err, the channel is invalid， channel :" + channel);
            return null;
        }

        int curPos = -1;
        for (int i = 0; i < getListData().size(); i++){
            RadioChannelInfo info = getListData().get(i);
            if (channel == info.getChannel()){
                curPos = i;
                curChannelInfo = info;
                break;
            }
        }

        if (curPos >= 0){
            isSelectedPos = curPos;
            notifyPartDataChange(curPos);
            return curChannelInfo;
        }

        return null;
    }
}
