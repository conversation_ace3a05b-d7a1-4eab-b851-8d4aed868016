package com.yaxon.base.maintain;

import android.annotation.SuppressLint;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.yaxon.base.R;
import com.yaxon.base.YXActivity;
import com.yaxon.datasource.api.ControlApi;
import com.yaxon.ipc.IpcServiceManager;
import com.yaxon.telematics.service.aidl.main.NetworkStatus;
import com.yaxon.utils.Tools;

/**
 * 系统状态显示界面
 * <AUTHOR>
 * @version   版本号，2013-5-18
 * @see       [相关类/方法]
 * @since     [产品/模块版本]
 */
public class SystemStateActivity extends YXActivity {
    private static final String TAG = "SystemStateActivity";
	//状态图片
	private View view_sim;
	private View view_gps;
	private View view_tcp;
	private View view_udp;
	private View view_print;
	private View view_udisk;
	private View view_positionType;
	//文本
	private TextView text_sim;
	private TextView text_gps;
	private TextView text_tcp;
	private TextView text_udp;
	private TextView text_print;
	private TextView text_udisk;
	private TextView text_position_type_data;
	//是否正常  true--正常
	private boolean sim = false;
	private boolean gps = false;
	private boolean tcp = false;
	private boolean udp = false;
	private boolean print = false;
	private boolean udisk = false;
	//系统状态信息
	private String sim_str;
	private String tcp_str;
	private String udp_str;
	
	private String position_type_string;
	
	private RelativeLayout layout;
	private TextView text_load;
	private ControlApi mControlApi = null;
	
	private TextView text_title_bat;
	private View btn_exit;
	private View btn_updata;
	
	private LoadDataAsyncTask task;
	
	private int posiontype = -1;
	
	@SuppressLint("MissingSuperCall")
    @Override
	protected void onCreate(Bundle savedInstanceState) {
		// TODO Auto-generated method stub
		super.onCreate(savedInstanceState, R.layout.system_state);
		mControlApi = ControlApi.getInstance();
		sim_str = getString(R.string.fal_load);
		tcp_str = getString(R.string.fal_load);
		udp_str = getString(R.string.fal_load);
		
		text_title_bat = (TextView) findViewById(R.id.title_text_name);
		
		text_title_bat.setText(R.string.system_state);
		
		btn_exit = findViewById(R.id.title_btn_exit);
		btn_exit.setOnClickListener(new OnClickListener() {
			
			@Override
			public void onClick(View arg0) {
				SystemStateActivity.this.finish();
			}
		});
		
		btn_updata = findViewById(R.id.title_btn_menu);
		btn_updata.setOnClickListener(new OnClickListener() {
			
			@Override
			public void onClick(View arg0) {
				text_load.setVisibility(View.VISIBLE);
				layout.setVisibility(View.GONE);
				if(ControlApi.isUse)
				{
					task = new LoadDataAsyncTask();
					task.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, "load");
				}else
				{
					text_load.setText(R.string.load_fal);
				}
			}
		});
		initView();
		if(ControlApi.isUse)
		{
			task = new LoadDataAsyncTask();
			task.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, "load");
		}else
		{
			text_load.setText(R.string.load_fal);
		}
	}
	
	@Override
	protected void onDestroy() {
		if(task != null)
		{
			task.cancel(true);
		}
		super.onDestroy();
	}
	
	
	private void initView()
	{
		layout = (RelativeLayout) findViewById(R.id.layout_data);
		text_load = (TextView) findViewById(R.id.text_load);
		
		view_sim = findViewById(R.id.image1);
		view_gps = findViewById(R.id.image2);
		view_tcp = findViewById(R.id.image3);
		view_udp = findViewById(R.id.image4);
		view_print = findViewById(R.id.image5);
		view_udisk = findViewById(R.id.image6);
		//added by DerekGuo
		view_positionType = findViewById(R.id.image7);
		
		
		text_sim = (TextView) findViewById(R.id.text_sim_data);
		text_gps = (TextView) findViewById(R.id.text_gps_data);
		text_tcp = (TextView) findViewById(R.id.text_tcp_data);
		text_udp = (TextView) findViewById(R.id.text_udp_data);
		text_print = (TextView) findViewById(R.id.text_print_data);
		text_udisk = (TextView) findViewById(R.id.text_udisk_data);
		//added by DerekGuo
		text_position_type_data = (TextView) findViewById(R.id.text_position_type_data);
		
	}
	
	private void refreshView()
	{
		System.out.println(sim+"-------------"+sim_str);
		if(sim)
		{
			view_sim.setBackgroundResource(R.drawable.normal);
		}else
		{
			view_sim.setBackgroundResource(R.drawable.no_normal);
		}
		text_sim.setText(sim_str);
		
		if(gps)
		{
			view_gps.setBackgroundResource(R.drawable.normal);
			text_gps.setText(R.string.locate);
		}else
		{
			view_gps.setBackgroundResource(R.drawable.no_normal);
			text_gps.setText(R.string.not_locate);
		}
		
		
		if(tcp)
		{
			view_tcp.setBackgroundResource(R.drawable.normal);
			text_tcp.setText(R.string.login);
		}else
		{
			view_tcp.setBackgroundResource(R.drawable.no_normal);
			if(tcp_str.equals(""))
			{
				text_tcp.setText(R.string.not_login);
			}else
			{
				text_tcp.setText(tcp_str);
			}
			
		}
		
		if(udp)
		{
			view_udp.setBackgroundResource(R.drawable.normal);
			text_udp.setText(R.string.not_login);
		}else
		{
			view_udp.setBackgroundResource(R.drawable.no_normal);
			if(udp_str.equals(""))
			{
				text_udp.setText(R.string.not_login);
			}else
			{
				text_udp.setText(udp_str);
			}
			
		}
		
		if(print)
		{
			view_print.setBackgroundResource(R.drawable.normal);
			text_print.setText(R.string.normal_text);
		}else
		{
			view_print.setBackgroundResource(R.drawable.no_normal);
			text_print.setText(R.string.no_normal_text);
		}
		
		if(udisk)
		{
			view_udisk.setBackgroundResource(R.drawable.normal);
			text_udisk.setText(R.string.has_insert);
		}else
		{
			view_udisk.setBackgroundResource(R.drawable.no_normal);
			text_udisk.setText(R.string.no_insert);
		}
		//added by DerekGuo
		if(posiontype == 1 || posiontype == 2 || posiontype == 3){
			view_positionType.setBackgroundResource(R.drawable.normal);
		}else{
			view_positionType.setBackgroundResource(R.drawable.no_normal);
		}
		Log.i(TAG,"refreshView "+position_type_string);
		text_position_type_data.setText(position_type_string);
		
	}
	
	private void initLoadData()
	{
		NetworkStatus info = mControlApi. getNetworkStatus();
		boolean printUse = mControlApi.testPrint();
		if(info != null)
		{
			sim = getSimState(info);
			tcp = info.mTcpLogResult;
			udp = info.mUdpLogResult;
			tcp_str = "";
			udp_str = "";
		}else
		{
			sim = false;
			tcp = false;
			udp = false;
			sim_str = getString(R.string.fal_load);
			tcp_str = getString(R.string.fal_load);
			udp_str = getString(R.string.fal_load);
		}
		gps = mControlApi.getGpsState();
		print = printUse;
		udisk = Tools.getUidskStatu();
		//added by DerekGuo
		getPositionType();
	}
	
	/**
	 * added by DerekGuo
	 */
	private void getPositionType(){
		
		if(IpcServiceManager.mMainRequest != null)
		{
			try {					
				posiontype = IpcServiceManager.mMainRequest.queryPositionMode();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}	
		switch (posiontype) {
		case 1:
			position_type_string= getString(R.string.gps_type);
			break;
		case 2:
			position_type_string= getString(R.string.beidou_type);
			break;
		case 3:
			position_type_string= getString(R.string.hunhe_type);
			break;
		default:
			position_type_string= getString(R.string.unknow_type);
			break;
		}
		
	}
	
	private boolean getSimState(NetworkStatus info)
	{
		int resId;
		boolean flog = false;
		switch (info.mNetworkLinkStatus) {
		case ControlApi.SIM_NO_FIND:
			resId = R.string.sim_no_find;
			break;
		case ControlApi.SIM_FIND_NO_CONN:
			resId = R.string.sim_no_net;
			break;
		case ControlApi.SIM_CONN_NO_NET:
			resId = R.string.sim_no_link;
			break;
		default:
			resId = R.string.normal_text;
			flog = true;
			break;
		}
		sim_str = getString(resId);
		return flog;
	}
	
//added by DerekGuo				

	
	private class LoadDataAsyncTask extends AsyncTask<String, String, Boolean> {
		@Override
		protected Boolean doInBackground(String... arg0) {
			// TODO Auto-generated method stub
			System.out.println(arg0[0]);
            initLoadData();//初始化列表数据
			return true;
		}
		@Override
		protected void onPostExecute(Boolean result) {
			refreshView();
			text_load.setVisibility(View.GONE);
			layout.setVisibility(View.VISIBLE);
			super.onPostExecute(result);
		}
	}
}
