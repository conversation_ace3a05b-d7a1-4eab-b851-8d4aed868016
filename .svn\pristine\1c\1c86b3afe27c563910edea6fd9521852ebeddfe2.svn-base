<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="549px"
    android:layout_height="310px"
    android:layout_gravity="center_vertical|center_horizontal"
    android:background="@drawable/dialog_bg"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40px"
        android:layout_marginTop="17px"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/main_listview_btn_down_normal" />

        <TextView
            android:id="@+id/dialog_tv_title"
            style="@style/TitleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20px"
            tools:text="wifi 信息" />
    </LinearLayout>


    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingLeft="10px"
        android:paddingRight="10px">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/h30dp"
            android:layout_marginLeft="@dimen/w18dp"
            android:layout_marginRight="@dimen/w18dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center|start"
                android:text="状态信息"
                android:textColor="@color/white"
                android:textSize="@dimen/h13sp" />

            <TextView
                android:id="@+id/tv_connect_status"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center|end"
                tools:text="已连接"
                android:textSize="@dimen/h12sp" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/h30dp"
            android:layout_marginLeft="@dimen/w18dp"
            android:layout_marginRight="@dimen/w18dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center|start"
                android:text="信号强度"
                android:textColor="@color/white"
                android:textSize="@dimen/h13sp" />

            <TextView
                android:id="@+id/tv_level"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center|end"
                tools:text="较强"
                android:textSize="@dimen/h12sp" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/h30dp"
            android:layout_marginLeft="@dimen/w18dp"
            android:layout_marginRight="@dimen/w18dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center|start"
                android:text="连接速度"
                android:textColor="@color/white"
                android:textSize="@dimen/h13sp" />

            <TextView
                android:id="@+id/tv_speed"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center|end"
                tools:text="1.2Gpbs"
                android:textSize="@dimen/h12sp" />


        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/h30dp"
            android:layout_marginLeft="@dimen/w18dp"
            android:layout_marginRight="@dimen/w18dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center|start"
                android:text="安全性"
                android:textColor="@color/white"
                android:textSize="@dimen/h13sp" />

            <TextView
                android:id="@+id/tv_security"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center|end"
                tools:text="无"
                android:textSize="@dimen/h12sp" />


        </LinearLayout>






    </LinearLayout>

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_cancel_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20px"
            android:layout_marginBottom="15px"
            android:background="@drawable/btn_selector"
            android:onClick="onClick"
            android:text="@string/main_hardware_wlan_cancel_save"
            android:textColor="@android:color/white"
            android:textSize="20sp" />

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="1dp"/>

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_marginRight="20px"
            android:layout_marginBottom="15px"
            android:background="@drawable/btn_selector"
            android:onClick="onClick"
            android:text="@string/main_cancel"
            android:textColor="@android:color/white"
            android:textSize="20sp" />

    </LinearLayout>
</LinearLayout>
