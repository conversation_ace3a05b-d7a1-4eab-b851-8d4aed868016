# 亚讯出租车车载终端系统 (YX_Taxi_4.0_Meige)

## 项目概述

本项目是基于Android平台开发的出租车车载终端系统，专为车载环境设计，支持多种硬件平台，提供完整的出租车运营管理功能。系统采用模块化架构，支持司机管理、电召服务、营运状态管理、监管平台对接等核心业务功能。

## 技术架构

### 开发环境
- **开发语言**: 100% Java 8/11 (禁用Kotlin)
- **UI框架**: 原生XML布局 (禁用Jetpack Compose)
- **架构模式**: 严格MVP架构 (Fragment为V，Presenter持有M和V的弱引用)
- **设计规范**: Material Design 2 + 车机主题 Theme.Car
- **编译版本**: 
  - compileSdkVersion: 28
  - buildToolsVersion: 28.0.3
  - minSdkVersion: 19
  - targetSdkVersion: 19

### 核心依赖库
- **依赖注入**: Dagger 2.48 (禁用Hilt)
- **网络请求**: Retrofit 2.9 + OkHttp 4.x
- **数据存储**: Room 2.5 + EncryptedSharedPreferences
- **响应式编程**: RxJava 2.x + RxAndroid
- **图片加载**: Glide 4.9.0
- **事件总线**: LiveEventBus + EventBus
- **内存泄漏检测**: LeakCanary
- **地图服务**: 高德地图SDK

## 项目结构

### 模块组成
```
├── YX_MainInterface_120ND/     # 主界面模块 (主应用)
├── YX_CommunicatePro_120ND/    # 通信协议模块
├── YX_Control/                 # 控制服务模块
└── YX_Common/                  # 公共基础模块
```

### 主要功能模块

#### 1. 司机管理系统
- **司机登录/登出**: 支持从业资格证号+密码登录
- **司机信息管理**: 头像、基本信息、版本同步
- **人脸识别**: 司机身份验证和监控
- **司机排名**: 中心下发的司机排名信息

#### 2. 电召服务系统
- **电召接单**: 实时接收中心下发的电召订单
- **订单管理**: 订单详情查看、抢答、取消
- **预约电召**: 支持预约订单的提醒和处理
- **营运状态**: 空车、重车、停运、电召、交班、包车状态管理

#### 3. 车载终端功能
- **GPS定位**: 实时位置信息获取和上报
- **营运数据**: 里程、时间、收入等数据统计
- **硬件控制**: LED显示、摄像头、音频等设备控制
- **系统维护**: 参数设置、版本升级、故障检测

#### 4. 监管平台对接
- **数据上报**: 营运数据实时上报监管平台
- **指令接收**: 接收监管平台下发的各类指令
- **合规检查**: 载客不打表检测、人数统计等
- **视频监控**: 车内外视频录制和传输

#### 5. 多媒体功能
- **音频播放**: 音乐播放、TTS语音播报
- **视频播放**: 广告视频、监控视频回放
- **拍照功能**: 司机拍照、违规抓拍

## 硬件平台支持

系统支持多种车载硬件平台：
- **RK2928**: 瑞芯微RK2928平台
- **PX3_44**: 全志PX3 Android 4.4平台  
- **A26**: A26硬件平台
- **PX3_71**: 全志PX3 Android 7.1平台
- **MEIG**: 美格硬件平台

## 车载特性适配

### 分辨率适配
- **顶部区域**: 200dp → 180dp (适配车机高度)
- **中间区域**: 固定160dp高度，确保显示
- **剩余空间**: 使用layout_weight="1"占用剩余空间
- **布局方向**: 统一使用android:orientation="vertical"纵向布局

### 交互适配
- **多输入支持**: DPAD、旋钮、触摸三种输入方式
- **焦点管理**: 所有可点击控件android:focusable="true"且minHeight=48dp
- **语音控制**: 所有页面暴露VoiceCommandProvider接口
- **命令词管理**: 统一在res/xml/voice_commands.xml中定义

### 系统集成
- **MCU通信**: 统一走YX_McuService，遵循McuProtocol.md协议
- **音效处理**: 所有音频调用经过YX_EqService
- **权限管理**: 动态申请位置、蓝牙等权限
- **熄屏处理**: 收到ACTION_SCREEN_OFF时5秒内停止后台任务
- **OTA升级**: 支持静默升级，使用YX_MiscService验证升级包

## 开发规范

### 命名规范
- **类名**: PascalCase (如DriverPresenter)
- **常量**: UPPER_SNAKE_CASE (如MAX_RETRY_COUNT)
- **方法/变量**: camelCase (如getUserInfo)
- **资源**: 全部yx_前缀 (如yx_btn_login)

### 代码规范
- **注释**: 所有public方法必须中文javadoc
- **行内注释**: 关键业务逻辑每5行至少1行中文注释
- **线程管理**: 耗时任务使用Executors.newSingleThreadExecutor()
- **日志**: 统一使用LogUtil.d(TAG, "xxx")，TAG使用类名常量
- **安全**: SharedPreference使用EncryptedSharedPreferences

### 构建配置
- **版本管理**: 根config.gradle统一定义版本号
- **签名配置**: 支持多平台签名配置
- **模块管理**: 新增模块需在settings.gradle中声明

## 业务流程

### 司机登录流程
1. 司机输入从业资格证号和密码
2. 系统验证司机信息
3. 从中心同步最新司机数据
4. 更新本地数据库
5. 切换到主界面

### 电召处理流程
1. 接收中心下发的电召订单
2. 播放提示音并显示订单信息
3. 司机选择抢答或忽略
4. 上报抢答结果到中心
5. 更新车辆营运状态

## 部署说明

### 编译环境
- Android Studio 4.1.3
- Gradle 6.5
- JDK 8/11

### 构建命令
```bash
# 编译所有平台版本
./gradlew assembleRelease

# 编译特定平台
./gradlew assembleMeigRelease
```

### 安装部署
1. 将APK安装到车载终端
2. 配置硬件参数
3. 设置网络连接
4. 进行功能测试

## 维护说明

### 日志管理
- 系统日志存储在/sdcard/yaxon/logs/
- 支持日志导出到U盘
- 关键操作记录详细日志

### 故障排查
- 检查网络连接状态
- 查看GPS定位状态
- 验证硬件设备状态
- 分析系统日志信息

### 版本升级
- 支持在线OTA升级
- 支持U盘本地升级
- 升级前自动备份配置

## 联系信息

- **开发团队**: 亚讯科技产研一部
- **项目版本**: 4.0
- **更新日期**: 2025年
- **技术支持**: 详见项目文档和代码注释

---

*本文档基于项目当前状态生成，如有更新请及时同步修改。*
