package com.yaxon.yx_control.productesting;

import android.content.Context;
import android.content.Intent;
import android.net.wifi.WifiManager;
import android.os.AsyncTask;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;
import android.widget.Toast;

import com.yaxon.global.YXDefine;
import com.yaxon.until.PlatformUtil;
import com.yaxon.until.ToastUtils;
import com.yaxon.yx_control.YXApplication;
import com.yaxon.yx_control.taxi.kaitian.KaiTianTestWifiManager;

import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.List;

/**
 * 智能工装检测相关处理
 */
public class TestingTooling {

    private static final String TAG = TestingTooling.class.getSimpleName();

    private static final int WIFI_TESTING_CNT = 10;
    private static WifiTestTask sWifiTestTask;



    /**
     * 请求屏生产检测功能应答
     *
     * @param testType    测试类型
     * @param controlType 控制类型 0x01:启动； 0x02:停止
     */
    public static void dealProductTestRequest(Context context, int testType, int controlType) {
        int result = 0;
        switch (testType) {
            case YXDefine.TESTING_SCREEN: //屏幕测试
                Intent intent = new Intent(context, TestingScreenActivity.class);
                intent.putExtra(YXDefine.KEY_IS_AUTO_TEST, true);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                result = 0x01;

                Intent intentTest = new Intent(YXDefine.ACTION_PRODUCT_TESTING);
                intentTest.putExtra(YXDefine.KEY_PRODUCT_TEST, YXDefine.VALUE_PRODUCT_TEST_START);
                intentTest.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                if (PlatformUtil.isMeiGPlatform()) {
                    context.sendBroadcast(intentTest);
                }
                break;
            case YXDefine.TESTING_UDISK: //U盘测试
                result = testUDisk() + 1;
                break;
            case YXDefine.TESTING_WIFI:
                if (PlatformUtil.isMeiGPlatform()) {
                    final WifiManager manager = (WifiManager) YXApplication.getInstance().getApplicationContext().getSystemService(Context.WIFI_SERVICE);
                    if (!manager.isWifiEnabled()) {
                        manager.setWifiEnabled(true);
                    }
                    SystemClock.sleep(3000);
                    if (manager.getWifiState() == WifiManager.WIFI_STATE_ENABLED) {
                        ToastUtils.showShortToast("WIFI检测成功");
                        YXApplication.getInstance().mControlApi.sendProductTestResult(testType, controlType,
                                0x01 );
                    }else {
                        ToastUtils.showShortToast("WIFI检测失败");
                        YXApplication.getInstance().mControlApi.sendProductTestResult(testType, controlType,
                                0x02 );
                    }

                } else {
                    testWiFi(context, testType, controlType);
                }

                result = 1;
                break;
            case YXDefine.TESTING_TF://TF卡测试
                result = testTF() + 1;
                break;
            case YXDefine.TESTING_KEEP_SCREEN_ON://智能工装测试时禁止熄屏
                dealScreenControl(controlType);
                result = 0x01;
                break;
        }
        if (result != 0 || testType != YXDefine.TESTING_WIFI) {
            YXApplication.getInstance().mControlApi.sendProductTestResult(testType, controlType, result);
        }
    }

    private static void testWiFi(Context context, int testType, int controlType) {


        if (controlType == 0x01) {
            // 启动
//            if (sWifiTestTask == null) {
//
//            }
            sWifiTestTask = new WifiTestTask();
            sWifiTestTask.execute(WIFI_TESTING_CNT, testType, controlType);

        } else {

            // 停止
            if (sWifiTestTask != null && sWifiTestTask.getStatus() == AsyncTask.Status.RUNNING) {
                sWifiTestTask.cancel(true);
            }
            sWifiTestTask = null;

            YXApplication.getInstance().mControlApi.sendProductTestResult(testType, controlType, 0x01);
        }
    }



    /**
     * 智能工装测试中禁止息屏
     *
     * @param controlType 0x01启动　0x02停止
     */
    private static void dealScreenControl(int controlType) {
        if (controlType == 0x01) {
            YXApplication.getInstance().acquireWakeLock();
        } else if (controlType == 0x02) {
            YXApplication.getInstance().releaseWakeLock();
        }
    }

    /**
     * 测试能否在TF卡上创建文件，成功返回0 <功能说明>
     *
     * @param @return
     */
    public static int testTF() {
        int ret = 0;
        String strTestFile = "/mnt/usb_sd/temp_yaxon_test.tmp";

        RandomAccessFile raf;
        ret = 0;
        try {
            raf = new RandomAccessFile(strTestFile, "rw");
            raf.close();

            File tmpFile = new File(strTestFile);
            if (tmpFile.exists()) {
                tmpFile.delete();
            }
        } catch (Exception e) {
            ret = 1;
            e.printStackTrace();
        }

        return ret;
    }

    /**
     * 测试能否在UDISK上创建文件，成功返回0 <功能说明>
     *
     * @param @return
     */
    public static int testUDisk() {
        int ret = 0;
        String strTestFile = "/mnt/usb_storage/temp_yaxon_test.tmp";
        RandomAccessFile raf;
        ret = 0;
        try {
            raf = new RandomAccessFile(strTestFile, "rw");
            raf.close();

            File tmpFile = new File(strTestFile);
            if (tmpFile.exists()) {
                tmpFile.delete();
            }
        } catch (Exception e) {
            ToastUtils.showLongToast(e.getMessage());
            e.printStackTrace();
            ret = 1;
        }

        return ret;
    }


    /**
     * 美格版本无权限创建
     *
     * @return
     */
    public static int meigeUDiskTest() {
        String[] command = new String[]{
                "adb shell",
                "cd mnt/usb_storage",
                "mkdir temp_yaxon_test.tmp",
                "rm temp_yaxon_test.tmp"
        };
        try {
            //   CommandExecution.execCommand(command, false);


        } catch (Exception e) {
            e.printStackTrace();
            return 2;
        }

        return 1;

    }


    private static class WifiTestTask extends AsyncTask<Integer, Void, Boolean> {

        int testType;
        int controlType;

        @Override
        protected Boolean doInBackground(Integer... params) {
            Log.d(TAG, "WifiTestTask: ");
            if (null == params || params.length < 3) {
                return false;
            }

            int mCount = params[0];
            testType = params[1];
            controlType = params[2];

            boolean result = false;

            if (KaiTianTestWifiManager.getInstance(YXApplication.getInstance(), mCount).setWifiManager()) {
                while (mCount-- > 0) {
                    try {
                        Thread.sleep(2000);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (KaiTianTestWifiManager.getKaiTianWifiStatus()) {
                        result = true;
                        break;
                    }
                }
            }

            return result;
        }

        @Override
        protected void onPostExecute(Boolean result) {
            String strPrompt = "WIFI测试 ";
            if (result) {
                strPrompt += "成功";
            } else {
                strPrompt += "失败";
            }

            ToastUtils.showShortToast(strPrompt);

            YXApplication.getInstance().mControlApi.sendProductTestResult(testType, controlType,
                    result ? 0x01 : 0x02);
        }
    }


}
