<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/main_background">

    <!-- 标题栏 -->
    <LinearLayout
        android:id="@+id/title_bar"
        android:orientation="horizontal"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content">

        <include layout="@layout/main_title_simple"></include>
    </LinearLayout>

    <!-- 重新检测按钮栏 -->
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="fill_parent"
        android:layout_height="70px"
        android:background="@drawable/check_layout_bg"
        android:gravity="center_vertical"
        android:layout_marginLeft="10px"
        android:layout_marginRight="10px"
        android:padding="0px">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/check_bg"
            android:src="@drawable/check_question"
            android:layout_marginLeft="16px" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginLeft="20px"
            android:layout_marginRight="20px"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/text_cd_sensor"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:text="@string/no_beging"
                style="@style/CommonText" />

            <TextView
                android:id="@+id/text_cd_alarm"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                style="@style/CommonText"
                android:visibility="gone" />

            <ProgressBar
                android:id="@+id/progressbar"
                style="@style/YaXon_ProgressBar"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:max="100"
                android:progress="0" />
        </LinearLayout>

        <Button
            android:id="@+id/btn_check_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/detection"
            android:layout_marginRight="20px"
            style="@style/CommonText"
            android:background="@drawable/btn_checking_selector"
            android:onClick="onClick" />
    </LinearLayout>

    <!--传感器、报警选择按钮栏 -->
    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4px"
        android:layout_marginLeft="10px">

        <Button
            android:id="@+id/btn_sensor_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sensor_state"
            android:textColor="@color/white"
            android:background="@drawable/btn_tab_selector_x"
            android:onClick="onClick"
            style="@style/CommonText" />

        <Button
            android:id="@+id/btn_alarm_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/alarm_state"
            android:textColor="@color/white"
            android:layout_marginLeft="5px"
            android:background="@drawable/btn_tab_selector"
            android:onClick="onClick"
            style="@style/CommonText" />
    </LinearLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="2px"
        android:layout_marginLeft="10px"
        android:layout_marginRight="10px"
        android:background="@color/tab_sp" />

    <ListView
        android:id="@+id/list_detection"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="2px"
        android:background="@drawable/list_bg"
        android:scrollingCache="false"
        android:scrollbars="none"
        android:layout_marginLeft="10px"
        android:layout_marginRight="10px"
        android:layout_marginBottom="10px"
        android:layout_marginTop="5px" />
</LinearLayout>